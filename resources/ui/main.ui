<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <requires lib="gtk" version="4.0"/>
  <object class="GtkApplicationWindow" id="gtk_window">
    <property name="title">Clyp</property>
    <property name="default-width">500</property>
    <property name="default-height">600</property>
    <property name="icon-name">bio.murat.clyp</property>
    <property name="titlebar">
      <object class="GtkHeaderBar" id="gtk_header_bar">
        <child type="start">
          <object class="GtkMenuButton">
            <property name="can-focus">false</property>
            <property name="icon-name">open-menu-symbolic</property>
            <property name="tooltip-text" translatable="yes">Main Menu</property>
            <property name="menu-model">primary_menu</property>
          </object>
        </child>
        <child type="start">
          <object class="GtkToggleButton" id="search_toggle_button">
            <property name="can-focus">false</property>
            <property name="icon-name">system-search-symbolic</property>
            <property name="tooltip-text" translatable="yes">Search</property>
          </object>
        </child>
      </object>
    </property>
    <property name="child">
      <object class="GtkBox" id="gtk_box">
        <property name="orientation">1</property>
        <property name="halign">fill</property>
        <property name="valign">fill</property>
        <property name="vexpand">true</property>
        <property name="hexpand">true</property>
        <property name="margin-top">0</property>
        <property name="margin-bottom">0</property>
        <property name="margin-start">0</property>
        <property name="margin-end">0</property>
        <property name="spacing">0</property>
        <child>
          <object class="GtkSearchBar" id="search_bar">
            <style>
              <class name="search-bar"/>
            </style>
            <child>
              <object class="GtkSearchEntry" id="search_entry">
                <property name="can-focus">true</property>
                <property name="halign">center</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkScrolledWindow">
            <property name="halign">fill</property>
            <property name="valign">fill</property>
            <property name="vexpand">true</property>
            <property name="hexpand">true</property>
            <property name="hscrollbar-policy">never</property>
            <property name="vscrollbar-policy">automatic</property>
            <property name="min-content-height">300</property>
            <property name="max-content-height">600</property>
            <property name="propagate-natural-height">false</property>
            <property name="overlay-scrolling">true</property>
            <child>
              <object class="GtkListBox" id="clipboard_list">
                <style>
                  <class name="clipboard-list"/>
                </style>
                <property name="can-focus">true</property>
                <property name="selection-mode">1</property>
                <property name="halign">fill</property>
                <property name="valign">start</property>
                <property name="show-separators">true</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </property>
  </object>
   <object class="GtkShortcutsWindow" id="shortcuts">
    <property name="modal">1</property>
    <child>
      <object class="GtkShortcutsSection">
        <property name="section-name">shortcuts</property>
        <child>
          <object class="GtkShortcutsGroup">
            <property name="title" translatable="yes">Main Window</property>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="accelerator">&lt;CTRL&gt;f</property>
                <property name="title" translatable="yes">Toggle Search Bar</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="accelerator">a...z</property>
                <property name="title" translatable="yes">Type to Search</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="accelerator">Escape</property>
                <property name="title" translatable="yes">Hide Search Bar / Quit</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkShortcutsGroup">
            <property name="title" translatable="yes">Items List</property>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="accelerator">Return</property>
                <property name="title" translatable="yes">Copy Selected Item</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="accelerator">Delete</property>
                <property name="title" translatable="yes">Delete Selected Item</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkShortcutsGroup">
            <property name="title" translatable="yes">Search Entry</property>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="accelerator">Return</property>
                <property name="title" translatable="yes">Focus First Item on the List</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="accelerator">Escape</property>
                <property name="title" translatable="yes">Hide Search Bar</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
  <menu id="primary_menu">
    <section>
      <item>
        <attribute name="label" translatable="yes">Run on Startup</attribute>
        <attribute name="action">app.run_on_startup</attribute>
      </item>
    </section>
    <section>
      <item>
        <attribute name="label" translatable="yes">Shortcuts</attribute>
        <attribute name="action">app.shortcuts</attribute>
      </item>
      <item>
        <attribute name="label" translatable="yes">About</attribute>
        <attribute name="action">app.about</attribute>
      </item>
    </section>
  </menu>
</interface>
