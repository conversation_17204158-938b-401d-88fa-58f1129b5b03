{"app-id": "bio.murat.clyp", "runtime": "org.gnome.Platform", "runtime-version": "48", "sdk": "org.gnome.Sdk", "sdk-extensions": ["org.freedesktop.Sdk.Extension.golang"], "command": "clyp", "finish-args": ["--share=ipc", "--socket=wayland", "--socket=fallback-x11"], "build-options": {"append-path": "/usr/lib/sdk/golang/bin", "env": {"GOBIN": "/app/bin", "GOCACHE": "/run/build/clyp/go-build-cache", "GOMODCACHE": "/run/build/clyp/go-mod-cache"}}, "cleanup": ["/include", "/lib/pkgconfig", "/man", "/share/doc", "/share/gtk-doc", "/share/man", "/share/pkgconfig", "*.la", "*.a"], "modules": [{"name": "clyp", "buildsystem": "simple", "build-options": {"env": {"CGO_ENABLED": "1", "GOOS": "linux", "GOARCH": "amd64"}}, "build-commands": ["go build -mod=vendor -v -o clyp .", "install -Dm755 clyp /app/bin/clyp", "install -Dm644 data/bio.murat.clyp.metainfo.xml /app/share/metainfo/bio.murat.clyp.metainfo.xml", "install -Dm644 data/bio.murat.clyp.desktop /app/share/applications/bio.murat.clyp.desktop", "install -Dm644 data/icons/hicolor/16x16/apps/bio.murat.clyp.png /app/share/icons/hicolor/16x16/apps/bio.murat.clyp.png", "install -Dm644 data/icons/hicolor/32x32/apps/bio.murat.clyp.png /app/share/icons/hicolor/32x32/apps/bio.murat.clyp.png", "install -Dm644 data/icons/hicolor/48x48/apps/bio.murat.clyp.png /app/share/icons/hicolor/48x48/apps/bio.murat.clyp.png", "install -Dm644 data/icons/hicolor/128x128/apps/bio.murat.clyp.png /app/share/icons/hicolor/128x128/apps/bio.murat.clyp.png", "install -Dm644 data/icons/hicolor/256x256/apps/bio.murat.clyp.png /app/share/icons/hicolor/256x256/apps/bio.murat.clyp.png", "install -Dm644 data/icons/hicolor/512x512/apps/bio.murat.clyp.png /app/share/icons/hicolor/512x512/apps/bio.murat.clyp.png"], "sources": [{"type": "git", "url": "https://github.com/murat-cileli/clyp.git", "commit": "db42bc94c44ed94b30cc112903b45a2c06dc42be"}]}]}