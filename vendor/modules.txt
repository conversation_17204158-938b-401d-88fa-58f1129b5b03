# github.com/KarpelesLab/weak v0.1.1
## explicit; go 1.18
github.com/KarpelesLab/weak
# github.com/diamondburned/gotk4/pkg v0.3.1
## explicit; go 1.21.0
github.com/diamondburned/gotk4/pkg/cairo
github.com/diamondburned/gotk4/pkg/cairo/swizzle
github.com/diamondburned/gotk4/pkg/core/closure
github.com/diamondburned/gotk4/pkg/core/gbox
github.com/diamondburned/gotk4/pkg/core/gcancel
github.com/diamondburned/gotk4/pkg/core/gdebug
github.com/diamondburned/gotk4/pkg/core/gerror
github.com/diamondburned/gotk4/pkg/core/gextras
github.com/diamondburned/gotk4/pkg/core/glib
github.com/diamondburned/gotk4/pkg/core/intern
github.com/diamondburned/gotk4/pkg/core/slab
github.com/diamondburned/gotk4/pkg/gdk/v4
github.com/diamondburned/gotk4/pkg/gdkpixbuf/v2
github.com/diamondburned/gotk4/pkg/gio/v2
github.com/diamondburned/gotk4/pkg/glib/v2
github.com/diamondburned/gotk4/pkg/graphene
github.com/diamondburned/gotk4/pkg/gsk/v4
github.com/diamondburned/gotk4/pkg/gtk/v4
github.com/diamondburned/gotk4/pkg/pango
# github.com/mattn/go-sqlite3 v1.14.32
## explicit; go 1.19
github.com/mattn/go-sqlite3
# go4.org/unsafe/assume-no-moving-gc v0.0.0-20231121144256-b99613f794b6
## explicit; go 1.11
go4.org/unsafe/assume-no-moving-gc
# golang.org/x/sync v0.16.0
## explicit; go 1.23.0
golang.org/x/sync/singleflight
