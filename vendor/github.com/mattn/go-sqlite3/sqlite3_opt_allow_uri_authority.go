// Copyright (C) 2019 <PERSON><PERSON><PERSON> <<EMAIL>>.
// Copyright (C) 2018 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
//
// Use of this source code is governed by an MIT-style
// license that can be found in the LICENSE file.

//go:build sqlite_allow_uri_authority
// +build sqlite_allow_uri_authority

package sqlite3

/*
#cgo CFLAGS: -DSQLITE_ALLOW_URI_AUTHORITY
#cgo LDFLAGS: -lm
*/
import "C"
