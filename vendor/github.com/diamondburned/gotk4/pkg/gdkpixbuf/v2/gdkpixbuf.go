// Code generated by girgen. DO NOT EDIT.

package gdkpixbuf

import (
	"context"
	"fmt"
	"image"
	"image/draw"
	"runtime"
	_ "runtime/cgo"
	"strings"
	"unsafe"

	"github.com/diamondburned/gotk4/pkg/cairo/swizzle"
	"github.com/diamondburned/gotk4/pkg/core/gbox"
	"github.com/diamondburned/gotk4/pkg/core/gcancel"
	"github.com/diamondburned/gotk4/pkg/core/gerror"
	"github.com/diamondburned/gotk4/pkg/core/gextras"
	coreglib "github.com/diamondburned/gotk4/pkg/core/glib"
	"github.com/diamondburned/gotk4/pkg/gio/v2"
	"github.com/diamondburned/gotk4/pkg/glib/v2"
)

// #cgo pkg-config: gdk-pixbuf-2.0
// #cgo CFLAGS: -Wno-deprecated-declarations
// #include <stdlib.h>
// #include <gdk-pixbuf/gdk-pixbuf.h>
// #include <glib-object.h>
// extern void _gotk4_gio2_AsyncReadyCallback(GObject*, GAsyncResult*, gpointer);
// extern void _gotk4_gdkpixbuf2_PixbufLoader_ConnectSizePrepared(gpointer, gint, gint, guintptr);
// extern void _gotk4_gdkpixbuf2_PixbufLoader_ConnectClosed(gpointer, guintptr);
// extern void _gotk4_gdkpixbuf2_PixbufLoader_ConnectAreaUpdated(gpointer, gint, gint, gint, gint, guintptr);
// extern void _gotk4_gdkpixbuf2_PixbufLoader_ConnectAreaPrepared(gpointer, guintptr);
// extern void _gotk4_gdkpixbuf2_PixbufLoaderClass_size_prepared(GdkPixbufLoader*, int, int);
// extern void _gotk4_gdkpixbuf2_PixbufLoaderClass_closed(GdkPixbufLoader*);
// extern void _gotk4_gdkpixbuf2_PixbufLoaderClass_area_updated(GdkPixbufLoader*, int, int, int, int);
// extern void _gotk4_gdkpixbuf2_PixbufLoaderClass_area_prepared(GdkPixbufLoader*);
// extern void _gotk4_gdkpixbuf2_AsyncReadyCallback(GObject*, GAsyncResult*, gpointer);
// extern gboolean _gotk4_gdkpixbuf2_PixbufSaveFunc(gchar*, gsize, GError**, gpointer);
// void _gotk4_gdkpixbuf2_PixbufLoader_virtual_area_prepared(void* fnptr, GdkPixbufLoader* arg0) {
//   ((void (*)(GdkPixbufLoader*))(fnptr))(arg0);
// };
// void _gotk4_gdkpixbuf2_PixbufLoader_virtual_area_updated(void* fnptr, GdkPixbufLoader* arg0, int arg1, int arg2, int arg3, int arg4) {
//   ((void (*)(GdkPixbufLoader*, int, int, int, int))(fnptr))(arg0, arg1, arg2, arg3, arg4);
// };
// void _gotk4_gdkpixbuf2_PixbufLoader_virtual_closed(void* fnptr, GdkPixbufLoader* arg0) {
//   ((void (*)(GdkPixbufLoader*))(fnptr))(arg0);
// };
// void _gotk4_gdkpixbuf2_PixbufLoader_virtual_size_prepared(void* fnptr, GdkPixbufLoader* arg0, int arg1, int arg2) {
//   ((void (*)(GdkPixbufLoader*, int, int))(fnptr))(arg0, arg1, arg2);
// };
import "C"

// GType values.
var (
	GTypeColorspace           = coreglib.Type(C.gdk_colorspace_get_type())
	GTypeInterpType           = coreglib.Type(C.gdk_interp_type_get_type())
	GTypePixbufAlphaMode      = coreglib.Type(C.gdk_pixbuf_alpha_mode_get_type())
	GTypePixbufError          = coreglib.Type(C.gdk_pixbuf_error_get_type())
	GTypePixbufRotation       = coreglib.Type(C.gdk_pixbuf_rotation_get_type())
	GTypePixbuf               = coreglib.Type(C.gdk_pixbuf_get_type())
	GTypePixbufAnimation      = coreglib.Type(C.gdk_pixbuf_animation_get_type())
	GTypePixbufAnimationIter  = coreglib.Type(C.gdk_pixbuf_animation_iter_get_type())
	GTypePixbufLoader         = coreglib.Type(C.gdk_pixbuf_loader_get_type())
	GTypePixbufSimpleAnim     = coreglib.Type(C.gdk_pixbuf_simple_anim_get_type())
	GTypePixbufSimpleAnimIter = coreglib.Type(C.gdk_pixbuf_simple_anim_iter_get_type())
	GTypePixbufFormat         = coreglib.Type(C.gdk_pixbuf_format_get_type())
)

func init() {
	coreglib.RegisterGValueMarshalers([]coreglib.TypeMarshaler{
		coreglib.TypeMarshaler{T: GTypeColorspace, F: marshalColorspace},
		coreglib.TypeMarshaler{T: GTypeInterpType, F: marshalInterpType},
		coreglib.TypeMarshaler{T: GTypePixbufAlphaMode, F: marshalPixbufAlphaMode},
		coreglib.TypeMarshaler{T: GTypePixbufError, F: marshalPixbufError},
		coreglib.TypeMarshaler{T: GTypePixbufRotation, F: marshalPixbufRotation},
		coreglib.TypeMarshaler{T: GTypePixbuf, F: marshalPixbuf},
		coreglib.TypeMarshaler{T: GTypePixbufAnimation, F: marshalPixbufAnimation},
		coreglib.TypeMarshaler{T: GTypePixbufAnimationIter, F: marshalPixbufAnimationIter},
		coreglib.TypeMarshaler{T: GTypePixbufLoader, F: marshalPixbufLoader},
		coreglib.TypeMarshaler{T: GTypePixbufSimpleAnim, F: marshalPixbufSimpleAnim},
		coreglib.TypeMarshaler{T: GTypePixbufSimpleAnimIter, F: marshalPixbufSimpleAnimIter},
		coreglib.TypeMarshaler{T: GTypePixbufFormat, F: marshalPixbufFormat},
	})
}

// PIXBUF_MAJOR: major version of gdk-pixbuf library, that is the "0" in "0.8.2"
// for example.
const PIXBUF_MAJOR = 2

// PIXBUF_MICRO: micro version of gdk-pixbuf library, that is the "2" in "0.8.2"
// for example.
const PIXBUF_MICRO = 12

// PIXBUF_MINOR: minor version of gdk-pixbuf library, that is the "8" in "0.8.2"
// for example.
const PIXBUF_MINOR = 42

// PIXBUF_VERSION contains the full version of GdkPixbuf as a string.
//
// This is the version being compiled against; contrast with gdk_pixbuf_version.
const PIXBUF_VERSION = "2.42.12"

// Colorspace: this enumeration defines the color spaces that are supported by
// the gdk-pixbuf library.
//
// Currently only RGB is supported.
type Colorspace C.gint

const (
	// ColorspaceRGB indicates a red/green/blue additive color space.
	ColorspaceRGB Colorspace = iota
)

func marshalColorspace(p uintptr) (interface{}, error) {
	return Colorspace(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for Colorspace.
func (c Colorspace) String() string {
	switch c {
	case ColorspaceRGB:
		return "RGB"
	default:
		return fmt.Sprintf("Colorspace(%d)", c)
	}
}

// InterpType: interpolation modes for scaling functions.
//
// The GDK_INTERP_NEAREST mode is the fastest scaling method, but has horrible
// quality when scaling down; GDK_INTERP_BILINEAR is the best choice if you
// aren't sure what to choose, it has a good speed/quality balance.
//
// **Note**: Cubic filtering is missing from the list; hyperbolic interpolation
// is just as fast and results in higher quality.
type InterpType C.gint

const (
	// InterpNearest: nearest neighbor sampling; this is the fastest and lowest
	// quality mode. Quality is normally unacceptable when scaling down, but may
	// be OK when scaling up.
	InterpNearest InterpType = iota
	// InterpTiles: this is an accurate simulation of the PostScript image
	// operator without any interpolation enabled. Each pixel is rendered as a
	// tiny parallelogram of solid color, the edges of which are implemented
	// with antialiasing. It resembles nearest neighbor for enlargement,
	// and bilinear for reduction.
	InterpTiles
	// InterpBilinear: best quality/speed balance; use this mode by default.
	// Bilinear interpolation. For enlargement, it is equivalent to
	// point-sampling the ideal bilinear-interpolated image. For reduction,
	// it is equivalent to laying down small tiles and integrating over the
	// coverage area.
	InterpBilinear
	// InterpHyper: this is the slowest and highest quality reconstruction
	// function. It is derived from the hyperbolic filters in Wolberg's "Digital
	// Image Warping", and is formally defined as the hyperbolic-filter sampling
	// the ideal hyperbolic-filter interpolated image (the filter is designed to
	// be idempotent for 1:1 pixel mapping). **Deprecated**: this interpolation
	// filter is deprecated, as in reality it has a lower quality than the
	// GDK_INTERP_BILINEAR filter (Since: 2.38).
	InterpHyper
)

func marshalInterpType(p uintptr) (interface{}, error) {
	return InterpType(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for InterpType.
func (i InterpType) String() string {
	switch i {
	case InterpNearest:
		return "Nearest"
	case InterpTiles:
		return "Tiles"
	case InterpBilinear:
		return "Bilinear"
	case InterpHyper:
		return "Hyper"
	default:
		return fmt.Sprintf("InterpType(%d)", i)
	}
}

// PixbufAlphaMode: control the alpha channel for drawables.
//
// These values can be passed to gdk_pixbuf_xlib_render_to_drawable_alpha()
// in gdk-pixbuf-xlib to control how the alpha channel of an image should be
// handled.
//
// This function can create a bilevel clipping mask (black and white) and use it
// while painting the image.
//
// In the future, when the X Window System gets an alpha channel extension,
// it will be possible to do full alpha compositing onto arbitrary drawables.
// For now both cases fall back to a bilevel clipping mask.
//
// Deprecated: There is no user of GdkPixbufAlphaMode in GdkPixbuf, and the Xlib
// utility functions have been split out to their own library, gdk-pixbuf-xlib.
type PixbufAlphaMode C.gint

const (
	// PixbufAlphaBilevel: bilevel clipping mask (black and white) will be
	// created and used to draw the image. Pixels below 0.5 opacity will be
	// considered fully transparent, and all others will be considered fully
	// opaque.
	PixbufAlphaBilevel PixbufAlphaMode = iota
	// PixbufAlphaFull: for now falls back to K_PIXBUF_ALPHA_BILEVEL. In the
	// future it will do full alpha compositing.
	PixbufAlphaFull
)

func marshalPixbufAlphaMode(p uintptr) (interface{}, error) {
	return PixbufAlphaMode(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for PixbufAlphaMode.
func (p PixbufAlphaMode) String() string {
	switch p {
	case PixbufAlphaBilevel:
		return "Bilevel"
	case PixbufAlphaFull:
		return "Full"
	default:
		return fmt.Sprintf("PixbufAlphaMode(%d)", p)
	}
}

// PixbufError: error code in the GDK_PIXBUF_ERROR domain.
//
// Many gdk-pixbuf operations can cause errors in this domain, or in the
// G_FILE_ERROR domain.
type PixbufError C.gint

const (
	// PixbufErrorCorruptImage: image file was broken somehow.
	PixbufErrorCorruptImage PixbufError = iota
	// PixbufErrorInsufficientMemory: not enough memory.
	PixbufErrorInsufficientMemory
	// PixbufErrorBadOption: bad option was passed to a pixbuf save module.
	PixbufErrorBadOption
	// PixbufErrorUnknownType: unknown image type.
	PixbufErrorUnknownType
	// PixbufErrorUnsupportedOperation: don't know how to perform the given
	// operation on the type of image at hand.
	PixbufErrorUnsupportedOperation
	// PixbufErrorFailed: generic failure code, something went wrong.
	PixbufErrorFailed
	// PixbufErrorIncompleteAnimation: only part of the animation was loaded.
	PixbufErrorIncompleteAnimation
)

func marshalPixbufError(p uintptr) (interface{}, error) {
	return PixbufError(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for PixbufError.
func (p PixbufError) String() string {
	switch p {
	case PixbufErrorCorruptImage:
		return "CorruptImage"
	case PixbufErrorInsufficientMemory:
		return "InsufficientMemory"
	case PixbufErrorBadOption:
		return "BadOption"
	case PixbufErrorUnknownType:
		return "UnknownType"
	case PixbufErrorUnsupportedOperation:
		return "UnsupportedOperation"
	case PixbufErrorFailed:
		return "Failed"
	case PixbufErrorIncompleteAnimation:
		return "IncompleteAnimation"
	default:
		return fmt.Sprintf("PixbufError(%d)", p)
	}
}

func PixbufErrorQuark() glib.Quark {
	var _cret C.GQuark // in

	_cret = C.gdk_pixbuf_error_quark()

	var _quark glib.Quark // out

	_quark = glib.Quark(_cret)

	return _quark
}

// PixbufRotation: possible rotations which can be passed to
// gdk_pixbuf_rotate_simple().
//
// To make them easier to use, their numerical values are the actual degrees.
type PixbufRotation C.gint

const (
	// PixbufRotateNone: no rotation.
	PixbufRotateNone PixbufRotation = 0
	// PixbufRotateCounterclockwise: rotate by 90 degrees.
	PixbufRotateCounterclockwise PixbufRotation = 90
	// PixbufRotateUpsidedown: rotate by 180 degrees.
	PixbufRotateUpsidedown PixbufRotation = 180
	// PixbufRotateClockwise: rotate by 270 degrees.
	PixbufRotateClockwise PixbufRotation = 270
)

func marshalPixbufRotation(p uintptr) (interface{}, error) {
	return PixbufRotation(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for PixbufRotation.
func (p PixbufRotation) String() string {
	switch p {
	case PixbufRotateNone:
		return "None"
	case PixbufRotateCounterclockwise:
		return "Counterclockwise"
	case PixbufRotateUpsidedown:
		return "Upsidedown"
	case PixbufRotateClockwise:
		return "Clockwise"
	default:
		return fmt.Sprintf("PixbufRotation(%d)", p)
	}
}

// PixbufFormatFlags flags which allow a module to specify further details about
// the supported operations.
type PixbufFormatFlags C.guint

const (
	// PixbufFormatWritable: module can write out images in the format.
	PixbufFormatWritable PixbufFormatFlags = 0b1
	// PixbufFormatScalable: image format is scalable.
	PixbufFormatScalable PixbufFormatFlags = 0b10
	// PixbufFormatThreadsafe: module is threadsafe. gdk-pixbuf ignores modules
	// that are not marked as threadsafe. (Since 2.28).
	PixbufFormatThreadsafe PixbufFormatFlags = 0b100
)

// String returns the names in string for PixbufFormatFlags.
func (p PixbufFormatFlags) String() string {
	if p == 0 {
		return "PixbufFormatFlags(0)"
	}

	var builder strings.Builder
	builder.Grow(64)

	for p != 0 {
		next := p & (p - 1)
		bit := p - next

		switch bit {
		case PixbufFormatWritable:
			builder.WriteString("Writable|")
		case PixbufFormatScalable:
			builder.WriteString("Scalable|")
		case PixbufFormatThreadsafe:
			builder.WriteString("Threadsafe|")
		default:
			builder.WriteString(fmt.Sprintf("PixbufFormatFlags(0b%b)|", bit))
		}

		p = next
	}

	return strings.TrimSuffix(builder.String(), "|")
}

// Has returns true if p contains other.
func (p PixbufFormatFlags) Has(other PixbufFormatFlags) bool {
	return (p & other) == other
}

// PixbufModulePreparedFunc defines the type of the function that gets called
// once the initial setup of pixbuf is done.
//
// PixbufLoader uses a function of this type to emit the "<link
// linkend="GdkPixbufLoader-area-prepared">area_prepared</link>" signal.
type PixbufModulePreparedFunc func(pixbuf *Pixbuf, anim *PixbufAnimation)

// PixbufModuleSizeFunc defines the type of the function that gets called once
// the size of the loaded image is known.
//
// The function is expected to set width and height to the desired size to which
// the image should be scaled. If a module has no efficient way to achieve
// the desired scaling during the loading of the image, it may either ignore
// the size request, or only approximate it - gdk-pixbuf will then perform the
// required scaling on the completely loaded image.
//
// If the function sets width or height to zero, the module should interpret
// this as a hint that it will be closed soon and shouldn't allocate further
// resources. This convention is used to implement gdk_pixbuf_get_file_info()
// efficiently.
type PixbufModuleSizeFunc func(width, height *int)

// PixbufModuleUpdatedFunc defines the type of the function that gets called
// every time a region of pixbuf is updated.
//
// PixbufLoader uses a function of this type to emit the "<link
// linkend="GdkPixbufLoader-area-updated">area_updated</link>" signal.
type PixbufModuleUpdatedFunc func(pixbuf *Pixbuf, x, y, width, height int)

// PixbufSaveFunc: save functions used by gdkpixbuf.Pixbuf.SaveToCallback().
//
// This function is called once for each block of bytes that is "written" by
// gdk_pixbuf_save_to_callback().
//
// If successful it should return TRUE; if an error occurs it should set error
// and return FALSE, in which case gdk_pixbuf_save_to_callback() will fail with
// the same error.
type PixbufSaveFunc func(buf []byte) (err error, ok bool)

// Pixbuf: pixel buffer.
//
// GdkPixbuf contains information about an image's pixel data, its color space,
// bits per sample, width and height, and the rowstride (the number of bytes
// between the start of one row and the start of the next).
//
// # Creating new GdkPixbuf
//
// The most basic way to create a pixbuf is to wrap an existing
// pixel buffer with a gdkpixbuf.Pixbuf instance. You can use the
// gdkpixbuf.Pixbuf.NewFromData function to do this.
//
// Every time you create a new GdkPixbuf instance for some data, you will
// need to specify the destroy notification function that will be called when
// the data buffer needs to be freed; this will happen when a GdkPixbuf is
// finalized by the reference counting functions. If you have a chunk of static
// data compiled into your application, you can pass in NULL as the destroy
// notification function so that the data will not be freed.
//
// The gdkpixbuf.Pixbuf.New constructor function can be used as a convenience to
// create a pixbuf with an empty buffer; this is equivalent to allocating a data
// buffer using malloc() and then wrapping it with gdk_pixbuf_new_from_data().
// The gdk_pixbuf_new() function will compute an optimal rowstride so that
// rendering can be performed with an efficient algorithm.
//
// As a special case, you can use the gdkpixbuf.Pixbuf.NewFromXPMData function
// to create a pixbuf from inline XPM image data.
//
// You can also copy an existing pixbuf with the pixbuf.Copy function. This
// is not the same as just acquiring a reference to the old pixbuf instance:
// the copy function will actually duplicate the pixel data in memory and create
// a new pixbuf instance for it.
//
// # Reference counting
//
// GdkPixbuf structures are reference counted. This means that an application
// can share a single pixbuf among many parts of the code. When a piece of the
// program needs to use a pixbuf, it should acquire a reference to it by calling
// g_object_ref(); when it no longer needs the pixbuf, it should release the
// reference it acquired by calling g_object_unref(). The resources associated
// with a GdkPixbuf will be freed when its reference count drops to zero.
// Newly-created GdkPixbuf instances start with a reference count of one.
//
// # Image Data
//
// Image data in a pixbuf is stored in memory in an uncompressed, packed format.
// Rows in the image are stored top to bottom, and in each row pixels are stored
// from left to right.
//
// There may be padding at the end of a row.
//
// The "rowstride" value of a pixbuf, as returned by
// gdkpixbuf.Pixbuf.GetRowstride(), indicates the number of bytes between rows.
//
// **NOTE**: If you are copying raw pixbuf data with memcpy() note that
// the last row in the pixbuf may not be as wide as the full rowstride,
// but rather just as wide as the pixel data needs to be; that is: it is unsafe
// to do memcpy (dest, pixels, rowstride * height) to copy a whole pixbuf.
// Use gdkpixbuf.Pixbuf.Copy() instead, or compute the width in bytes of the
// last row as:
//
//	last_row = width * ((n_channels * bits_per_sample + 7) / 8);
//
// The same rule applies when iterating over each row of a GdkPixbuf pixels
// array.
//
// The following code illustrates a simple put_pixel() function for RGB pixbufs
// with 8 bits per channel with an alpha channel.
//
//	static void
//	put_pixel (GdkPixbuf *pixbuf,
//	           int x,
//		   int y,
//		   guchar red,
//		   guchar green,
//		   guchar blue,
//		   guchar alpha)
//	{
//	  int n_channels = gdk_pixbuf_get_n_channels (pixbuf);
//
//	  // Ensure that the pixbuf is valid
//	  g_assert (gdk_pixbuf_get_colorspace (pixbuf) == GDK_COLORSPACE_RGB);
//	  g_assert (gdk_pixbuf_get_bits_per_sample (pixbuf) == 8);
//	  g_assert (gdk_pixbuf_get_has_alpha (pixbuf));
//	  g_assert (n_channels == 4);
//
//	  int width = gdk_pixbuf_get_width (pixbuf);
//	  int height = gdk_pixbuf_get_height (pixbuf);
//
//	  // Ensure that the coordinates are in a valid range
//	  g_assert (x >= 0 && x < width);
//	  g_assert (y >= 0 && y < height);
//
//	  int rowstride = gdk_pixbuf_get_rowstride (pixbuf);
//
//	  // The pixel buffer in the GdkPixbuf instance
//	  guchar *pixels = gdk_pixbuf_get_pixels (pixbuf);
//
//	  // The pixel we wish to modify
//	  guchar *p = pixels + y * rowstride + x * n_channels;
//	  p[0] = red;
//	  p[1] = green;
//	  p[2] = blue;
//	  p[3] = alpha;
//	}
//
// # Loading images
//
// The GdkPixBuf class provides a simple mechanism for loading an image from a
// file in synchronous and asynchronous fashion.
//
// For GUI applications, it is recommended to use the asynchronous stream API to
// avoid blocking the control flow of the application.
//
// Additionally, GdkPixbuf provides the gdkpixbuf.PixbufLoader` API for
// progressive image loading.
//
// # Saving images
//
// The GdkPixbuf class provides methods for saving image data in a number of
// file formats. The formatted data can be written to a file or to a memory
// buffer. GdkPixbuf can also call a user-defined callback on the data, which
// allows to e.g. write the image to a socket or store it in a database.
type Pixbuf struct {
	_ [0]func() // equal guard
	*coreglib.Object

	gio.LoadableIcon
}

var (
	_ coreglib.Objector = (*Pixbuf)(nil)
)

func wrapPixbuf(obj *coreglib.Object) *Pixbuf {
	return &Pixbuf{
		Object: obj,
		LoadableIcon: gio.LoadableIcon{
			Icon: gio.Icon{
				Object: obj,
			},
		},
	}
}

func marshalPixbuf(p uintptr) (interface{}, error) {
	return wrapPixbuf(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewPixbuf creates a new GdkPixbuf structure and allocates a buffer for it.
//
// If the allocation of the buffer failed, this function will return NULL.
//
// The buffer has an optimal rowstride. Note that the buffer is not cleared;
// you will have to fill it completely yourself.
//
// The function takes the following parameters:
//
//   - colorspace: color space for image.
//   - hasAlpha: whether the image should have transparency information.
//   - bitsPerSample: number of bits per color sample.
//   - width: width of image in pixels, must be > 0.
//   - height: height of image in pixels, must be > 0.
//
// The function returns the following values:
//
//   - pixbuf (optional): newly-created pixel buffer.
func NewPixbuf(colorspace Colorspace, hasAlpha bool, bitsPerSample, width, height int) *Pixbuf {
	var _arg1 C.GdkColorspace // out
	var _arg2 C.gboolean      // out
	var _arg3 C.int           // out
	var _arg4 C.int           // out
	var _arg5 C.int           // out
	var _cret *C.GdkPixbuf    // in

	_arg1 = C.GdkColorspace(colorspace)
	if hasAlpha {
		_arg2 = C.TRUE
	}
	_arg3 = C.int(bitsPerSample)
	_arg4 = C.int(width)
	_arg5 = C.int(height)

	_cret = C.gdk_pixbuf_new(_arg1, _arg2, _arg3, _arg4, _arg5)
	runtime.KeepAlive(colorspace)
	runtime.KeepAlive(hasAlpha)
	runtime.KeepAlive(bitsPerSample)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)

	var _pixbuf *Pixbuf // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}

	return _pixbuf
}

// NewPixbufFromBytes creates a new Pixbuf out of in-memory readonly image data.
//
// Currently only RGB images with 8 bits per sample are supported.
//
// This is the GBytes variant of gdk_pixbuf_new_from_data(), useful for language
// bindings.
//
// The function takes the following parameters:
//
//   - data: image data in 8-bit/sample packed format inside a #GBytes.
//   - colorspace: colorspace for the image data.
//   - hasAlpha: whether the data has an opacity channel.
//   - bitsPerSample: number of bits per sample.
//   - width: width of the image in pixels, must be > 0.
//   - height: height of the image in pixels, must be > 0.
//   - rowstride: distance in bytes between row starts.
//
// The function returns the following values:
//
//   - pixbuf: newly-created pixbuf.
func NewPixbufFromBytes(data *glib.Bytes, colorspace Colorspace, hasAlpha bool, bitsPerSample, width, height, rowstride int) *Pixbuf {
	var _arg1 *C.GBytes       // out
	var _arg2 C.GdkColorspace // out
	var _arg3 C.gboolean      // out
	var _arg4 C.int           // out
	var _arg5 C.int           // out
	var _arg6 C.int           // out
	var _arg7 C.int           // out
	var _cret *C.GdkPixbuf    // in

	_arg1 = (*C.GBytes)(gextras.StructNative(unsafe.Pointer(data)))
	_arg2 = C.GdkColorspace(colorspace)
	if hasAlpha {
		_arg3 = C.TRUE
	}
	_arg4 = C.int(bitsPerSample)
	_arg5 = C.int(width)
	_arg6 = C.int(height)
	_arg7 = C.int(rowstride)

	_cret = C.gdk_pixbuf_new_from_bytes(_arg1, _arg2, _arg3, _arg4, _arg5, _arg6, _arg7)
	runtime.KeepAlive(data)
	runtime.KeepAlive(colorspace)
	runtime.KeepAlive(hasAlpha)
	runtime.KeepAlive(bitsPerSample)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)
	runtime.KeepAlive(rowstride)

	var _pixbuf *Pixbuf // out

	_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _pixbuf
}

// NewPixbufFromFile creates a new pixbuf by loading an image from a file.
//
// The file format is detected automatically.
//
// If NULL is returned, then error will be set. Possible errors are:
//
//   - the file could not be opened
//   - there is no loader for the file's format
//   - there is not enough memory to allocate the image buffer
//   - the image buffer contains invalid data
//
// The error domains are GDK_PIXBUF_ERROR and G_FILE_ERROR.
//
// The function takes the following parameters:
//
//   - filename: name of file to load, in the GLib file name encoding.
//
// The function returns the following values:
//
//   - pixbuf (optional): newly-created pixbuf.
func NewPixbufFromFile(filename string) (*Pixbuf, error) {
	var _arg1 *C.char      // out
	var _cret *C.GdkPixbuf // in
	var _cerr *C.GError    // in

	_arg1 = (*C.char)(unsafe.Pointer(C.CString(filename)))
	defer C.free(unsafe.Pointer(_arg1))

	_cret = C.gdk_pixbuf_new_from_file(_arg1, &_cerr)
	runtime.KeepAlive(filename)

	var _pixbuf *Pixbuf // out
	var _goerr error    // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _pixbuf, _goerr
}

// NewPixbufFromFileAtScale creates a new pixbuf by loading an image from a
// file.
//
// The file format is detected automatically.
//
// If NULL is returned, then error will be set. Possible errors are:
//
//   - the file could not be opened
//   - there is no loader for the file's format
//   - there is not enough memory to allocate the image buffer
//   - the image buffer contains invalid data
//
// The error domains are GDK_PIXBUF_ERROR and G_FILE_ERROR.
//
// The image will be scaled to fit in the requested size, optionally preserving
// the image's aspect ratio.
//
// When preserving the aspect ratio, a width of -1 will cause the image to be
// scaled to the exact given height, and a height of -1 will cause the image
// to be scaled to the exact given width. When not preserving aspect ratio, a
// width or height of -1 means to not scale the image at all in that dimension.
// Negative values for width and height are allowed since 2.8.
//
// The function takes the following parameters:
//
//   - filename: name of file to load, in the GLib file name encoding.
//   - width the image should have or -1 to not constrain the width.
//   - height the image should have or -1 to not constrain the height.
//   - preserveAspectRatio: TRUE to preserve the image's aspect ratio.
//
// The function returns the following values:
//
//   - pixbuf (optional): newly-created pixbuf.
func NewPixbufFromFileAtScale(filename string, width, height int, preserveAspectRatio bool) (*Pixbuf, error) {
	var _arg1 *C.char      // out
	var _arg2 C.int        // out
	var _arg3 C.int        // out
	var _arg4 C.gboolean   // out
	var _cret *C.GdkPixbuf // in
	var _cerr *C.GError    // in

	_arg1 = (*C.char)(unsafe.Pointer(C.CString(filename)))
	defer C.free(unsafe.Pointer(_arg1))
	_arg2 = C.int(width)
	_arg3 = C.int(height)
	if preserveAspectRatio {
		_arg4 = C.TRUE
	}

	_cret = C.gdk_pixbuf_new_from_file_at_scale(_arg1, _arg2, _arg3, _arg4, &_cerr)
	runtime.KeepAlive(filename)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)
	runtime.KeepAlive(preserveAspectRatio)

	var _pixbuf *Pixbuf // out
	var _goerr error    // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _pixbuf, _goerr
}

// NewPixbufFromFileAtSize creates a new pixbuf by loading an image from a file.
//
// The file format is detected automatically.
//
// If NULL is returned, then error will be set. Possible errors are:
//
//   - the file could not be opened
//   - there is no loader for the file's format
//   - there is not enough memory to allocate the image buffer
//   - the image buffer contains invalid data
//
// The error domains are GDK_PIXBUF_ERROR and G_FILE_ERROR.
//
// The image will be scaled to fit in the requested size, preserving the image's
// aspect ratio. Note that the returned pixbuf may be smaller than width x
// height, if the aspect ratio requires it. To load and image at the requested
// size, regardless of aspect ratio, use gdkpixbuf.Pixbuf.NewFromFileAtScale.
//
// The function takes the following parameters:
//
//   - filename: name of file to load, in the GLib file name encoding.
//   - width the image should have or -1 to not constrain the width.
//   - height the image should have or -1 to not constrain the height.
//
// The function returns the following values:
//
//   - pixbuf (optional): newly-created pixbuf.
func NewPixbufFromFileAtSize(filename string, width, height int) (*Pixbuf, error) {
	var _arg1 *C.char      // out
	var _arg2 C.int        // out
	var _arg3 C.int        // out
	var _cret *C.GdkPixbuf // in
	var _cerr *C.GError    // in

	_arg1 = (*C.char)(unsafe.Pointer(C.CString(filename)))
	defer C.free(unsafe.Pointer(_arg1))
	_arg2 = C.int(width)
	_arg3 = C.int(height)

	_cret = C.gdk_pixbuf_new_from_file_at_size(_arg1, _arg2, _arg3, &_cerr)
	runtime.KeepAlive(filename)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)

	var _pixbuf *Pixbuf // out
	var _goerr error    // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _pixbuf, _goerr
}

// NewPixbufFromInline creates a GdkPixbuf from a flat representation that is
// suitable for storing as inline data in a program.
//
// This is useful if you want to ship a program with images, but don't want to
// depend on any external files.
//
// GdkPixbuf ships with a program called gdk-pixbuf-csource, which allows for
// conversion of GdkPixbufs into such a inline representation.
//
// In almost all cases, you should pass the --raw option to gdk-pixbuf-csource.
// A sample invocation would be:
//
//	gdk-pixbuf-csource --raw --name=myimage_inline myimage.png
//
// For the typical case where the inline pixbuf is read-only static data,
// you don't need to copy the pixel data unless you intend to write to it, so
// you can pass FALSE for copy_pixels. If you pass --rle to gdk-pixbuf-csource,
// a copy will be made even if copy_pixels is FALSE, so using this option is
// generally a bad idea.
//
// If you create a pixbuf from const inline data compiled into your program,
// it's probably safe to ignore errors and disable length checks, since things
// will always succeed:
//
//	pixbuf = gdk_pixbuf_new_from_inline (-1, myimage_inline, FALSE, NULL);
//
// For non-const inline data, you could get out of memory. For untrusted inline
// data located at runtime, you could have corrupt inline data in addition.
//
// Deprecated: Use GResource instead.
//
// The function takes the following parameters:
//
//   - data: byte data containing a serialized GdkPixdata structure.
//   - copyPixels: whether to copy the pixel data, or use direct pointers data
//     for the resulting pixbuf.
//
// The function returns the following values:
//
//   - pixbuf: newly-created pixbuf.
func NewPixbufFromInline(data []byte, copyPixels bool) (*Pixbuf, error) {
	var _arg2 *C.guint8 // out
	var _arg1 C.gint
	var _arg3 C.gboolean   // out
	var _cret *C.GdkPixbuf // in
	var _cerr *C.GError    // in

	_arg1 = (C.gint)(len(data))
	if len(data) > 0 {
		_arg2 = (*C.guint8)(unsafe.Pointer(&data[0]))
	}
	if copyPixels {
		_arg3 = C.TRUE
	}

	_cret = C.gdk_pixbuf_new_from_inline(_arg1, _arg2, _arg3, &_cerr)
	runtime.KeepAlive(data)
	runtime.KeepAlive(copyPixels)

	var _pixbuf *Pixbuf // out
	var _goerr error    // out

	_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _pixbuf, _goerr
}

// NewPixbufFromResource creates a new pixbuf by loading an image from an
// resource.
//
// The file format is detected automatically. If NULL is returned, then error
// will be set.
//
// The function takes the following parameters:
//
//   - resourcePath: path of the resource file.
//
// The function returns the following values:
//
//   - pixbuf (optional): newly-created pixbuf.
func NewPixbufFromResource(resourcePath string) (*Pixbuf, error) {
	var _arg1 *C.char      // out
	var _cret *C.GdkPixbuf // in
	var _cerr *C.GError    // in

	_arg1 = (*C.char)(unsafe.Pointer(C.CString(resourcePath)))
	defer C.free(unsafe.Pointer(_arg1))

	_cret = C.gdk_pixbuf_new_from_resource(_arg1, &_cerr)
	runtime.KeepAlive(resourcePath)

	var _pixbuf *Pixbuf // out
	var _goerr error    // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _pixbuf, _goerr
}

// NewPixbufFromResourceAtScale creates a new pixbuf by loading an image from an
// resource.
//
// The file format is detected automatically. If NULL is returned, then error
// will be set.
//
// The image will be scaled to fit in the requested size, optionally preserving
// the image's aspect ratio. When preserving the aspect ratio, a width of -1
// will cause the image to be scaled to the exact given height, and a height
// of -1 will cause the image to be scaled to the exact given width. When not
// preserving aspect ratio, a width or height of -1 means to not scale the image
// at all in that dimension.
//
// The stream is not closed.
//
// The function takes the following parameters:
//
//   - resourcePath: path of the resource file.
//   - width the image should have or -1 to not constrain the width.
//   - height the image should have or -1 to not constrain the height.
//   - preserveAspectRatio: TRUE to preserve the image's aspect ratio.
//
// The function returns the following values:
//
//   - pixbuf (optional): newly-created pixbuf.
func NewPixbufFromResourceAtScale(resourcePath string, width, height int, preserveAspectRatio bool) (*Pixbuf, error) {
	var _arg1 *C.char      // out
	var _arg2 C.int        // out
	var _arg3 C.int        // out
	var _arg4 C.gboolean   // out
	var _cret *C.GdkPixbuf // in
	var _cerr *C.GError    // in

	_arg1 = (*C.char)(unsafe.Pointer(C.CString(resourcePath)))
	defer C.free(unsafe.Pointer(_arg1))
	_arg2 = C.int(width)
	_arg3 = C.int(height)
	if preserveAspectRatio {
		_arg4 = C.TRUE
	}

	_cret = C.gdk_pixbuf_new_from_resource_at_scale(_arg1, _arg2, _arg3, _arg4, &_cerr)
	runtime.KeepAlive(resourcePath)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)
	runtime.KeepAlive(preserveAspectRatio)

	var _pixbuf *Pixbuf // out
	var _goerr error    // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _pixbuf, _goerr
}

// NewPixbufFromStream creates a new pixbuf by loading an image from an input
// stream.
//
// The file format is detected automatically.
//
// If NULL is returned, then error will be set.
//
// The cancellable can be used to abort the operation from another thread. If
// the operation was cancelled, the error G_IO_ERROR_CANCELLED will be returned.
// Other possible errors are in the GDK_PIXBUF_ERROR and G_IO_ERROR domains.
//
// The stream is not closed.
//
// The function takes the following parameters:
//
//   - ctx (optional): optional GCancellable object, NULL to ignore.
//   - stream: GInputStream to load the pixbuf from.
//
// The function returns the following values:
//
//   - pixbuf (optional): newly-created pixbuf.
func NewPixbufFromStream(ctx context.Context, stream gio.InputStreamer) (*Pixbuf, error) {
	var _arg2 *C.GCancellable // out
	var _arg1 *C.GInputStream // out
	var _cret *C.GdkPixbuf    // in
	var _cerr *C.GError       // in

	{
		cancellable := gcancel.GCancellableFromContext(ctx)
		defer runtime.KeepAlive(cancellable)
		_arg2 = (*C.GCancellable)(unsafe.Pointer(cancellable.Native()))
	}
	_arg1 = (*C.GInputStream)(unsafe.Pointer(coreglib.InternObject(stream).Native()))

	_cret = C.gdk_pixbuf_new_from_stream(_arg1, _arg2, &_cerr)
	runtime.KeepAlive(ctx)
	runtime.KeepAlive(stream)

	var _pixbuf *Pixbuf // out
	var _goerr error    // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _pixbuf, _goerr
}

// NewPixbufFromStreamAtScale creates a new pixbuf by loading an image from an
// input stream.
//
// The file format is detected automatically. If NULL is returned, then error
// will be set. The cancellable can be used to abort the operation from another
// thread. If the operation was cancelled, the error G_IO_ERROR_CANCELLED will
// be returned. Other possible errors are in the GDK_PIXBUF_ERROR and G_IO_ERROR
// domains.
//
// The image will be scaled to fit in the requested size, optionally preserving
// the image's aspect ratio.
//
// When preserving the aspect ratio, a width of -1 will cause the image to be
// scaled to the exact given height, and a height of -1 will cause the image to
// be scaled to the exact given width. If both width and height are given, this
// function will behave as if the smaller of the two values is passed as -1.
//
// When not preserving aspect ratio, a width or height of -1 means to not scale
// the image at all in that dimension.
//
// The stream is not closed.
//
// The function takes the following parameters:
//
//   - ctx (optional): optional GCancellable object, NULL to ignore.
//   - stream: GInputStream to load the pixbuf from.
//   - width the image should have or -1 to not constrain the width.
//   - height the image should have or -1 to not constrain the height.
//   - preserveAspectRatio: TRUE to preserve the image's aspect ratio.
//
// The function returns the following values:
//
//   - pixbuf (optional): newly-created pixbuf.
func NewPixbufFromStreamAtScale(ctx context.Context, stream gio.InputStreamer, width, height int, preserveAspectRatio bool) (*Pixbuf, error) {
	var _arg5 *C.GCancellable // out
	var _arg1 *C.GInputStream // out
	var _arg2 C.gint          // out
	var _arg3 C.gint          // out
	var _arg4 C.gboolean      // out
	var _cret *C.GdkPixbuf    // in
	var _cerr *C.GError       // in

	{
		cancellable := gcancel.GCancellableFromContext(ctx)
		defer runtime.KeepAlive(cancellable)
		_arg5 = (*C.GCancellable)(unsafe.Pointer(cancellable.Native()))
	}
	_arg1 = (*C.GInputStream)(unsafe.Pointer(coreglib.InternObject(stream).Native()))
	_arg2 = C.gint(width)
	_arg3 = C.gint(height)
	if preserveAspectRatio {
		_arg4 = C.TRUE
	}

	_cret = C.gdk_pixbuf_new_from_stream_at_scale(_arg1, _arg2, _arg3, _arg4, _arg5, &_cerr)
	runtime.KeepAlive(ctx)
	runtime.KeepAlive(stream)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)
	runtime.KeepAlive(preserveAspectRatio)

	var _pixbuf *Pixbuf // out
	var _goerr error    // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _pixbuf, _goerr
}

// NewPixbufFromStreamFinish finishes an asynchronous pixbuf creation operation
// started with gdk_pixbuf_new_from_stream_async().
//
// The function takes the following parameters:
//
//   - asyncResult: GAsyncResult.
//
// The function returns the following values:
//
//   - pixbuf (optional): newly created pixbuf.
func NewPixbufFromStreamFinish(asyncResult gio.AsyncResulter) (*Pixbuf, error) {
	var _arg1 *C.GAsyncResult // out
	var _cret *C.GdkPixbuf    // in
	var _cerr *C.GError       // in

	_arg1 = (*C.GAsyncResult)(unsafe.Pointer(coreglib.InternObject(asyncResult).Native()))

	_cret = C.gdk_pixbuf_new_from_stream_finish(_arg1, &_cerr)
	runtime.KeepAlive(asyncResult)

	var _pixbuf *Pixbuf // out
	var _goerr error    // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _pixbuf, _goerr
}

// NewPixbufFromXPMData creates a new pixbuf by parsing XPM data in memory.
//
// This data is commonly the result of including an XPM file into a program's C
// source.
//
// The function takes the following parameters:
//
//   - data: pointer to inline XPM data.
//
// The function returns the following values:
//
//   - pixbuf (optional): newly-created pixbuf.
func NewPixbufFromXPMData(data []string) *Pixbuf {
	var _arg1 **C.char     // out
	var _cret *C.GdkPixbuf // in

	{
		_arg1 = (**C.char)(C.calloc(C.size_t((len(data) + 1)), C.size_t(unsafe.Sizeof(uint(0)))))
		defer C.free(unsafe.Pointer(_arg1))
		{
			out := unsafe.Slice(_arg1, len(data)+1)
			var zero *C.char
			out[len(data)] = zero
			for i := range data {
				out[i] = (*C.char)(unsafe.Pointer(C.CString(data[i])))
				defer C.free(unsafe.Pointer(out[i]))
			}
		}
	}

	_cret = C.gdk_pixbuf_new_from_xpm_data(_arg1)
	runtime.KeepAlive(data)

	var _pixbuf *Pixbuf // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}

	return _pixbuf
}

// AddAlpha takes an existing pixbuf and adds an alpha channel to it.
//
// If the existing pixbuf already had an alpha channel, the channel values are
// copied from the original; otherwise, the alpha channel is initialized to 255
// (full opacity).
//
// If substitute_color is TRUE, then the color specified by the (r, g, b)
// arguments will be assigned zero opacity. That is, if you pass (255, 255, 255)
// for the substitute color, all white pixels will become fully transparent.
//
// If substitute_color is FALSE, then the (r, g, b) arguments will be ignored.
//
// The function takes the following parameters:
//
//   - substituteColor: whether to set a color to zero opacity.
//   - r: red value to substitute.
//   - g: green value to substitute.
//   - b: blue value to substitute.
//
// The function returns the following values:
//
//   - ret (optional): newly-created pixbuf.
func (pixbuf *Pixbuf) AddAlpha(substituteColor bool, r, g, b byte) *Pixbuf {
	var _arg0 *C.GdkPixbuf // out
	var _arg1 C.gboolean   // out
	var _arg2 C.guchar     // out
	var _arg3 C.guchar     // out
	var _arg4 C.guchar     // out
	var _cret *C.GdkPixbuf // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))
	if substituteColor {
		_arg1 = C.TRUE
	}
	_arg2 = C.guchar(r)
	_arg3 = C.guchar(g)
	_arg4 = C.guchar(b)

	_cret = C.gdk_pixbuf_add_alpha(_arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(pixbuf)
	runtime.KeepAlive(substituteColor)
	runtime.KeepAlive(r)
	runtime.KeepAlive(g)
	runtime.KeepAlive(b)

	var _ret *Pixbuf // out

	if _cret != nil {
		_ret = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}

	return _ret
}

// ApplyEmbeddedOrientation takes an existing pixbuf and checks for the presence
// of an associated "orientation" option.
//
// The orientation option may be provided by the JPEG loader (which reads the
// exif orientation tag) or the TIFF loader (which reads the TIFF orientation
// tag, and compensates it for the partial transforms performed by libtiff).
//
// If an orientation option/tag is present, the appropriate transform will be
// performed so that the pixbuf is oriented correctly.
//
// The function returns the following values:
//
//   - pixbuf (optional): newly-created pixbuf.
func (src *Pixbuf) ApplyEmbeddedOrientation() *Pixbuf {
	var _arg0 *C.GdkPixbuf // out
	var _cret *C.GdkPixbuf // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(src).Native()))

	_cret = C.gdk_pixbuf_apply_embedded_orientation(_arg0)
	runtime.KeepAlive(src)

	var _pixbuf *Pixbuf // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}

	return _pixbuf
}

// Composite creates a transformation of the source image src by scaling by
// scale_x and scale_y then translating by offset_x and offset_y.
//
// This gives an image in the coordinates of the destination pixbuf.
// The rectangle (dest_x, dest_y, dest_width, dest_height) is then alpha blended
// onto the corresponding rectangle of the original destination image.
//
// When the destination rectangle contains parts not in the source image,
// the data at the edges of the source image is replicated to infinity.
//
// ! (composite.png).
//
// The function takes the following parameters:
//
//   - dest into which to render the results.
//   - destX: left coordinate for region to render.
//   - destY: top coordinate for region to render.
//   - destWidth: width of the region to render.
//   - destHeight: height of the region to render.
//   - offsetX: offset in the X direction (currently rounded to an integer).
//   - offsetY: offset in the Y direction (currently rounded to an integer).
//   - scaleX: scale factor in the X direction.
//   - scaleY: scale factor in the Y direction.
//   - interpType: interpolation type for the transformation.
//   - overallAlpha: overall alpha for source image (0..255).
func (src *Pixbuf) Composite(dest *Pixbuf, destX, destY, destWidth, destHeight int, offsetX, offsetY, scaleX, scaleY float64, interpType InterpType, overallAlpha int) {
	var _arg0 *C.GdkPixbuf     // out
	var _arg1 *C.GdkPixbuf     // out
	var _arg2 C.int            // out
	var _arg3 C.int            // out
	var _arg4 C.int            // out
	var _arg5 C.int            // out
	var _arg6 C.double         // out
	var _arg7 C.double         // out
	var _arg8 C.double         // out
	var _arg9 C.double         // out
	var _arg10 C.GdkInterpType // out
	var _arg11 C.int           // out

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(src).Native()))
	_arg1 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(dest).Native()))
	_arg2 = C.int(destX)
	_arg3 = C.int(destY)
	_arg4 = C.int(destWidth)
	_arg5 = C.int(destHeight)
	_arg6 = C.double(offsetX)
	_arg7 = C.double(offsetY)
	_arg8 = C.double(scaleX)
	_arg9 = C.double(scaleY)
	_arg10 = C.GdkInterpType(interpType)
	_arg11 = C.int(overallAlpha)

	C.gdk_pixbuf_composite(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, _arg6, _arg7, _arg8, _arg9, _arg10, _arg11)
	runtime.KeepAlive(src)
	runtime.KeepAlive(dest)
	runtime.KeepAlive(destX)
	runtime.KeepAlive(destY)
	runtime.KeepAlive(destWidth)
	runtime.KeepAlive(destHeight)
	runtime.KeepAlive(offsetX)
	runtime.KeepAlive(offsetY)
	runtime.KeepAlive(scaleX)
	runtime.KeepAlive(scaleY)
	runtime.KeepAlive(interpType)
	runtime.KeepAlive(overallAlpha)
}

// CompositeColor creates a transformation of the source image src by scaling
// by scale_x and scale_y then translating by offset_x and offset_y, then
// alpha blends the rectangle (dest_x ,dest_y, dest_width, dest_height) of the
// resulting image with a checkboard of the colors color1 and color2 and renders
// it onto the destination image.
//
// If the source image has no alpha channel, and overall_alpha is 255, a fast
// path is used which omits the alpha blending and just performs the scaling.
//
// See gdk_pixbuf_composite_color_simple() for a simpler variant of this
// function suitable for many tasks.
//
// The function takes the following parameters:
//
//   - dest into which to render the results.
//   - destX: left coordinate for region to render.
//   - destY: top coordinate for region to render.
//   - destWidth: width of the region to render.
//   - destHeight: height of the region to render.
//   - offsetX: offset in the X direction (currently rounded to an integer).
//   - offsetY: offset in the Y direction (currently rounded to an integer).
//   - scaleX: scale factor in the X direction.
//   - scaleY: scale factor in the Y direction.
//   - interpType: interpolation type for the transformation.
//   - overallAlpha: overall alpha for source image (0..255).
//   - checkX: x offset for the checkboard (origin of checkboard is at -check_x,
//     -check_y).
//   - checkY: y offset for the checkboard.
//   - checkSize: size of checks in the checkboard (must be a power of two).
//   - color1: color of check at upper left.
//   - color2: color of the other check.
func (src *Pixbuf) CompositeColor(dest *Pixbuf, destX, destY, destWidth, destHeight int, offsetX, offsetY, scaleX, scaleY float64, interpType InterpType, overallAlpha, checkX, checkY, checkSize int, color1, color2 uint32) {
	var _arg0 *C.GdkPixbuf     // out
	var _arg1 *C.GdkPixbuf     // out
	var _arg2 C.int            // out
	var _arg3 C.int            // out
	var _arg4 C.int            // out
	var _arg5 C.int            // out
	var _arg6 C.double         // out
	var _arg7 C.double         // out
	var _arg8 C.double         // out
	var _arg9 C.double         // out
	var _arg10 C.GdkInterpType // out
	var _arg11 C.int           // out
	var _arg12 C.int           // out
	var _arg13 C.int           // out
	var _arg14 C.int           // out
	var _arg15 C.guint32       // out
	var _arg16 C.guint32       // out

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(src).Native()))
	_arg1 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(dest).Native()))
	_arg2 = C.int(destX)
	_arg3 = C.int(destY)
	_arg4 = C.int(destWidth)
	_arg5 = C.int(destHeight)
	_arg6 = C.double(offsetX)
	_arg7 = C.double(offsetY)
	_arg8 = C.double(scaleX)
	_arg9 = C.double(scaleY)
	_arg10 = C.GdkInterpType(interpType)
	_arg11 = C.int(overallAlpha)
	_arg12 = C.int(checkX)
	_arg13 = C.int(checkY)
	_arg14 = C.int(checkSize)
	_arg15 = C.guint32(color1)
	_arg16 = C.guint32(color2)

	C.gdk_pixbuf_composite_color(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, _arg6, _arg7, _arg8, _arg9, _arg10, _arg11, _arg12, _arg13, _arg14, _arg15, _arg16)
	runtime.KeepAlive(src)
	runtime.KeepAlive(dest)
	runtime.KeepAlive(destX)
	runtime.KeepAlive(destY)
	runtime.KeepAlive(destWidth)
	runtime.KeepAlive(destHeight)
	runtime.KeepAlive(offsetX)
	runtime.KeepAlive(offsetY)
	runtime.KeepAlive(scaleX)
	runtime.KeepAlive(scaleY)
	runtime.KeepAlive(interpType)
	runtime.KeepAlive(overallAlpha)
	runtime.KeepAlive(checkX)
	runtime.KeepAlive(checkY)
	runtime.KeepAlive(checkSize)
	runtime.KeepAlive(color1)
	runtime.KeepAlive(color2)
}

// CompositeColorSimple creates a new pixbuf by scaling src to dest_width x
// dest_height and alpha blending the result with a checkboard of colors color1
// and color2.
//
// The function takes the following parameters:
//
//   - destWidth: width of destination image.
//   - destHeight: height of destination image.
//   - interpType: interpolation type for the transformation.
//   - overallAlpha: overall alpha for source image (0..255).
//   - checkSize: size of checks in the checkboard (must be a power of two).
//   - color1: color of check at upper left.
//   - color2: color of the other check.
//
// The function returns the following values:
//
//   - pixbuf (optional): new pixbuf.
func (src *Pixbuf) CompositeColorSimple(destWidth, destHeight int, interpType InterpType, overallAlpha, checkSize int, color1, color2 uint32) *Pixbuf {
	var _arg0 *C.GdkPixbuf    // out
	var _arg1 C.int           // out
	var _arg2 C.int           // out
	var _arg3 C.GdkInterpType // out
	var _arg4 C.int           // out
	var _arg5 C.int           // out
	var _arg6 C.guint32       // out
	var _arg7 C.guint32       // out
	var _cret *C.GdkPixbuf    // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(src).Native()))
	_arg1 = C.int(destWidth)
	_arg2 = C.int(destHeight)
	_arg3 = C.GdkInterpType(interpType)
	_arg4 = C.int(overallAlpha)
	_arg5 = C.int(checkSize)
	_arg6 = C.guint32(color1)
	_arg7 = C.guint32(color2)

	_cret = C.gdk_pixbuf_composite_color_simple(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, _arg6, _arg7)
	runtime.KeepAlive(src)
	runtime.KeepAlive(destWidth)
	runtime.KeepAlive(destHeight)
	runtime.KeepAlive(interpType)
	runtime.KeepAlive(overallAlpha)
	runtime.KeepAlive(checkSize)
	runtime.KeepAlive(color1)
	runtime.KeepAlive(color2)

	var _pixbuf *Pixbuf // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}

	return _pixbuf
}

// Copy creates a new GdkPixbuf with a copy of the information in the specified
// pixbuf.
//
// Note that this does not copy the options set on the original GdkPixbuf,
// use gdk_pixbuf_copy_options() for this.
//
// The function returns the following values:
//
//   - ret (optional): newly-created pixbuf.
func (pixbuf *Pixbuf) Copy() *Pixbuf {
	var _arg0 *C.GdkPixbuf // out
	var _cret *C.GdkPixbuf // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))

	_cret = C.gdk_pixbuf_copy(_arg0)
	runtime.KeepAlive(pixbuf)

	var _ret *Pixbuf // out

	if _cret != nil {
		_ret = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}

	return _ret
}

// CopyArea copies a rectangular area from src_pixbuf to dest_pixbuf.
//
// Conversion of pixbuf formats is done automatically.
//
// If the source rectangle overlaps the destination rectangle on the same
// pixbuf, it will be overwritten during the copy operation. Therefore, you can
// not use this function to scroll a pixbuf.
//
// The function takes the following parameters:
//
//   - srcX: source X coordinate within src_pixbuf.
//   - srcY: source Y coordinate within src_pixbuf.
//   - width: width of the area to copy.
//   - height: height of the area to copy.
//   - destPixbuf: destination pixbuf.
//   - destX: x coordinate within dest_pixbuf.
//   - destY: y coordinate within dest_pixbuf.
func (srcPixbuf *Pixbuf) CopyArea(srcX, srcY, width, height int, destPixbuf *Pixbuf, destX, destY int) {
	var _arg0 *C.GdkPixbuf // out
	var _arg1 C.int        // out
	var _arg2 C.int        // out
	var _arg3 C.int        // out
	var _arg4 C.int        // out
	var _arg5 *C.GdkPixbuf // out
	var _arg6 C.int        // out
	var _arg7 C.int        // out

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(srcPixbuf).Native()))
	_arg1 = C.int(srcX)
	_arg2 = C.int(srcY)
	_arg3 = C.int(width)
	_arg4 = C.int(height)
	_arg5 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(destPixbuf).Native()))
	_arg6 = C.int(destX)
	_arg7 = C.int(destY)

	C.gdk_pixbuf_copy_area(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, _arg6, _arg7)
	runtime.KeepAlive(srcPixbuf)
	runtime.KeepAlive(srcX)
	runtime.KeepAlive(srcY)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)
	runtime.KeepAlive(destPixbuf)
	runtime.KeepAlive(destX)
	runtime.KeepAlive(destY)
}

// CopyOptions copies the key/value pair options attached to a GdkPixbuf to
// another GdkPixbuf.
//
// This is useful to keep original metadata after having manipulated a file.
// However be careful to remove metadata which you've already applied, such as
// the "orientation" option after rotating the image.
//
// The function takes the following parameters:
//
//   - destPixbuf: destination pixbuf.
//
// The function returns the following values:
//
//   - ok: TRUE on success.
func (srcPixbuf *Pixbuf) CopyOptions(destPixbuf *Pixbuf) bool {
	var _arg0 *C.GdkPixbuf // out
	var _arg1 *C.GdkPixbuf // out
	var _cret C.gboolean   // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(srcPixbuf).Native()))
	_arg1 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(destPixbuf).Native()))

	_cret = C.gdk_pixbuf_copy_options(_arg0, _arg1)
	runtime.KeepAlive(srcPixbuf)
	runtime.KeepAlive(destPixbuf)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// Fill clears a pixbuf to the given RGBA value, converting the RGBA value into
// the pixbuf's pixel format.
//
// The alpha component will be ignored if the pixbuf doesn't have an alpha
// channel.
//
// The function takes the following parameters:
//
//   - pixel: RGBA pixel to used to clear (0xffffffff is opaque white,
//     0x00000000 transparent black).
func (pixbuf *Pixbuf) Fill(pixel uint32) {
	var _arg0 *C.GdkPixbuf // out
	var _arg1 C.guint32    // out

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))
	_arg1 = C.guint32(pixel)

	C.gdk_pixbuf_fill(_arg0, _arg1)
	runtime.KeepAlive(pixbuf)
	runtime.KeepAlive(pixel)
}

// Flip flips a pixbuf horizontally or vertically and returns the result in a
// new pixbuf.
//
// The function takes the following parameters:
//
//   - horizontal: TRUE to flip horizontally, FALSE to flip vertically.
//
// The function returns the following values:
//
//   - pixbuf (optional): new pixbuf.
func (src *Pixbuf) Flip(horizontal bool) *Pixbuf {
	var _arg0 *C.GdkPixbuf // out
	var _arg1 C.gboolean   // out
	var _cret *C.GdkPixbuf // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(src).Native()))
	if horizontal {
		_arg1 = C.TRUE
	}

	_cret = C.gdk_pixbuf_flip(_arg0, _arg1)
	runtime.KeepAlive(src)
	runtime.KeepAlive(horizontal)

	var _pixbuf *Pixbuf // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}

	return _pixbuf
}

// BitsPerSample queries the number of bits per color sample in a pixbuf.
//
// The function returns the following values:
//
//   - gint: number of bits per color sample.
func (pixbuf *Pixbuf) BitsPerSample() int {
	var _arg0 *C.GdkPixbuf // out
	var _cret C.int        // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))

	_cret = C.gdk_pixbuf_get_bits_per_sample(_arg0)
	runtime.KeepAlive(pixbuf)

	var _gint int // out

	_gint = int(_cret)

	return _gint
}

// ByteLength returns the length of the pixel data, in bytes.
//
// The function returns the following values:
//
//   - gsize: length of the pixel data.
func (pixbuf *Pixbuf) ByteLength() uint {
	var _arg0 *C.GdkPixbuf // out
	var _cret C.gsize      // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))

	_cret = C.gdk_pixbuf_get_byte_length(_arg0)
	runtime.KeepAlive(pixbuf)

	var _gsize uint // out

	_gsize = uint(_cret)

	return _gsize
}

// Colorspace queries the color space of a pixbuf.
//
// The function returns the following values:
//
//   - colorspace: color space.
func (pixbuf *Pixbuf) Colorspace() Colorspace {
	var _arg0 *C.GdkPixbuf    // out
	var _cret C.GdkColorspace // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))

	_cret = C.gdk_pixbuf_get_colorspace(_arg0)
	runtime.KeepAlive(pixbuf)

	var _colorspace Colorspace // out

	_colorspace = Colorspace(_cret)

	return _colorspace
}

// HasAlpha queries whether a pixbuf has an alpha channel (opacity information).
//
// The function returns the following values:
//
//   - ok: TRUE if it has an alpha channel, FALSE otherwise.
func (pixbuf *Pixbuf) HasAlpha() bool {
	var _arg0 *C.GdkPixbuf // out
	var _cret C.gboolean   // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))

	_cret = C.gdk_pixbuf_get_has_alpha(_arg0)
	runtime.KeepAlive(pixbuf)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// Height queries the height of a pixbuf.
//
// The function returns the following values:
//
//   - gint: height in pixels.
func (pixbuf *Pixbuf) Height() int {
	var _arg0 *C.GdkPixbuf // out
	var _cret C.int        // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))

	_cret = C.gdk_pixbuf_get_height(_arg0)
	runtime.KeepAlive(pixbuf)

	var _gint int // out

	_gint = int(_cret)

	return _gint
}

// NChannels queries the number of channels of a pixbuf.
//
// The function returns the following values:
//
//   - gint: number of channels.
func (pixbuf *Pixbuf) NChannels() int {
	var _arg0 *C.GdkPixbuf // out
	var _cret C.int        // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))

	_cret = C.gdk_pixbuf_get_n_channels(_arg0)
	runtime.KeepAlive(pixbuf)

	var _gint int // out

	_gint = int(_cret)

	return _gint
}

// Option looks up key in the list of options that may have been attached to the
// pixbuf when it was loaded, or that may have been attached by another function
// using gdk_pixbuf_set_option().
//
// For instance, the ANI loader provides "Title" and "Artist" options. The ICO,
// XBM, and XPM loaders provide "x_hot" and "y_hot" hot-spot options for cursor
// definitions. The PNG loader provides the tEXt ancillary chunk key/value pairs
// as options. Since 2.12, the TIFF and JPEG loaders return an "orientation"
// option string that corresponds to the embedded TIFF/Exif orientation tag (if
// present). Since 2.32, the TIFF loader sets the "multipage" option string to
// "yes" when a multi-page TIFF is loaded. Since 2.32 the JPEG and PNG loaders
// set "x-dpi" and "y-dpi" if the file contains image density information in
// dots per inch. Since 2.36.6, the JPEG loader sets the "comment" option with
// the comment EXIF tag.
//
// The function takes the following parameters:
//
//   - key: nul-terminated string.
//
// The function returns the following values:
//
//   - utf8 (optional): value associated with key.
func (pixbuf *Pixbuf) Option(key string) string {
	var _arg0 *C.GdkPixbuf // out
	var _arg1 *C.gchar     // out
	var _cret *C.gchar     // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))
	_arg1 = (*C.gchar)(unsafe.Pointer(C.CString(key)))
	defer C.free(unsafe.Pointer(_arg1))

	_cret = C.gdk_pixbuf_get_option(_arg0, _arg1)
	runtime.KeepAlive(pixbuf)
	runtime.KeepAlive(key)

	var _utf8 string // out

	if _cret != nil {
		_utf8 = C.GoString((*C.gchar)(unsafe.Pointer(_cret)))
	}

	return _utf8
}

// Options returns a GHashTable with a list of all the options that may have
// been attached to the pixbuf when it was loaded, or that may have been
// attached by another function using gdkpixbuf.Pixbuf.SetOption().
//
// The function returns the following values:
//
//   - hashTable: Table of key/values pairs.
func (pixbuf *Pixbuf) Options() map[string]string {
	var _arg0 *C.GdkPixbuf  // out
	var _cret *C.GHashTable // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))

	_cret = C.gdk_pixbuf_get_options(_arg0)
	runtime.KeepAlive(pixbuf)

	var _hashTable map[string]string // out

	_hashTable = make(map[string]string, gextras.HashTableSize(unsafe.Pointer(_cret)))
	gextras.MoveHashTable(unsafe.Pointer(_cret), true, func(k, v unsafe.Pointer) {
		ksrc := *(**C.gchar)(k)
		vsrc := *(**C.gchar)(v)
		var kdst string // out
		var vdst string // out
		kdst = C.GoString((*C.gchar)(unsafe.Pointer(ksrc)))
		vdst = C.GoString((*C.gchar)(unsafe.Pointer(vsrc)))
		_hashTable[kdst] = vdst
	})

	return _hashTable
}

// Pixels queries a pointer to the pixel data of a pixbuf.
//
// This function will cause an implicit copy of the pixbuf data if the pixbuf
// was created from read-only data.
//
// Please see the section on image data (class.Pixbuf.html#image-data) for
// information about how the pixel data is stored in memory.
//
// The function returns the following values:
//
//   - guint8s: pointer to the pixbuf's pixel data.
func (pixbuf *Pixbuf) Pixels() []byte {
	var _arg0 *C.GdkPixbuf // out
	var _cret *C.guchar    // in
	var _arg1 C.guint      // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))

	_cret = C.gdk_pixbuf_get_pixels_with_length(_arg0, &_arg1)
	runtime.KeepAlive(pixbuf)

	var _guint8s []byte // out

	_guint8s = make([]byte, _arg1)
	copy(_guint8s, unsafe.Slice((*byte)(unsafe.Pointer(_cret)), _arg1))

	return _guint8s
}

// Rowstride queries the rowstride of a pixbuf, which is the number of bytes
// between the start of a row and the start of the next row.
//
// The function returns the following values:
//
//   - gint: distance between row starts.
func (pixbuf *Pixbuf) Rowstride() int {
	var _arg0 *C.GdkPixbuf // out
	var _cret C.int        // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))

	_cret = C.gdk_pixbuf_get_rowstride(_arg0)
	runtime.KeepAlive(pixbuf)

	var _gint int // out

	_gint = int(_cret)

	return _gint
}

// Width queries the width of a pixbuf.
//
// The function returns the following values:
//
//   - gint: width in pixels.
func (pixbuf *Pixbuf) Width() int {
	var _arg0 *C.GdkPixbuf // out
	var _cret C.int        // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))

	_cret = C.gdk_pixbuf_get_width(_arg0)
	runtime.KeepAlive(pixbuf)

	var _gint int // out

	_gint = int(_cret)

	return _gint
}

// NewSubpixbuf creates a new pixbuf which represents a sub-region of
// src_pixbuf.
//
// The new pixbuf shares its pixels with the original pixbuf, so writing to one
// affects both. The new pixbuf holds a reference to src_pixbuf, so src_pixbuf
// will not be finalized until the new pixbuf is finalized.
//
// Note that if src_pixbuf is read-only, this function will force it to be
// mutable.
//
// The function takes the following parameters:
//
//   - srcX: x coord in src_pixbuf.
//   - srcY: y coord in src_pixbuf.
//   - width of region in src_pixbuf.
//   - height of region in src_pixbuf.
//
// The function returns the following values:
//
//   - pixbuf: new pixbuf.
func (srcPixbuf *Pixbuf) NewSubpixbuf(srcX, srcY, width, height int) *Pixbuf {
	var _arg0 *C.GdkPixbuf // out
	var _arg1 C.int        // out
	var _arg2 C.int        // out
	var _arg3 C.int        // out
	var _arg4 C.int        // out
	var _cret *C.GdkPixbuf // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(srcPixbuf).Native()))
	_arg1 = C.int(srcX)
	_arg2 = C.int(srcY)
	_arg3 = C.int(width)
	_arg4 = C.int(height)

	_cret = C.gdk_pixbuf_new_subpixbuf(_arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(srcPixbuf)
	runtime.KeepAlive(srcX)
	runtime.KeepAlive(srcY)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)

	var _pixbuf *Pixbuf // out

	_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _pixbuf
}

// ReadPixelBytes provides a #GBytes buffer containing the raw pixel data;
// the data must not be modified.
//
// This function allows skipping the implicit copy that must be made if
// gdk_pixbuf_get_pixels() is called on a read-only pixbuf.
//
// The function returns the following values:
//
//   - bytes: new reference to a read-only copy of the pixel data. Note that for
//     mutable pixbufs, this function will incur a one-time copy of the pixel
//     data for conversion into the returned #GBytes.
func (pixbuf *Pixbuf) ReadPixelBytes() *glib.Bytes {
	var _arg0 *C.GdkPixbuf // out
	var _cret *C.GBytes    // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))

	_cret = C.gdk_pixbuf_read_pixel_bytes(_arg0)
	runtime.KeepAlive(pixbuf)

	var _bytes *glib.Bytes // out

	_bytes = (*glib.Bytes)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_bytes)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_bytes_unref((*C.GBytes)(intern.C))
		},
	)

	return _bytes
}

// ReadPixels provides a read-only pointer to the raw pixel data.
//
// This function allows skipping the implicit copy that must be made if
// gdk_pixbuf_get_pixels() is called on a read-only pixbuf.
//
// The function returns the following values:
//
//   - guint8: read-only pointer to the raw pixel data.
func (pixbuf *Pixbuf) ReadPixels() *byte {
	var _arg0 *C.GdkPixbuf // out
	var _cret *C.guint8    // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))

	_cret = C.gdk_pixbuf_read_pixels(_arg0)
	runtime.KeepAlive(pixbuf)

	var _guint8 *byte // out

	_guint8 = (*byte)(unsafe.Pointer(_cret))

	return _guint8
}

// RemoveOption removes the key/value pair option attached to a GdkPixbuf.
//
// The function takes the following parameters:
//
//   - key: nul-terminated string representing the key to remove.
//
// The function returns the following values:
//
//   - ok: TRUE if an option was removed, FALSE if not.
func (pixbuf *Pixbuf) RemoveOption(key string) bool {
	var _arg0 *C.GdkPixbuf // out
	var _arg1 *C.gchar     // out
	var _cret C.gboolean   // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))
	_arg1 = (*C.gchar)(unsafe.Pointer(C.CString(key)))
	defer C.free(unsafe.Pointer(_arg1))

	_cret = C.gdk_pixbuf_remove_option(_arg0, _arg1)
	runtime.KeepAlive(pixbuf)
	runtime.KeepAlive(key)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// RotateSimple rotates a pixbuf by a multiple of 90 degrees, and returns the
// result in a new pixbuf.
//
// If angle is 0, this function will return a copy of src.
//
// The function takes the following parameters:
//
//   - angle to rotate by.
//
// The function returns the following values:
//
//   - pixbuf (optional): new pixbuf.
func (src *Pixbuf) RotateSimple(angle PixbufRotation) *Pixbuf {
	var _arg0 *C.GdkPixbuf        // out
	var _arg1 C.GdkPixbufRotation // out
	var _cret *C.GdkPixbuf        // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(src).Native()))
	_arg1 = C.GdkPixbufRotation(angle)

	_cret = C.gdk_pixbuf_rotate_simple(_arg0, _arg1)
	runtime.KeepAlive(src)
	runtime.KeepAlive(angle)

	var _pixbuf *Pixbuf // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}

	return _pixbuf
}

// SaturateAndPixelate modifies saturation and optionally pixelates src, placing
// the result in dest.
//
// The src and dest pixbufs must have the same image format, size, and
// rowstride.
//
// The src and dest arguments may be the same pixbuf with no ill effects.
//
// If saturation is 1.0 then saturation is not changed. If it's less than 1.0,
// saturation is reduced (the image turns toward grayscale); if greater than
// 1.0, saturation is increased (the image gets more vivid colors).
//
// If pixelate is TRUE, then pixels are faded in a checkerboard pattern to
// create a pixelated image.
//
// The function takes the following parameters:
//
//   - dest: place to write modified version of src.
//   - saturation factor.
//   - pixelate: whether to pixelate.
func (src *Pixbuf) SaturateAndPixelate(dest *Pixbuf, saturation float32, pixelate bool) {
	var _arg0 *C.GdkPixbuf // out
	var _arg1 *C.GdkPixbuf // out
	var _arg2 C.gfloat     // out
	var _arg3 C.gboolean   // out

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(src).Native()))
	_arg1 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(dest).Native()))
	_arg2 = C.gfloat(saturation)
	if pixelate {
		_arg3 = C.TRUE
	}

	C.gdk_pixbuf_saturate_and_pixelate(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(src)
	runtime.KeepAlive(dest)
	runtime.KeepAlive(saturation)
	runtime.KeepAlive(pixelate)
}

// SaveToBufferv: vector version of gdk_pixbuf_save_to_buffer().
//
// Saves pixbuf to a new buffer in format type, which is currently "jpeg",
// "tiff", "png", "ico" or "bmp".
//
// See gdkpixbuf.Pixbuf.SaveToBuffer() for more details.
//
// The function takes the following parameters:
//
//   - typ: name of file format.
//   - optionKeys (optional): name of options to set.
//   - optionValues (optional) values for named options.
//
// The function returns the following values:
//
//   - buffer: location to receive a pointer to the new buffer.
func (pixbuf *Pixbuf) SaveToBufferv(typ string, optionKeys, optionValues []string) ([]byte, error) {
	var _arg0 *C.GdkPixbuf // out
	var _arg1 *C.gchar     // in
	var _arg2 C.gsize      // in
	var _arg3 *C.char      // out
	var _arg4 **C.char     // out
	var _arg5 **C.char     // out
	var _cerr *C.GError    // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))
	_arg3 = (*C.char)(unsafe.Pointer(C.CString(typ)))
	defer C.free(unsafe.Pointer(_arg3))
	{
		_arg4 = (**C.char)(C.calloc(C.size_t((len(optionKeys) + 1)), C.size_t(unsafe.Sizeof(uint(0)))))
		defer C.free(unsafe.Pointer(_arg4))
		{
			out := unsafe.Slice(_arg4, len(optionKeys)+1)
			var zero *C.char
			out[len(optionKeys)] = zero
			for i := range optionKeys {
				out[i] = (*C.char)(unsafe.Pointer(C.CString(optionKeys[i])))
				defer C.free(unsafe.Pointer(out[i]))
			}
		}
	}
	{
		_arg5 = (**C.char)(C.calloc(C.size_t((len(optionValues) + 1)), C.size_t(unsafe.Sizeof(uint(0)))))
		defer C.free(unsafe.Pointer(_arg5))
		{
			out := unsafe.Slice(_arg5, len(optionValues)+1)
			var zero *C.char
			out[len(optionValues)] = zero
			for i := range optionValues {
				out[i] = (*C.char)(unsafe.Pointer(C.CString(optionValues[i])))
				defer C.free(unsafe.Pointer(out[i]))
			}
		}
	}

	C.gdk_pixbuf_save_to_bufferv(_arg0, &_arg1, &_arg2, _arg3, _arg4, _arg5, &_cerr)
	runtime.KeepAlive(pixbuf)
	runtime.KeepAlive(typ)
	runtime.KeepAlive(optionKeys)
	runtime.KeepAlive(optionValues)

	var _buffer []byte // out
	var _goerr error   // out

	defer C.free(unsafe.Pointer(_arg1))
	_buffer = make([]byte, _arg2)
	copy(_buffer, unsafe.Slice((*byte)(unsafe.Pointer(_arg1)), _arg2))
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _buffer, _goerr
}

// SaveToCallbackv: vector version of gdk_pixbuf_save_to_callback().
//
// Saves pixbuf to a callback in format type, which is currently "jpeg", "png",
// "tiff", "ico" or "bmp".
//
// If error is set, FALSE will be returned.
//
// See gdkpixbuf.Pixbuf.SaveToCallback() for more details.
//
// The function takes the following parameters:
//
//   - saveFunc: function that is called to save each block of data that the
//     save routine generates.
//   - typ: name of file format.
//   - optionKeys (optional): name of options to set.
//   - optionValues (optional) values for named options.
func (pixbuf *Pixbuf) SaveToCallbackv(saveFunc PixbufSaveFunc, typ string, optionKeys, optionValues []string) error {
	var _arg0 *C.GdkPixbuf        // out
	var _arg1 C.GdkPixbufSaveFunc // out
	var _arg2 C.gpointer
	var _arg3 *C.char   // out
	var _arg4 **C.char  // out
	var _arg5 **C.char  // out
	var _cerr *C.GError // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))
	_arg1 = (*[0]byte)(C._gotk4_gdkpixbuf2_PixbufSaveFunc)
	_arg2 = C.gpointer(gbox.Assign(saveFunc))
	defer gbox.Delete(uintptr(_arg2))
	_arg3 = (*C.char)(unsafe.Pointer(C.CString(typ)))
	defer C.free(unsafe.Pointer(_arg3))
	{
		_arg4 = (**C.char)(C.calloc(C.size_t((len(optionKeys) + 1)), C.size_t(unsafe.Sizeof(uint(0)))))
		defer C.free(unsafe.Pointer(_arg4))
		{
			out := unsafe.Slice(_arg4, len(optionKeys)+1)
			var zero *C.char
			out[len(optionKeys)] = zero
			for i := range optionKeys {
				out[i] = (*C.char)(unsafe.Pointer(C.CString(optionKeys[i])))
				defer C.free(unsafe.Pointer(out[i]))
			}
		}
	}
	{
		_arg5 = (**C.char)(C.calloc(C.size_t((len(optionValues) + 1)), C.size_t(unsafe.Sizeof(uint(0)))))
		defer C.free(unsafe.Pointer(_arg5))
		{
			out := unsafe.Slice(_arg5, len(optionValues)+1)
			var zero *C.char
			out[len(optionValues)] = zero
			for i := range optionValues {
				out[i] = (*C.char)(unsafe.Pointer(C.CString(optionValues[i])))
				defer C.free(unsafe.Pointer(out[i]))
			}
		}
	}

	C.gdk_pixbuf_save_to_callbackv(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, &_cerr)
	runtime.KeepAlive(pixbuf)
	runtime.KeepAlive(saveFunc)
	runtime.KeepAlive(typ)
	runtime.KeepAlive(optionKeys)
	runtime.KeepAlive(optionValues)

	var _goerr error // out

	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _goerr
}

// SaveToStreamv saves pixbuf to an output stream.
//
// Supported file formats are currently "jpeg", "tiff", "png", "ico" or "bmp".
//
// See gdkpixbuf.Pixbuf.SaveToStream() for more details.
//
// The function takes the following parameters:
//
//   - ctx (optional): optional GCancellable object, NULL to ignore.
//   - stream: GOutputStream to save the pixbuf to.
//   - typ: name of file format.
//   - optionKeys (optional): name of options to set.
//   - optionValues (optional) values for named options.
func (pixbuf *Pixbuf) SaveToStreamv(ctx context.Context, stream gio.OutputStreamer, typ string, optionKeys, optionValues []string) error {
	var _arg0 *C.GdkPixbuf     // out
	var _arg5 *C.GCancellable  // out
	var _arg1 *C.GOutputStream // out
	var _arg2 *C.char          // out
	var _arg3 **C.char         // out
	var _arg4 **C.char         // out
	var _cerr *C.GError        // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))
	{
		cancellable := gcancel.GCancellableFromContext(ctx)
		defer runtime.KeepAlive(cancellable)
		_arg5 = (*C.GCancellable)(unsafe.Pointer(cancellable.Native()))
	}
	_arg1 = (*C.GOutputStream)(unsafe.Pointer(coreglib.InternObject(stream).Native()))
	_arg2 = (*C.char)(unsafe.Pointer(C.CString(typ)))
	defer C.free(unsafe.Pointer(_arg2))
	{
		_arg3 = (**C.char)(C.calloc(C.size_t((len(optionKeys) + 1)), C.size_t(unsafe.Sizeof(uint(0)))))
		defer C.free(unsafe.Pointer(_arg3))
		{
			out := unsafe.Slice(_arg3, len(optionKeys)+1)
			var zero *C.char
			out[len(optionKeys)] = zero
			for i := range optionKeys {
				out[i] = (*C.char)(unsafe.Pointer(C.CString(optionKeys[i])))
				defer C.free(unsafe.Pointer(out[i]))
			}
		}
	}
	{
		_arg4 = (**C.char)(C.calloc(C.size_t((len(optionValues) + 1)), C.size_t(unsafe.Sizeof(uint(0)))))
		defer C.free(unsafe.Pointer(_arg4))
		{
			out := unsafe.Slice(_arg4, len(optionValues)+1)
			var zero *C.char
			out[len(optionValues)] = zero
			for i := range optionValues {
				out[i] = (*C.char)(unsafe.Pointer(C.CString(optionValues[i])))
				defer C.free(unsafe.Pointer(out[i]))
			}
		}
	}

	C.gdk_pixbuf_save_to_streamv(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, &_cerr)
	runtime.KeepAlive(pixbuf)
	runtime.KeepAlive(ctx)
	runtime.KeepAlive(stream)
	runtime.KeepAlive(typ)
	runtime.KeepAlive(optionKeys)
	runtime.KeepAlive(optionValues)

	var _goerr error // out

	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _goerr
}

// SaveToStreamvAsync saves pixbuf to an output stream asynchronously.
//
// For more details see gdk_pixbuf_save_to_streamv(), which is the synchronous
// version of this function.
//
// When the operation is finished, callback will be called in the main thread.
//
// You can then call gdk_pixbuf_save_to_stream_finish() to get the result of the
// operation.
//
// The function takes the following parameters:
//
//   - ctx (optional): optional GCancellable object, NULL to ignore.
//   - stream: GOutputStream to which to save the pixbuf.
//   - typ: name of file format.
//   - optionKeys (optional): name of options to set.
//   - optionValues (optional) values for named options.
//   - callback (optional): GAsyncReadyCallback to call when the pixbuf is
//     saved.
func (pixbuf *Pixbuf) SaveToStreamvAsync(ctx context.Context, stream gio.OutputStreamer, typ string, optionKeys, optionValues []string, callback gio.AsyncReadyCallback) {
	var _arg0 *C.GdkPixbuf          // out
	var _arg5 *C.GCancellable       // out
	var _arg1 *C.GOutputStream      // out
	var _arg2 *C.gchar              // out
	var _arg3 **C.gchar             // out
	var _arg4 **C.gchar             // out
	var _arg6 C.GAsyncReadyCallback // out
	var _arg7 C.gpointer

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))
	{
		cancellable := gcancel.GCancellableFromContext(ctx)
		defer runtime.KeepAlive(cancellable)
		_arg5 = (*C.GCancellable)(unsafe.Pointer(cancellable.Native()))
	}
	_arg1 = (*C.GOutputStream)(unsafe.Pointer(coreglib.InternObject(stream).Native()))
	_arg2 = (*C.gchar)(unsafe.Pointer(C.CString(typ)))
	defer C.free(unsafe.Pointer(_arg2))
	{
		_arg3 = (**C.gchar)(C.calloc(C.size_t((len(optionKeys) + 1)), C.size_t(unsafe.Sizeof(uint(0)))))
		defer C.free(unsafe.Pointer(_arg3))
		{
			out := unsafe.Slice(_arg3, len(optionKeys)+1)
			var zero *C.gchar
			out[len(optionKeys)] = zero
			for i := range optionKeys {
				out[i] = (*C.gchar)(unsafe.Pointer(C.CString(optionKeys[i])))
				defer C.free(unsafe.Pointer(out[i]))
			}
		}
	}
	{
		_arg4 = (**C.gchar)(C.calloc(C.size_t((len(optionValues) + 1)), C.size_t(unsafe.Sizeof(uint(0)))))
		defer C.free(unsafe.Pointer(_arg4))
		{
			out := unsafe.Slice(_arg4, len(optionValues)+1)
			var zero *C.gchar
			out[len(optionValues)] = zero
			for i := range optionValues {
				out[i] = (*C.gchar)(unsafe.Pointer(C.CString(optionValues[i])))
				defer C.free(unsafe.Pointer(out[i]))
			}
		}
	}
	if callback != nil {
		_arg6 = (*[0]byte)(C._gotk4_gio2_AsyncReadyCallback)
		_arg7 = C.gpointer(gbox.AssignOnce(callback))
	}

	C.gdk_pixbuf_save_to_streamv_async(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, _arg6, _arg7)
	runtime.KeepAlive(pixbuf)
	runtime.KeepAlive(ctx)
	runtime.KeepAlive(stream)
	runtime.KeepAlive(typ)
	runtime.KeepAlive(optionKeys)
	runtime.KeepAlive(optionValues)
	runtime.KeepAlive(callback)
}

// Savev: vector version of gdk_pixbuf_save().
//
// Saves pixbuf to a file in type, which is currently "jpeg", "png", "tiff",
// "ico" or "bmp".
//
// If error is set, FALSE will be returned.
//
// See gdkpixbuf.Pixbuf.Save() for more details.
//
// The function takes the following parameters:
//
//   - filename: name of file to save.
//   - typ: name of file format.
//   - optionKeys (optional): name of options to set.
//   - optionValues (optional) values for named options.
func (pixbuf *Pixbuf) Savev(filename, typ string, optionKeys, optionValues []string) error {
	var _arg0 *C.GdkPixbuf // out
	var _arg1 *C.char      // out
	var _arg2 *C.char      // out
	var _arg3 **C.char     // out
	var _arg4 **C.char     // out
	var _cerr *C.GError    // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))
	_arg1 = (*C.char)(unsafe.Pointer(C.CString(filename)))
	defer C.free(unsafe.Pointer(_arg1))
	_arg2 = (*C.char)(unsafe.Pointer(C.CString(typ)))
	defer C.free(unsafe.Pointer(_arg2))
	{
		_arg3 = (**C.char)(C.calloc(C.size_t((len(optionKeys) + 1)), C.size_t(unsafe.Sizeof(uint(0)))))
		defer C.free(unsafe.Pointer(_arg3))
		{
			out := unsafe.Slice(_arg3, len(optionKeys)+1)
			var zero *C.char
			out[len(optionKeys)] = zero
			for i := range optionKeys {
				out[i] = (*C.char)(unsafe.Pointer(C.CString(optionKeys[i])))
				defer C.free(unsafe.Pointer(out[i]))
			}
		}
	}
	{
		_arg4 = (**C.char)(C.calloc(C.size_t((len(optionValues) + 1)), C.size_t(unsafe.Sizeof(uint(0)))))
		defer C.free(unsafe.Pointer(_arg4))
		{
			out := unsafe.Slice(_arg4, len(optionValues)+1)
			var zero *C.char
			out[len(optionValues)] = zero
			for i := range optionValues {
				out[i] = (*C.char)(unsafe.Pointer(C.CString(optionValues[i])))
				defer C.free(unsafe.Pointer(out[i]))
			}
		}
	}

	C.gdk_pixbuf_savev(_arg0, _arg1, _arg2, _arg3, _arg4, &_cerr)
	runtime.KeepAlive(pixbuf)
	runtime.KeepAlive(filename)
	runtime.KeepAlive(typ)
	runtime.KeepAlive(optionKeys)
	runtime.KeepAlive(optionValues)

	var _goerr error // out

	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _goerr
}

// Scale creates a transformation of the source image src by scaling by scale_x
// and scale_y then translating by offset_x and offset_y, then renders the
// rectangle (dest_x, dest_y, dest_width, dest_height) of the resulting image
// onto the destination image replacing the previous contents.
//
// Try to use gdk_pixbuf_scale_simple() first; this function is
// the industrial-strength power tool you can fall back to, if
// gdk_pixbuf_scale_simple() isn't powerful enough.
//
// If the source rectangle overlaps the destination rectangle on the same
// pixbuf, it will be overwritten during the scaling which results in rendering
// artifacts.
//
// The function takes the following parameters:
//
//   - dest into which to render the results.
//   - destX: left coordinate for region to render.
//   - destY: top coordinate for region to render.
//   - destWidth: width of the region to render.
//   - destHeight: height of the region to render.
//   - offsetX: offset in the X direction (currently rounded to an integer).
//   - offsetY: offset in the Y direction (currently rounded to an integer).
//   - scaleX: scale factor in the X direction.
//   - scaleY: scale factor in the Y direction.
//   - interpType: interpolation type for the transformation.
func (src *Pixbuf) Scale(dest *Pixbuf, destX, destY, destWidth, destHeight int, offsetX, offsetY, scaleX, scaleY float64, interpType InterpType) {
	var _arg0 *C.GdkPixbuf     // out
	var _arg1 *C.GdkPixbuf     // out
	var _arg2 C.int            // out
	var _arg3 C.int            // out
	var _arg4 C.int            // out
	var _arg5 C.int            // out
	var _arg6 C.double         // out
	var _arg7 C.double         // out
	var _arg8 C.double         // out
	var _arg9 C.double         // out
	var _arg10 C.GdkInterpType // out

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(src).Native()))
	_arg1 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(dest).Native()))
	_arg2 = C.int(destX)
	_arg3 = C.int(destY)
	_arg4 = C.int(destWidth)
	_arg5 = C.int(destHeight)
	_arg6 = C.double(offsetX)
	_arg7 = C.double(offsetY)
	_arg8 = C.double(scaleX)
	_arg9 = C.double(scaleY)
	_arg10 = C.GdkInterpType(interpType)

	C.gdk_pixbuf_scale(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, _arg6, _arg7, _arg8, _arg9, _arg10)
	runtime.KeepAlive(src)
	runtime.KeepAlive(dest)
	runtime.KeepAlive(destX)
	runtime.KeepAlive(destY)
	runtime.KeepAlive(destWidth)
	runtime.KeepAlive(destHeight)
	runtime.KeepAlive(offsetX)
	runtime.KeepAlive(offsetY)
	runtime.KeepAlive(scaleX)
	runtime.KeepAlive(scaleY)
	runtime.KeepAlive(interpType)
}

// ScaleSimple: create a new pixbuf containing a copy of src scaled to
// dest_width x dest_height.
//
// This function leaves src unaffected.
//
// The interp_type should be GDK_INTERP_NEAREST if you want maximum speed (but
// when scaling down GDK_INTERP_NEAREST is usually unusably ugly). The default
// interp_type should be GDK_INTERP_BILINEAR which offers reasonable quality and
// speed.
//
// You can scale a sub-portion of src by creating a sub-pixbuf pointing into
// src; see gdkpixbuf.Pixbuf.NewSubpixbuf().
//
// If dest_width and dest_height are equal to the width and height of src,
// this function will return an unscaled copy of src.
//
// For more complicated scaling/alpha blending see gdkpixbuf.Pixbuf.Scale() and
// gdkpixbuf.Pixbuf.Composite().
//
// The function takes the following parameters:
//
//   - destWidth: width of destination image.
//   - destHeight: height of destination image.
//   - interpType: interpolation type for the transformation.
//
// The function returns the following values:
//
//   - pixbuf (optional): new pixbuf.
func (src *Pixbuf) ScaleSimple(destWidth, destHeight int, interpType InterpType) *Pixbuf {
	var _arg0 *C.GdkPixbuf    // out
	var _arg1 C.int           // out
	var _arg2 C.int           // out
	var _arg3 C.GdkInterpType // out
	var _cret *C.GdkPixbuf    // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(src).Native()))
	_arg1 = C.int(destWidth)
	_arg2 = C.int(destHeight)
	_arg3 = C.GdkInterpType(interpType)

	_cret = C.gdk_pixbuf_scale_simple(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(src)
	runtime.KeepAlive(destWidth)
	runtime.KeepAlive(destHeight)
	runtime.KeepAlive(interpType)

	var _pixbuf *Pixbuf // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}

	return _pixbuf
}

// SetOption attaches a key/value pair as an option to a GdkPixbuf.
//
// If key already exists in the list of options attached to the pixbuf, the new
// value is ignored and FALSE is returned.
//
// The function takes the following parameters:
//
//   - key: nul-terminated string.
//   - value: nul-terminated string.
//
// The function returns the following values:
//
//   - ok: TRUE on success.
func (pixbuf *Pixbuf) SetOption(key, value string) bool {
	var _arg0 *C.GdkPixbuf // out
	var _arg1 *C.gchar     // out
	var _arg2 *C.gchar     // out
	var _cret C.gboolean   // in

	_arg0 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))
	_arg1 = (*C.gchar)(unsafe.Pointer(C.CString(key)))
	defer C.free(unsafe.Pointer(_arg1))
	_arg2 = (*C.gchar)(unsafe.Pointer(C.CString(value)))
	defer C.free(unsafe.Pointer(_arg2))

	_cret = C.gdk_pixbuf_set_option(_arg0, _arg1, _arg2)
	runtime.KeepAlive(pixbuf)
	runtime.KeepAlive(key)
	runtime.KeepAlive(value)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// PixbufCalculateRowstride calculates the rowstride that an image created with
// those values would have.
//
// This function is useful for front-ends and backends that want to check image
// values without needing to create a GdkPixbuf.
//
// The function takes the following parameters:
//
//   - colorspace: color space for image.
//   - hasAlpha: whether the image should have transparency information.
//   - bitsPerSample: number of bits per color sample.
//   - width: width of image in pixels, must be > 0.
//   - height: height of image in pixels, must be > 0.
//
// The function returns the following values:
//
//   - gint: rowstride for the given values, or -1 in case of error.
func PixbufCalculateRowstride(colorspace Colorspace, hasAlpha bool, bitsPerSample, width, height int) int {
	var _arg1 C.GdkColorspace // out
	var _arg2 C.gboolean      // out
	var _arg3 C.int           // out
	var _arg4 C.int           // out
	var _arg5 C.int           // out
	var _cret C.gint          // in

	_arg1 = C.GdkColorspace(colorspace)
	if hasAlpha {
		_arg2 = C.TRUE
	}
	_arg3 = C.int(bitsPerSample)
	_arg4 = C.int(width)
	_arg5 = C.int(height)

	_cret = C.gdk_pixbuf_calculate_rowstride(_arg1, _arg2, _arg3, _arg4, _arg5)
	runtime.KeepAlive(colorspace)
	runtime.KeepAlive(hasAlpha)
	runtime.KeepAlive(bitsPerSample)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)

	var _gint int // out

	_gint = int(_cret)

	return _gint
}

// PixbufGetFileInfo parses an image file far enough to determine its format and
// size.
//
// The function takes the following parameters:
//
//   - filename: name of the file to identify.
//
// The function returns the following values:
//
//   - width (optional): return location for the width of the image.
//   - height (optional): return location for the height of the image.
//   - pixbufFormat (optional): GdkPixbufFormat describing the image format of
//     the file.
func PixbufGetFileInfo(filename string) (width, height int, pixbufFormat *PixbufFormat) {
	var _arg1 *C.gchar           // out
	var _arg2 C.gint             // in
	var _arg3 C.gint             // in
	var _cret *C.GdkPixbufFormat // in

	_arg1 = (*C.gchar)(unsafe.Pointer(C.CString(filename)))
	defer C.free(unsafe.Pointer(_arg1))

	_cret = C.gdk_pixbuf_get_file_info(_arg1, &_arg2, &_arg3)
	runtime.KeepAlive(filename)

	var _width int                  // out
	var _height int                 // out
	var _pixbufFormat *PixbufFormat // out

	_width = int(_arg2)
	_height = int(_arg3)
	if _cret != nil {
		_pixbufFormat = (*PixbufFormat)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	}

	return _width, _height, _pixbufFormat
}

// PixbufGetFileInfoAsync: asynchronously parses an image file far enough to
// determine its format and size.
//
// For more details see gdk_pixbuf_get_file_info(), which is the synchronous
// version of this function.
//
// When the operation is finished, callback will be called in the main thread.
// You can then call gdk_pixbuf_get_file_info_finish() to get the result of the
// operation.
//
// The function takes the following parameters:
//
//   - ctx (optional): optional GCancellable object, NULL to ignore.
//   - filename: name of the file to identify.
//   - callback (optional): GAsyncReadyCallback to call when the file info is
//     available.
func PixbufGetFileInfoAsync(ctx context.Context, filename string, callback gio.AsyncReadyCallback) {
	var _arg2 *C.GCancellable       // out
	var _arg1 *C.gchar              // out
	var _arg3 C.GAsyncReadyCallback // out
	var _arg4 C.gpointer

	{
		cancellable := gcancel.GCancellableFromContext(ctx)
		defer runtime.KeepAlive(cancellable)
		_arg2 = (*C.GCancellable)(unsafe.Pointer(cancellable.Native()))
	}
	_arg1 = (*C.gchar)(unsafe.Pointer(C.CString(filename)))
	defer C.free(unsafe.Pointer(_arg1))
	if callback != nil {
		_arg3 = (*[0]byte)(C._gotk4_gio2_AsyncReadyCallback)
		_arg4 = C.gpointer(gbox.AssignOnce(callback))
	}

	C.gdk_pixbuf_get_file_info_async(_arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(ctx)
	runtime.KeepAlive(filename)
	runtime.KeepAlive(callback)
}

// PixbufGetFileInfoFinish finishes an asynchronous pixbuf parsing operation
// started with gdk_pixbuf_get_file_info_async().
//
// The function takes the following parameters:
//
//   - asyncResult: GAsyncResult.
//
// The function returns the following values:
//
//   - width: return location for the width of the image, or NULL.
//   - height: return location for the height of the image, or NULL.
//   - pixbufFormat (optional): GdkPixbufFormat describing the image format of
//     the file.
func PixbufGetFileInfoFinish(asyncResult gio.AsyncResulter) (width, height int, pixbufFormat *PixbufFormat, goerr error) {
	var _arg1 *C.GAsyncResult    // out
	var _arg2 C.gint             // in
	var _arg3 C.gint             // in
	var _cret *C.GdkPixbufFormat // in
	var _cerr *C.GError          // in

	_arg1 = (*C.GAsyncResult)(unsafe.Pointer(coreglib.InternObject(asyncResult).Native()))

	_cret = C.gdk_pixbuf_get_file_info_finish(_arg1, &_arg2, &_arg3, &_cerr)
	runtime.KeepAlive(asyncResult)

	var _width int                  // out
	var _height int                 // out
	var _pixbufFormat *PixbufFormat // out
	var _goerr error                // out

	_width = int(_arg2)
	_height = int(_arg3)
	if _cret != nil {
		_pixbufFormat = (*PixbufFormat)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	}
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _width, _height, _pixbufFormat, _goerr
}

// PixbufGetFormats obtains the available information about the image formats
// supported by GdkPixbuf.
//
// The function returns the following values:
//
//   - sList: list of support image formats.
func PixbufGetFormats() []*PixbufFormat {
	var _cret *C.GSList // in

	_cret = C.gdk_pixbuf_get_formats()

	var _sList []*PixbufFormat // out

	_sList = make([]*PixbufFormat, 0, gextras.SListSize(unsafe.Pointer(_cret)))
	gextras.MoveSList(unsafe.Pointer(_cret), true, func(v unsafe.Pointer) {
		src := (*C.GdkPixbufFormat)(v)
		var dst *PixbufFormat // out
		dst = (*PixbufFormat)(gextras.NewStructNative(unsafe.Pointer(src)))
		_sList = append(_sList, dst)
	})

	return _sList
}

// PixbufInitModules initalizes the gdk-pixbuf loader modules referenced by the
// loaders.cache file present inside that directory.
//
// This is to be used by applications that want to ship certain loaders in a
// different location from the system ones.
//
// This is needed when the OS or runtime ships a minimal number of loaders so
// as to reduce the potential attack surface of carefully crafted image files,
// especially for uncommon file types. Applications that require broader image
// file types coverage, such as image viewers, would be expected to ship the
// gdk-pixbuf modules in a separate location, bundled with the application in a
// separate directory from the OS or runtime- provided modules.
//
// The function takes the following parameters:
//
//   - path: path to directory where the loaders.cache is installed.
func PixbufInitModules(path string) error {
	var _arg1 *C.char   // out
	var _cerr *C.GError // in

	_arg1 = (*C.char)(unsafe.Pointer(C.CString(path)))
	defer C.free(unsafe.Pointer(_arg1))

	C.gdk_pixbuf_init_modules(_arg1, &_cerr)
	runtime.KeepAlive(path)

	var _goerr error // out

	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _goerr
}

// NewPixbufFromStreamAsync creates a new pixbuf by asynchronously loading an
// image from an input stream.
//
// For more details see gdk_pixbuf_new_from_stream(), which is the synchronous
// version of this function.
//
// When the operation is finished, callback will be called in the main thread.
// You can then call gdk_pixbuf_new_from_stream_finish() to get the result of
// the operation.
//
// The function takes the following parameters:
//
//   - ctx (optional): optional GCancellable object, NULL to ignore.
//   - stream: GInputStream from which to load the pixbuf.
//   - callback (optional): GAsyncReadyCallback to call when the pixbuf is
//     loaded.
func NewPixbufFromStreamAsync(ctx context.Context, stream gio.InputStreamer, callback gio.AsyncReadyCallback) {
	var _arg2 *C.GCancellable       // out
	var _arg1 *C.GInputStream       // out
	var _arg3 C.GAsyncReadyCallback // out
	var _arg4 C.gpointer

	{
		cancellable := gcancel.GCancellableFromContext(ctx)
		defer runtime.KeepAlive(cancellable)
		_arg2 = (*C.GCancellable)(unsafe.Pointer(cancellable.Native()))
	}
	_arg1 = (*C.GInputStream)(unsafe.Pointer(coreglib.InternObject(stream).Native()))
	if callback != nil {
		_arg3 = (*[0]byte)(C._gotk4_gio2_AsyncReadyCallback)
		_arg4 = C.gpointer(gbox.AssignOnce(callback))
	}

	C.gdk_pixbuf_new_from_stream_async(_arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(ctx)
	runtime.KeepAlive(stream)
	runtime.KeepAlive(callback)
}

// NewPixbufFromStreamAtScaleAsync creates a new pixbuf by asynchronously
// loading an image from an input stream.
//
// For more details see gdk_pixbuf_new_from_stream_at_scale(), which is the
// synchronous version of this function.
//
// When the operation is finished, callback will be called in the main thread.
// You can then call gdk_pixbuf_new_from_stream_finish() to get the result of
// the operation.
//
// The function takes the following parameters:
//
//   - ctx (optional): optional GCancellable object, NULL to ignore.
//   - stream: GInputStream from which to load the pixbuf.
//   - width the image should have or -1 to not constrain the width.
//   - height the image should have or -1 to not constrain the height.
//   - preserveAspectRatio: TRUE to preserve the image's aspect ratio.
//   - callback (optional): GAsyncReadyCallback to call when the pixbuf is
//     loaded.
func NewPixbufFromStreamAtScaleAsync(ctx context.Context, stream gio.InputStreamer, width, height int, preserveAspectRatio bool, callback gio.AsyncReadyCallback) {
	var _arg5 *C.GCancellable       // out
	var _arg1 *C.GInputStream       // out
	var _arg2 C.gint                // out
	var _arg3 C.gint                // out
	var _arg4 C.gboolean            // out
	var _arg6 C.GAsyncReadyCallback // out
	var _arg7 C.gpointer

	{
		cancellable := gcancel.GCancellableFromContext(ctx)
		defer runtime.KeepAlive(cancellable)
		_arg5 = (*C.GCancellable)(unsafe.Pointer(cancellable.Native()))
	}
	_arg1 = (*C.GInputStream)(unsafe.Pointer(coreglib.InternObject(stream).Native()))
	_arg2 = C.gint(width)
	_arg3 = C.gint(height)
	if preserveAspectRatio {
		_arg4 = C.TRUE
	}
	if callback != nil {
		_arg6 = (*[0]byte)(C._gotk4_gio2_AsyncReadyCallback)
		_arg7 = C.gpointer(gbox.AssignOnce(callback))
	}

	C.gdk_pixbuf_new_from_stream_at_scale_async(_arg1, _arg2, _arg3, _arg4, _arg5, _arg6, _arg7)
	runtime.KeepAlive(ctx)
	runtime.KeepAlive(stream)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)
	runtime.KeepAlive(preserveAspectRatio)
	runtime.KeepAlive(callback)
}

// PixbufSaveToStreamFinish finishes an asynchronous pixbuf save operation
// started with gdk_pixbuf_save_to_stream_async().
//
// The function takes the following parameters:
//
//   - asyncResult: GAsyncResult.
func PixbufSaveToStreamFinish(asyncResult gio.AsyncResulter) error {
	var _arg1 *C.GAsyncResult // out
	var _cerr *C.GError       // in

	_arg1 = (*C.GAsyncResult)(unsafe.Pointer(coreglib.InternObject(asyncResult).Native()))

	C.gdk_pixbuf_save_to_stream_finish(_arg1, &_cerr)
	runtime.KeepAlive(asyncResult)

	var _goerr error // out

	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _goerr
}

// PixbufAnimation: opaque object representing an animation.
//
// The GdkPixBuf library provides a simple mechanism to load and represent
// animations. An animation is conceptually a series of frames to be displayed
// over time.
//
// The animation may not be represented as a series of frames internally;
// for example, it may be stored as a sprite and instructions for moving the
// sprite around a background.
//
// To display an animation you don't need to understand its representation,
// however; you just ask GdkPixbuf what should be displayed at a given point in
// time.
type PixbufAnimation struct {
	_ [0]func() // equal guard
	*coreglib.Object
}

var (
	_ coreglib.Objector = (*PixbufAnimation)(nil)
)

func wrapPixbufAnimation(obj *coreglib.Object) *PixbufAnimation {
	return &PixbufAnimation{
		Object: obj,
	}
}

func marshalPixbufAnimation(p uintptr) (interface{}, error) {
	return wrapPixbufAnimation(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewPixbufAnimationFromFile creates a new animation by loading it from a file.
//
// The file format is detected automatically.
//
// If the file's format does not support multi-frame images, then an animation
// with a single frame will be created.
//
// Possible errors are in the GDK_PIXBUF_ERROR and G_FILE_ERROR domains.
//
// The function takes the following parameters:
//
//   - filename: name of file to load, in the GLib file name encoding.
//
// The function returns the following values:
//
//   - pixbufAnimation (optional): newly-created animation.
func NewPixbufAnimationFromFile(filename string) (*PixbufAnimation, error) {
	var _arg1 *C.char               // out
	var _cret *C.GdkPixbufAnimation // in
	var _cerr *C.GError             // in

	_arg1 = (*C.char)(unsafe.Pointer(C.CString(filename)))
	defer C.free(unsafe.Pointer(_arg1))

	_cret = C.gdk_pixbuf_animation_new_from_file(_arg1, &_cerr)
	runtime.KeepAlive(filename)

	var _pixbufAnimation *PixbufAnimation // out
	var _goerr error                      // out

	if _cret != nil {
		_pixbufAnimation = wrapPixbufAnimation(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _pixbufAnimation, _goerr
}

// NewPixbufAnimationFromResource creates a new pixbuf animation by loading an
// image from an resource.
//
// The file format is detected automatically. If NULL is returned, then error
// will be set.
//
// The function takes the following parameters:
//
//   - resourcePath: path of the resource file.
//
// The function returns the following values:
//
//   - pixbufAnimation (optional): newly-created animation.
func NewPixbufAnimationFromResource(resourcePath string) (*PixbufAnimation, error) {
	var _arg1 *C.char               // out
	var _cret *C.GdkPixbufAnimation // in
	var _cerr *C.GError             // in

	_arg1 = (*C.char)(unsafe.Pointer(C.CString(resourcePath)))
	defer C.free(unsafe.Pointer(_arg1))

	_cret = C.gdk_pixbuf_animation_new_from_resource(_arg1, &_cerr)
	runtime.KeepAlive(resourcePath)

	var _pixbufAnimation *PixbufAnimation // out
	var _goerr error                      // out

	if _cret != nil {
		_pixbufAnimation = wrapPixbufAnimation(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _pixbufAnimation, _goerr
}

// NewPixbufAnimationFromStream creates a new animation by loading it from an
// input stream.
//
// The file format is detected automatically.
//
// If NULL is returned, then error will be set.
//
// The cancellable can be used to abort the operation from another thread. If
// the operation was cancelled, the error G_IO_ERROR_CANCELLED will be returned.
// Other possible errors are in the GDK_PIXBUF_ERROR and G_IO_ERROR domains.
//
// The stream is not closed.
//
// The function takes the following parameters:
//
//   - ctx (optional): optional GCancellable object.
//   - stream: GInputStream to load the pixbuf from.
//
// The function returns the following values:
//
//   - pixbufAnimation (optional): newly-created animation.
func NewPixbufAnimationFromStream(ctx context.Context, stream gio.InputStreamer) (*PixbufAnimation, error) {
	var _arg2 *C.GCancellable       // out
	var _arg1 *C.GInputStream       // out
	var _cret *C.GdkPixbufAnimation // in
	var _cerr *C.GError             // in

	{
		cancellable := gcancel.GCancellableFromContext(ctx)
		defer runtime.KeepAlive(cancellable)
		_arg2 = (*C.GCancellable)(unsafe.Pointer(cancellable.Native()))
	}
	_arg1 = (*C.GInputStream)(unsafe.Pointer(coreglib.InternObject(stream).Native()))

	_cret = C.gdk_pixbuf_animation_new_from_stream(_arg1, _arg2, &_cerr)
	runtime.KeepAlive(ctx)
	runtime.KeepAlive(stream)

	var _pixbufAnimation *PixbufAnimation // out
	var _goerr error                      // out

	if _cret != nil {
		_pixbufAnimation = wrapPixbufAnimation(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _pixbufAnimation, _goerr
}

// NewPixbufAnimationFromStreamFinish finishes an asynchronous
// pixbuf animation creation operation started with
// gdkpixbuf.PixbufAnimation().NewFromStreamAsync.
//
// The function takes the following parameters:
//
//   - asyncResult: Result.
//
// The function returns the following values:
//
//   - pixbufAnimation (optional): newly created animation.
func NewPixbufAnimationFromStreamFinish(asyncResult gio.AsyncResulter) (*PixbufAnimation, error) {
	var _arg1 *C.GAsyncResult       // out
	var _cret *C.GdkPixbufAnimation // in
	var _cerr *C.GError             // in

	_arg1 = (*C.GAsyncResult)(unsafe.Pointer(coreglib.InternObject(asyncResult).Native()))

	_cret = C.gdk_pixbuf_animation_new_from_stream_finish(_arg1, &_cerr)
	runtime.KeepAlive(asyncResult)

	var _pixbufAnimation *PixbufAnimation // out
	var _goerr error                      // out

	if _cret != nil {
		_pixbufAnimation = wrapPixbufAnimation(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _pixbufAnimation, _goerr
}

// Height queries the height of the bounding box of a pixbuf animation.
//
// The function returns the following values:
//
//   - gint: height of the bounding box of the animation.
func (animation *PixbufAnimation) Height() int {
	var _arg0 *C.GdkPixbufAnimation // out
	var _cret C.int                 // in

	_arg0 = (*C.GdkPixbufAnimation)(unsafe.Pointer(coreglib.InternObject(animation).Native()))

	_cret = C.gdk_pixbuf_animation_get_height(_arg0)
	runtime.KeepAlive(animation)

	var _gint int // out

	_gint = int(_cret)

	return _gint
}

// Iter: get an iterator for displaying an animation.
//
// The iterator provides the frames that should be displayed at a given time.
//
// start_time would normally come from g_get_current_time(),
// and marks the beginning of animation playback. After creating an
// iterator, you should immediately display the pixbuf returned by
// gdk_pixbuf_animation_iter_get_pixbuf(). Then, you should install a timeout
// (with g_timeout_add()) or by some other mechanism ensure that you'll update
// the image after gdk_pixbuf_animation_iter_get_delay_time() milliseconds. Each
// time the image is updated, you should reinstall the timeout with the new,
// possibly-changed delay time.
//
// As a shortcut, if start_time is NULL, the result of g_get_current_time() will
// be used automatically.
//
// To update the image (i.e. possibly change the result of
// gdk_pixbuf_animation_iter_get_pixbuf() to a new frame of the animation),
// call gdk_pixbuf_animation_iter_advance().
//
// If you're using PixbufLoader, in addition to updating the image after the
// delay time, you should also update it whenever you receive the area_updated
// signal and gdk_pixbuf_animation_iter_on_currently_loading_frame() returns
// TRUE. In this case, the frame currently being fed into the loader has
// received new data, so needs to be refreshed. The delay time for a frame may
// also be modified after an area_updated signal, for example if the delay time
// for a frame is encoded in the data after the frame itself. So your timeout
// should be reinstalled after any area_updated signal.
//
// A delay time of -1 is possible, indicating "infinite".
//
// The function takes the following parameters:
//
//   - startTime (optional): time when the animation starts playing.
//
// The function returns the following values:
//
//   - pixbufAnimationIter: iterator to move over the animation.
func (animation *PixbufAnimation) Iter(startTime *glib.TimeVal) *PixbufAnimationIter {
	var _arg0 *C.GdkPixbufAnimation     // out
	var _arg1 *C.GTimeVal               // out
	var _cret *C.GdkPixbufAnimationIter // in

	_arg0 = (*C.GdkPixbufAnimation)(unsafe.Pointer(coreglib.InternObject(animation).Native()))
	if startTime != nil {
		_arg1 = (*C.GTimeVal)(gextras.StructNative(unsafe.Pointer(startTime)))
	}

	_cret = C.gdk_pixbuf_animation_get_iter(_arg0, _arg1)
	runtime.KeepAlive(animation)
	runtime.KeepAlive(startTime)

	var _pixbufAnimationIter *PixbufAnimationIter // out

	_pixbufAnimationIter = wrapPixbufAnimationIter(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _pixbufAnimationIter
}

// StaticImage retrieves a static image for the animation.
//
// If an animation is really just a plain image (has only one frame), this
// function returns that image.
//
// If the animation is an animation, this function returns a reasonable image
// to use as a static unanimated image, which might be the first frame,
// or something more sophisticated depending on the file format.
//
// If an animation hasn't loaded any frames yet, this function will return NULL.
//
// The function returns the following values:
//
//   - pixbuf: unanimated image representing the animation.
func (animation *PixbufAnimation) StaticImage() *Pixbuf {
	var _arg0 *C.GdkPixbufAnimation // out
	var _cret *C.GdkPixbuf          // in

	_arg0 = (*C.GdkPixbufAnimation)(unsafe.Pointer(coreglib.InternObject(animation).Native()))

	_cret = C.gdk_pixbuf_animation_get_static_image(_arg0)
	runtime.KeepAlive(animation)

	var _pixbuf *Pixbuf // out

	_pixbuf = wrapPixbuf(coreglib.Take(unsafe.Pointer(_cret)))

	return _pixbuf
}

// Width queries the width of the bounding box of a pixbuf animation.
//
// The function returns the following values:
//
//   - gint: width of the bounding box of the animation.
func (animation *PixbufAnimation) Width() int {
	var _arg0 *C.GdkPixbufAnimation // out
	var _cret C.int                 // in

	_arg0 = (*C.GdkPixbufAnimation)(unsafe.Pointer(coreglib.InternObject(animation).Native()))

	_cret = C.gdk_pixbuf_animation_get_width(_arg0)
	runtime.KeepAlive(animation)

	var _gint int // out

	_gint = int(_cret)

	return _gint
}

// IsStaticImage checks whether the animation is a static image.
//
// If you load a file with gdk_pixbuf_animation_new_from_file() and it turns
// out to be a plain, unanimated image, then this function will return TRUE.
// Use gdk_pixbuf_animation_get_static_image() to retrieve the image.
//
// The function returns the following values:
//
//   - ok: TRUE if the "animation" was really just an image.
func (animation *PixbufAnimation) IsStaticImage() bool {
	var _arg0 *C.GdkPixbufAnimation // out
	var _cret C.gboolean            // in

	_arg0 = (*C.GdkPixbufAnimation)(unsafe.Pointer(coreglib.InternObject(animation).Native()))

	_cret = C.gdk_pixbuf_animation_is_static_image(_arg0)
	runtime.KeepAlive(animation)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// NewPixbufAnimationFromStreamAsync creates a new animation by asynchronously
// loading an image from an input stream.
//
// For more details see gdk_pixbuf_new_from_stream(), which is the synchronous
// version of this function.
//
// When the operation is finished, callback will be called in the main thread.
// You can then call gdk_pixbuf_animation_new_from_stream_finish() to get the
// result of the operation.
//
// The function takes the following parameters:
//
//   - ctx (optional): optional #GCancellable object.
//   - stream from which to load the animation.
//   - callback (optional): GAsyncReadyCallback to call when the pixbuf is
//     loaded.
func NewPixbufAnimationFromStreamAsync(ctx context.Context, stream gio.InputStreamer, callback gio.AsyncReadyCallback) {
	var _arg2 *C.GCancellable       // out
	var _arg1 *C.GInputStream       // out
	var _arg3 C.GAsyncReadyCallback // out
	var _arg4 C.gpointer

	{
		cancellable := gcancel.GCancellableFromContext(ctx)
		defer runtime.KeepAlive(cancellable)
		_arg2 = (*C.GCancellable)(unsafe.Pointer(cancellable.Native()))
	}
	_arg1 = (*C.GInputStream)(unsafe.Pointer(coreglib.InternObject(stream).Native()))
	if callback != nil {
		_arg3 = (*[0]byte)(C._gotk4_gio2_AsyncReadyCallback)
		_arg4 = C.gpointer(gbox.AssignOnce(callback))
	}

	C.gdk_pixbuf_animation_new_from_stream_async(_arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(ctx)
	runtime.KeepAlive(stream)
	runtime.KeepAlive(callback)
}

// PixbufAnimationIter: opaque object representing an iterator which points to a
// certain position in an animation.
type PixbufAnimationIter struct {
	_ [0]func() // equal guard
	*coreglib.Object
}

var (
	_ coreglib.Objector = (*PixbufAnimationIter)(nil)
)

func wrapPixbufAnimationIter(obj *coreglib.Object) *PixbufAnimationIter {
	return &PixbufAnimationIter{
		Object: obj,
	}
}

func marshalPixbufAnimationIter(p uintptr) (interface{}, error) {
	return wrapPixbufAnimationIter(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// Advance: possibly advances an animation to a new frame.
//
// Chooses the frame based on the start time passed to
// gdk_pixbuf_animation_get_iter().
//
// current_time would normally come from g_get_current_time(),
// and must be greater than or equal to the time passed to
// gdk_pixbuf_animation_get_iter(), and must increase or remain unchanged each
// time gdk_pixbuf_animation_iter_get_pixbuf() is called. That is, you can't go
// backward in time; animations only play forward.
//
// As a shortcut, pass NULL for the current time and g_get_current_time() will
// be invoked on your behalf. So you only need to explicitly pass current_time
// if you're doing something odd like playing the animation at double speed.
//
// If this function returns FALSE, there's no need to update the animation
// display, assuming the display had been rendered prior to advancing; if TRUE,
// you need to call gdk_pixbuf_animation_iter_get_pixbuf() and update the
// display with the new pixbuf.
//
// The function takes the following parameters:
//
//   - currentTime (optional): current time.
//
// The function returns the following values:
//
//   - ok: TRUE if the image may need updating.
func (iter *PixbufAnimationIter) Advance(currentTime *glib.TimeVal) bool {
	var _arg0 *C.GdkPixbufAnimationIter // out
	var _arg1 *C.GTimeVal               // out
	var _cret C.gboolean                // in

	_arg0 = (*C.GdkPixbufAnimationIter)(unsafe.Pointer(coreglib.InternObject(iter).Native()))
	if currentTime != nil {
		_arg1 = (*C.GTimeVal)(gextras.StructNative(unsafe.Pointer(currentTime)))
	}

	_cret = C.gdk_pixbuf_animation_iter_advance(_arg0, _arg1)
	runtime.KeepAlive(iter)
	runtime.KeepAlive(currentTime)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// DelayTime gets the number of milliseconds the current pixbuf should be
// displayed, or -1 if the current pixbuf should be displayed forever.
//
// The g_timeout_add() function conveniently takes a timeout in milliseconds,
// so you can use a timeout to schedule the next update.
//
// Note that some formats, like GIF, might clamp the timeout values in the image
// file to avoid updates that are just too quick. The minimum timeout for GIF
// images is currently 20 milliseconds.
//
// The function returns the following values:
//
//   - gint: delay time in milliseconds (thousandths of a second).
func (iter *PixbufAnimationIter) DelayTime() int {
	var _arg0 *C.GdkPixbufAnimationIter // out
	var _cret C.int                     // in

	_arg0 = (*C.GdkPixbufAnimationIter)(unsafe.Pointer(coreglib.InternObject(iter).Native()))

	_cret = C.gdk_pixbuf_animation_iter_get_delay_time(_arg0)
	runtime.KeepAlive(iter)

	var _gint int // out

	_gint = int(_cret)

	return _gint
}

// Pixbuf gets the current pixbuf which should be displayed.
//
// The pixbuf might not be the same size as the animation itself
// (gdk_pixbuf_animation_get_width(), gdk_pixbuf_animation_get_height()).
//
// This pixbuf should be displayed for
// gdk_pixbuf_animation_iter_get_delay_time() milliseconds.
//
// The caller of this function does not own a reference to the returned
// pixbuf; the returned pixbuf will become invalid when the iterator
// advances to the next frame, which may happen anytime you call
// gdk_pixbuf_animation_iter_advance().
//
// Copy the pixbuf to keep it (don't just add a reference), as it may get
// recycled as you advance the iterator.
//
// The function returns the following values:
//
//   - pixbuf to be displayed.
func (iter *PixbufAnimationIter) Pixbuf() *Pixbuf {
	var _arg0 *C.GdkPixbufAnimationIter // out
	var _cret *C.GdkPixbuf              // in

	_arg0 = (*C.GdkPixbufAnimationIter)(unsafe.Pointer(coreglib.InternObject(iter).Native()))

	_cret = C.gdk_pixbuf_animation_iter_get_pixbuf(_arg0)
	runtime.KeepAlive(iter)

	var _pixbuf *Pixbuf // out

	_pixbuf = wrapPixbuf(coreglib.Take(unsafe.Pointer(_cret)))

	return _pixbuf
}

// OnCurrentlyLoadingFrame: used to determine how to respond to the area_updated
// signal on PixbufLoader when loading an animation.
//
// The ::area_updated signal is emitted for an area of the frame currently
// streaming in to the loader. So if you're on the currently loading frame,
// you will need to redraw the screen for the updated area.
//
// The function returns the following values:
//
//   - ok: TRUE if the frame we're on is partially loaded, or the last frame.
func (iter *PixbufAnimationIter) OnCurrentlyLoadingFrame() bool {
	var _arg0 *C.GdkPixbufAnimationIter // out
	var _cret C.gboolean                // in

	_arg0 = (*C.GdkPixbufAnimationIter)(unsafe.Pointer(coreglib.InternObject(iter).Native()))

	_cret = C.gdk_pixbuf_animation_iter_on_currently_loading_frame(_arg0)
	runtime.KeepAlive(iter)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// PixbufLoaderOverrides contains methods that are overridable.
type PixbufLoaderOverrides struct {
	AreaPrepared func()
	// The function takes the following parameters:
	//
	//   - x
	//   - y
	//   - width
	//   - height
	AreaUpdated func(x, y, width, height int)
	Closed      func()
	// The function takes the following parameters:
	//
	//   - width
	//   - height
	SizePrepared func(width, height int)
}

func defaultPixbufLoaderOverrides(v *PixbufLoader) PixbufLoaderOverrides {
	return PixbufLoaderOverrides{
		AreaPrepared: v.areaPrepared,
		AreaUpdated:  v.areaUpdated,
		Closed:       v.closed,
		SizePrepared: v.sizePrepared,
	}
}

// PixbufLoader: incremental image loader.
//
// GdkPixbufLoader provides a way for applications to drive the process
// of loading an image, by letting them send the image data directly to
// the loader instead of having the loader read the data from a file.
// Applications can use this functionality instead of gdk_pixbuf_new_from_file()
// or gdk_pixbuf_animation_new_from_file() when they need to parse image data
// in small chunks. For example, it should be used when reading an image from
// a (potentially) slow network connection, or when loading an extremely large
// file.
//
// To use GdkPixbufLoader to load an image, create a new instance,
// and call gdkpixbuf.PixbufLoader.Write() to send the data to it. When done,
// gdkpixbuf.PixbufLoader.Close() should be called to end the stream and
// finalize everything.
//
// The loader will emit three important signals throughout the process:
//
//   - gdkpixbuf.PixbufLoader::size-prepared will be emitted as soon as the
//     image has enough information to determine the size of the image to be
//     used. If you want to scale the image while loading it, you can call
//     gdkpixbuf.PixbufLoader.SetSize() in response to this signal.
//   - gdkpixbuf.PixbufLoader::area-prepared will be emitted as soon as
//     the pixbuf of the desired has been allocated. You can obtain the
//     GdkPixbuf instance by calling gdkpixbuf.PixbufLoader.GetPixbuf().
//     If you want to use it, simply acquire a reference to it. You can also
//     call gdk_pixbuf_loader_get_pixbuf() later to get the same pixbuf.
//   - gdkpixbuf.PixbufLoader::area-updated will be emitted every time a region
//     is updated. This way you can update a partially completed image. Note
//     that you do not know anything about the completeness of an image from the
//     updated area. For example, in an interlaced image you will need to make
//     several passes before the image is done loading.
//
// # Loading an animation
//
// Loading an animation is almost as easy as loading an image. Once the first
// gdkpixbuf.PixbufLoader::area-prepared signal has been emitted, you can call
// gdkpixbuf.PixbufLoader.GetAnimation() to get the gdkpixbuf.PixbufAnimation
// instance, and then call and gdkpixbuf.PixbufAnimation.GetIter() to get a
// gdkpixbuf.PixbufAnimationIter to retrieve the pixbuf for the desired time
// stamp.
type PixbufLoader struct {
	_ [0]func() // equal guard
	*coreglib.Object
}

var (
	_ coreglib.Objector = (*PixbufLoader)(nil)
)

func init() {
	coreglib.RegisterClassInfo[*PixbufLoader, *PixbufLoaderClass, PixbufLoaderOverrides](
		GTypePixbufLoader,
		initPixbufLoaderClass,
		wrapPixbufLoader,
		defaultPixbufLoaderOverrides,
	)
}

func initPixbufLoaderClass(gclass unsafe.Pointer, overrides PixbufLoaderOverrides, classInitFunc func(*PixbufLoaderClass)) {
	pclass := (*C.GdkPixbufLoaderClass)(unsafe.Pointer(C.g_type_check_class_cast((*C.GTypeClass)(gclass), C.GType(GTypePixbufLoader))))

	if overrides.AreaPrepared != nil {
		pclass.area_prepared = (*[0]byte)(C._gotk4_gdkpixbuf2_PixbufLoaderClass_area_prepared)
	}

	if overrides.AreaUpdated != nil {
		pclass.area_updated = (*[0]byte)(C._gotk4_gdkpixbuf2_PixbufLoaderClass_area_updated)
	}

	if overrides.Closed != nil {
		pclass.closed = (*[0]byte)(C._gotk4_gdkpixbuf2_PixbufLoaderClass_closed)
	}

	if overrides.SizePrepared != nil {
		pclass.size_prepared = (*[0]byte)(C._gotk4_gdkpixbuf2_PixbufLoaderClass_size_prepared)
	}

	if classInitFunc != nil {
		class := (*PixbufLoaderClass)(gextras.NewStructNative(gclass))
		classInitFunc(class)
	}
}

func wrapPixbufLoader(obj *coreglib.Object) *PixbufLoader {
	return &PixbufLoader{
		Object: obj,
	}
}

func marshalPixbufLoader(p uintptr) (interface{}, error) {
	return wrapPixbufLoader(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// ConnectAreaPrepared: this signal is emitted when the pixbuf loader has
// allocated the pixbuf in the desired size.
//
// After this signal is emitted, applications can call
// gdk_pixbuf_loader_get_pixbuf() to fetch the partially-loaded pixbuf.
func (loader *PixbufLoader) ConnectAreaPrepared(f func()) coreglib.SignalHandle {
	return coreglib.ConnectGeneratedClosure(loader, "area-prepared", false, unsafe.Pointer(C._gotk4_gdkpixbuf2_PixbufLoader_ConnectAreaPrepared), f)
}

// ConnectAreaUpdated: this signal is emitted when a significant area of the
// image being loaded has been updated.
//
// Normally it means that a complete scanline has been read in, but it could be
// a different area as well.
//
// Applications can use this signal to know when to repaint areas of an image
// that is being loaded.
func (loader *PixbufLoader) ConnectAreaUpdated(f func(x, y, width, height int)) coreglib.SignalHandle {
	return coreglib.ConnectGeneratedClosure(loader, "area-updated", false, unsafe.Pointer(C._gotk4_gdkpixbuf2_PixbufLoader_ConnectAreaUpdated), f)
}

// ConnectClosed: this signal is emitted when gdk_pixbuf_loader_close() is
// called.
//
// It can be used by different parts of an application to receive notification
// when an image loader is closed by the code that drives it.
func (loader *PixbufLoader) ConnectClosed(f func()) coreglib.SignalHandle {
	return coreglib.ConnectGeneratedClosure(loader, "closed", false, unsafe.Pointer(C._gotk4_gdkpixbuf2_PixbufLoader_ConnectClosed), f)
}

// ConnectSizePrepared: this signal is emitted when the pixbuf loader has been
// fed the initial amount of data that is required to figure out the size of the
// image that it will create.
//
// Applications can call gdk_pixbuf_loader_set_size() in response to this signal
// to set the desired size to which the image should be scaled.
func (loader *PixbufLoader) ConnectSizePrepared(f func(width, height int)) coreglib.SignalHandle {
	return coreglib.ConnectGeneratedClosure(loader, "size-prepared", false, unsafe.Pointer(C._gotk4_gdkpixbuf2_PixbufLoader_ConnectSizePrepared), f)
}

// NewPixbufLoader creates a new pixbuf loader object.
//
// The function returns the following values:
//
//   - pixbufLoader: newly-created pixbuf loader.
func NewPixbufLoader() *PixbufLoader {
	var _cret *C.GdkPixbufLoader // in

	_cret = C.gdk_pixbuf_loader_new()

	var _pixbufLoader *PixbufLoader // out

	_pixbufLoader = wrapPixbufLoader(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _pixbufLoader
}

// NewPixbufLoaderWithMIMEType creates a new pixbuf loader object that always
// attempts to parse image data as if it were an image of MIME type mime_type,
// instead of identifying the type automatically.
//
// This function is useful if you want an error if the image isn't the expected
// MIME type; for loading image formats that can't be reliably identified by
// looking at the data; or if the user manually forces a specific MIME type.
//
// The list of supported mime types depends on what image loaders are installed,
// but typically "image/png", "image/jpeg", "image/gif", "image/tiff" and
// "image/x-xpixmap" are among the supported mime types. To obtain the full list
// of supported mime types, call gdk_pixbuf_format_get_mime_types() on each of
// the PixbufFormat structs returned by gdk_pixbuf_get_formats().
//
// The function takes the following parameters:
//
//   - mimeType: mime type to be loaded.
//
// The function returns the following values:
//
//   - pixbufLoader: newly-created pixbuf loader.
func NewPixbufLoaderWithMIMEType(mimeType string) (*PixbufLoader, error) {
	var _arg1 *C.char            // out
	var _cret *C.GdkPixbufLoader // in
	var _cerr *C.GError          // in

	_arg1 = (*C.char)(unsafe.Pointer(C.CString(mimeType)))
	defer C.free(unsafe.Pointer(_arg1))

	_cret = C.gdk_pixbuf_loader_new_with_mime_type(_arg1, &_cerr)
	runtime.KeepAlive(mimeType)

	var _pixbufLoader *PixbufLoader // out
	var _goerr error                // out

	_pixbufLoader = wrapPixbufLoader(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _pixbufLoader, _goerr
}

// NewPixbufLoaderWithType creates a new pixbuf loader object that always
// attempts to parse image data as if it were an image of type image_type,
// instead of identifying the type automatically.
//
// This function is useful if you want an error if the image isn't the expected
// type; for loading image formats that can't be reliably identified by looking
// at the data; or if the user manually forces a specific type.
//
// The list of supported image formats depends on what image loaders are
// installed, but typically "png", "jpeg", "gif", "tiff" and "xpm" are among the
// supported formats. To obtain the full list of supported image formats, call
// gdk_pixbuf_format_get_name() on each of the PixbufFormat structs returned by
// gdk_pixbuf_get_formats().
//
// The function takes the following parameters:
//
//   - imageType: name of the image format to be loaded with the image.
//
// The function returns the following values:
//
//   - pixbufLoader: newly-created pixbuf loader.
func NewPixbufLoaderWithType(imageType string) (*PixbufLoader, error) {
	var _arg1 *C.char            // out
	var _cret *C.GdkPixbufLoader // in
	var _cerr *C.GError          // in

	_arg1 = (*C.char)(unsafe.Pointer(C.CString(imageType)))
	defer C.free(unsafe.Pointer(_arg1))

	_cret = C.gdk_pixbuf_loader_new_with_type(_arg1, &_cerr)
	runtime.KeepAlive(imageType)

	var _pixbufLoader *PixbufLoader // out
	var _goerr error                // out

	_pixbufLoader = wrapPixbufLoader(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _pixbufLoader, _goerr
}

// Close informs a pixbuf loader that no further writes with
// gdk_pixbuf_loader_write() will occur, so that it can free its internal
// loading structures.
//
// This function also tries to parse any data that hasn't yet been parsed;
// if the remaining data is partial or corrupt, an error will be returned.
//
// If FALSE is returned, error will be set to an error from the GDK_PIXBUF_ERROR
// or G_FILE_ERROR domains.
//
// If you're just cancelling a load rather than expecting it to be finished,
// passing NULL for error to ignore it is reasonable.
//
// Remember that this function does not release a reference on the loader,
// so you will need to explicitly release any reference you hold.
func (loader *PixbufLoader) Close() error {
	var _arg0 *C.GdkPixbufLoader // out
	var _cerr *C.GError          // in

	_arg0 = (*C.GdkPixbufLoader)(unsafe.Pointer(coreglib.InternObject(loader).Native()))

	C.gdk_pixbuf_loader_close(_arg0, &_cerr)
	runtime.KeepAlive(loader)

	var _goerr error // out

	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _goerr
}

// Animation queries the PixbufAnimation that a pixbuf loader is currently
// creating.
//
// In general it only makes sense to call this function after the
// gdkpixbuf.PixbufLoader::area-prepared signal has been emitted by the loader.
//
// If the loader doesn't have enough bytes yet, and hasn't emitted the
// area-prepared signal, this function will return NULL.
//
// The function returns the following values:
//
//   - pixbufAnimation (optional): animation that the loader is currently
//     loading.
func (loader *PixbufLoader) Animation() *PixbufAnimation {
	var _arg0 *C.GdkPixbufLoader    // out
	var _cret *C.GdkPixbufAnimation // in

	_arg0 = (*C.GdkPixbufLoader)(unsafe.Pointer(coreglib.InternObject(loader).Native()))

	_cret = C.gdk_pixbuf_loader_get_animation(_arg0)
	runtime.KeepAlive(loader)

	var _pixbufAnimation *PixbufAnimation // out

	if _cret != nil {
		_pixbufAnimation = wrapPixbufAnimation(coreglib.Take(unsafe.Pointer(_cret)))
	}

	return _pixbufAnimation
}

// Format obtains the available information about the format of the currently
// loading image file.
//
// The function returns the following values:
//
//   - pixbufFormat (optional): PixbufFormat.
func (loader *PixbufLoader) Format() *PixbufFormat {
	var _arg0 *C.GdkPixbufLoader // out
	var _cret *C.GdkPixbufFormat // in

	_arg0 = (*C.GdkPixbufLoader)(unsafe.Pointer(coreglib.InternObject(loader).Native()))

	_cret = C.gdk_pixbuf_loader_get_format(_arg0)
	runtime.KeepAlive(loader)

	var _pixbufFormat *PixbufFormat // out

	if _cret != nil {
		_pixbufFormat = (*PixbufFormat)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	}

	return _pixbufFormat
}

// Pixbuf queries the Pixbuf that a pixbuf loader is currently creating.
//
// In general it only makes sense to call this function after the
// gdkpixbuf.PixbufLoader::area-prepared signal has been emitted by the loader;
// this means that enough data has been read to know the size of the image that
// will be allocated.
//
// If the loader has not received enough data via gdk_pixbuf_loader_write(),
// then this function returns NULL.
//
// The returned pixbuf will be the same in all future calls to the loader,
// so if you want to keep using it, you should acquire a reference to it.
//
// Additionally, if the loader is an animation, it will return the "static
// image" of the animation (see gdk_pixbuf_animation_get_static_image()).
//
// The function returns the following values:
//
//   - pixbuf (optional) that the loader is creating.
func (loader *PixbufLoader) Pixbuf() *Pixbuf {
	var _arg0 *C.GdkPixbufLoader // out
	var _cret *C.GdkPixbuf       // in

	_arg0 = (*C.GdkPixbufLoader)(unsafe.Pointer(coreglib.InternObject(loader).Native()))

	_cret = C.gdk_pixbuf_loader_get_pixbuf(_arg0)
	runtime.KeepAlive(loader)

	var _pixbuf *Pixbuf // out

	if _cret != nil {
		_pixbuf = wrapPixbuf(coreglib.Take(unsafe.Pointer(_cret)))
	}

	return _pixbuf
}

// SetSize causes the image to be scaled while it is loaded.
//
// The desired image size can be determined relative to the original size of the
// image by calling gdk_pixbuf_loader_set_size() from a signal handler for the
// ::size-prepared signal.
//
// Attempts to set the desired image size are ignored after the emission of the
// ::size-prepared signal.
//
// The function takes the following parameters:
//
//   - width: desired width of the image being loaded.
//   - height: desired height of the image being loaded.
func (loader *PixbufLoader) SetSize(width, height int) {
	var _arg0 *C.GdkPixbufLoader // out
	var _arg1 C.int              // out
	var _arg2 C.int              // out

	_arg0 = (*C.GdkPixbufLoader)(unsafe.Pointer(coreglib.InternObject(loader).Native()))
	_arg1 = C.int(width)
	_arg2 = C.int(height)

	C.gdk_pixbuf_loader_set_size(_arg0, _arg1, _arg2)
	runtime.KeepAlive(loader)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)
}

// Write parses the next count bytes in the given image buffer.
//
// The function takes the following parameters:
//
//   - buf: pointer to image data.
func (loader *PixbufLoader) Write(buf []byte) error {
	var _arg0 *C.GdkPixbufLoader // out
	var _arg1 *C.guchar          // out
	var _arg2 C.gsize
	var _cerr *C.GError // in

	_arg0 = (*C.GdkPixbufLoader)(unsafe.Pointer(coreglib.InternObject(loader).Native()))
	_arg2 = (C.gsize)(len(buf))
	if len(buf) > 0 {
		_arg1 = (*C.guchar)(unsafe.Pointer(&buf[0]))
	}

	C.gdk_pixbuf_loader_write(_arg0, _arg1, _arg2, &_cerr)
	runtime.KeepAlive(loader)
	runtime.KeepAlive(buf)

	var _goerr error // out

	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _goerr
}

// WriteBytes parses the next contents of the given image buffer.
//
// The function takes the following parameters:
//
//   - buffer: image data as a GBytes buffer.
func (loader *PixbufLoader) WriteBytes(buffer *glib.Bytes) error {
	var _arg0 *C.GdkPixbufLoader // out
	var _arg1 *C.GBytes          // out
	var _cerr *C.GError          // in

	_arg0 = (*C.GdkPixbufLoader)(unsafe.Pointer(coreglib.InternObject(loader).Native()))
	_arg1 = (*C.GBytes)(gextras.StructNative(unsafe.Pointer(buffer)))

	C.gdk_pixbuf_loader_write_bytes(_arg0, _arg1, &_cerr)
	runtime.KeepAlive(loader)
	runtime.KeepAlive(buffer)

	var _goerr error // out

	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _goerr
}

func (loader *PixbufLoader) areaPrepared() {
	gclass := (*C.GdkPixbufLoaderClass)(coreglib.PeekParentClass(loader))
	fnarg := gclass.area_prepared

	var _arg0 *C.GdkPixbufLoader // out

	_arg0 = (*C.GdkPixbufLoader)(unsafe.Pointer(coreglib.InternObject(loader).Native()))

	C._gotk4_gdkpixbuf2_PixbufLoader_virtual_area_prepared(unsafe.Pointer(fnarg), _arg0)
	runtime.KeepAlive(loader)
}

// The function takes the following parameters:
//
//   - x
//   - y
//   - width
//   - height
func (loader *PixbufLoader) areaUpdated(x, y, width, height int) {
	gclass := (*C.GdkPixbufLoaderClass)(coreglib.PeekParentClass(loader))
	fnarg := gclass.area_updated

	var _arg0 *C.GdkPixbufLoader // out
	var _arg1 C.int              // out
	var _arg2 C.int              // out
	var _arg3 C.int              // out
	var _arg4 C.int              // out

	_arg0 = (*C.GdkPixbufLoader)(unsafe.Pointer(coreglib.InternObject(loader).Native()))
	_arg1 = C.int(x)
	_arg2 = C.int(y)
	_arg3 = C.int(width)
	_arg4 = C.int(height)

	C._gotk4_gdkpixbuf2_PixbufLoader_virtual_area_updated(unsafe.Pointer(fnarg), _arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(loader)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)
}

func (loader *PixbufLoader) closed() {
	gclass := (*C.GdkPixbufLoaderClass)(coreglib.PeekParentClass(loader))
	fnarg := gclass.closed

	var _arg0 *C.GdkPixbufLoader // out

	_arg0 = (*C.GdkPixbufLoader)(unsafe.Pointer(coreglib.InternObject(loader).Native()))

	C._gotk4_gdkpixbuf2_PixbufLoader_virtual_closed(unsafe.Pointer(fnarg), _arg0)
	runtime.KeepAlive(loader)
}

// The function takes the following parameters:
//
//   - width
//   - height
func (loader *PixbufLoader) sizePrepared(width, height int) {
	gclass := (*C.GdkPixbufLoaderClass)(coreglib.PeekParentClass(loader))
	fnarg := gclass.size_prepared

	var _arg0 *C.GdkPixbufLoader // out
	var _arg1 C.int              // out
	var _arg2 C.int              // out

	_arg0 = (*C.GdkPixbufLoader)(unsafe.Pointer(coreglib.InternObject(loader).Native()))
	_arg1 = C.int(width)
	_arg2 = C.int(height)

	C._gotk4_gdkpixbuf2_PixbufLoader_virtual_size_prepared(unsafe.Pointer(fnarg), _arg0, _arg1, _arg2)
	runtime.KeepAlive(loader)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)
}

// PixbufSimpleAnim: opaque struct representing a simple animation.
type PixbufSimpleAnim struct {
	_ [0]func() // equal guard
	PixbufAnimation
}

var (
	_ coreglib.Objector = (*PixbufSimpleAnim)(nil)
)

func wrapPixbufSimpleAnim(obj *coreglib.Object) *PixbufSimpleAnim {
	return &PixbufSimpleAnim{
		PixbufAnimation: PixbufAnimation{
			Object: obj,
		},
	}
}

func marshalPixbufSimpleAnim(p uintptr) (interface{}, error) {
	return wrapPixbufSimpleAnim(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewPixbufSimpleAnim creates a new, empty animation.
//
// The function takes the following parameters:
//
//   - width of the animation.
//   - height of the animation.
//   - rate: speed of the animation, in frames per second.
//
// The function returns the following values:
//
//   - pixbufSimpleAnim: newly allocated PixbufSimpleAnim.
func NewPixbufSimpleAnim(width, height int, rate float32) *PixbufSimpleAnim {
	var _arg1 C.gint                 // out
	var _arg2 C.gint                 // out
	var _arg3 C.gfloat               // out
	var _cret *C.GdkPixbufSimpleAnim // in

	_arg1 = C.gint(width)
	_arg2 = C.gint(height)
	_arg3 = C.gfloat(rate)

	_cret = C.gdk_pixbuf_simple_anim_new(_arg1, _arg2, _arg3)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)
	runtime.KeepAlive(rate)

	var _pixbufSimpleAnim *PixbufSimpleAnim // out

	_pixbufSimpleAnim = wrapPixbufSimpleAnim(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _pixbufSimpleAnim
}

// AddFrame adds a new frame to animation. The pixbuf must have the dimensions
// specified when the animation was constructed.
//
// The function takes the following parameters:
//
//   - pixbuf to add.
func (animation *PixbufSimpleAnim) AddFrame(pixbuf *Pixbuf) {
	var _arg0 *C.GdkPixbufSimpleAnim // out
	var _arg1 *C.GdkPixbuf           // out

	_arg0 = (*C.GdkPixbufSimpleAnim)(unsafe.Pointer(coreglib.InternObject(animation).Native()))
	_arg1 = (*C.GdkPixbuf)(unsafe.Pointer(coreglib.InternObject(pixbuf).Native()))

	C.gdk_pixbuf_simple_anim_add_frame(_arg0, _arg1)
	runtime.KeepAlive(animation)
	runtime.KeepAlive(pixbuf)
}

// Loop gets whether animation should loop indefinitely when it reaches the end.
//
// The function returns the following values:
//
//   - ok: TRUE if the animation loops forever, FALSE otherwise.
func (animation *PixbufSimpleAnim) Loop() bool {
	var _arg0 *C.GdkPixbufSimpleAnim // out
	var _cret C.gboolean             // in

	_arg0 = (*C.GdkPixbufSimpleAnim)(unsafe.Pointer(coreglib.InternObject(animation).Native()))

	_cret = C.gdk_pixbuf_simple_anim_get_loop(_arg0)
	runtime.KeepAlive(animation)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// SetLoop sets whether animation should loop indefinitely when it reaches the
// end.
//
// The function takes the following parameters:
//
//   - loop: whether to loop the animation.
func (animation *PixbufSimpleAnim) SetLoop(loop bool) {
	var _arg0 *C.GdkPixbufSimpleAnim // out
	var _arg1 C.gboolean             // out

	_arg0 = (*C.GdkPixbufSimpleAnim)(unsafe.Pointer(coreglib.InternObject(animation).Native()))
	if loop {
		_arg1 = C.TRUE
	}

	C.gdk_pixbuf_simple_anim_set_loop(_arg0, _arg1)
	runtime.KeepAlive(animation)
	runtime.KeepAlive(loop)
}

type PixbufSimpleAnimIter struct {
	_ [0]func() // equal guard
	PixbufAnimationIter
}

var (
	_ coreglib.Objector = (*PixbufSimpleAnimIter)(nil)
)

func wrapPixbufSimpleAnimIter(obj *coreglib.Object) *PixbufSimpleAnimIter {
	return &PixbufSimpleAnimIter{
		PixbufAnimationIter: PixbufAnimationIter{
			Object: obj,
		},
	}
}

func marshalPixbufSimpleAnimIter(p uintptr) (interface{}, error) {
	return wrapPixbufSimpleAnimIter(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// PixbufFormat: GdkPixbufFormat contains information about the image format
// accepted by a module.
//
// Only modules should access the fields directly, applications should use the
// gdk_pixbuf_format_* family of functions.
//
// An instance of this type is always passed by reference.
type PixbufFormat struct {
	*pixbufFormat
}

// pixbufFormat is the struct that's finalized.
type pixbufFormat struct {
	native *C.GdkPixbufFormat
}

func marshalPixbufFormat(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &PixbufFormat{&pixbufFormat{(*C.GdkPixbufFormat)(b)}}, nil
}

// Copy creates a copy of format.
//
// The function returns the following values:
//
//   - pixbufFormat (optional): newly allocated copy of a GdkPixbufFormat.
//     Use gdk_pixbuf_format_free() to free the resources when done.
func (format *PixbufFormat) Copy() *PixbufFormat {
	var _arg0 *C.GdkPixbufFormat // out
	var _cret *C.GdkPixbufFormat // in

	_arg0 = (*C.GdkPixbufFormat)(gextras.StructNative(unsafe.Pointer(format)))

	_cret = C.gdk_pixbuf_format_copy(_arg0)
	runtime.KeepAlive(format)

	var _pixbufFormat *PixbufFormat // out

	if _cret != nil {
		_pixbufFormat = (*PixbufFormat)(gextras.NewStructNative(unsafe.Pointer(_cret)))
		runtime.SetFinalizer(
			gextras.StructIntern(unsafe.Pointer(_pixbufFormat)),
			func(intern *struct{ C unsafe.Pointer }) {
				C.gdk_pixbuf_format_free((*C.GdkPixbufFormat)(intern.C))
			},
		)
	}

	return _pixbufFormat
}

// Description returns a description of the format.
//
// The function returns the following values:
//
//   - utf8 (optional): description of the format.
func (format *PixbufFormat) Description() string {
	var _arg0 *C.GdkPixbufFormat // out
	var _cret *C.gchar           // in

	_arg0 = (*C.GdkPixbufFormat)(gextras.StructNative(unsafe.Pointer(format)))

	_cret = C.gdk_pixbuf_format_get_description(_arg0)
	runtime.KeepAlive(format)

	var _utf8 string // out

	if _cret != nil {
		_utf8 = C.GoString((*C.gchar)(unsafe.Pointer(_cret)))
		defer C.free(unsafe.Pointer(_cret))
	}

	return _utf8
}

// Extensions returns the filename extensions typically used for files in the
// given format.
//
// The function returns the following values:
//
//   - utf8s (optional): array of filename extensions.
func (format *PixbufFormat) Extensions() []string {
	var _arg0 *C.GdkPixbufFormat // out
	var _cret **C.gchar          // in

	_arg0 = (*C.GdkPixbufFormat)(gextras.StructNative(unsafe.Pointer(format)))

	_cret = C.gdk_pixbuf_format_get_extensions(_arg0)
	runtime.KeepAlive(format)

	var _utf8s []string // out

	if _cret != nil {
		defer C.free(unsafe.Pointer(_cret))
		{
			var i int
			var z *C.gchar
			for p := _cret; *p != z; p = &unsafe.Slice(p, 2)[1] {
				i++
			}

			src := unsafe.Slice(_cret, i)
			_utf8s = make([]string, i)
			for i := range src {
				_utf8s[i] = C.GoString((*C.gchar)(unsafe.Pointer(src[i])))
				defer C.free(unsafe.Pointer(src[i]))
			}
		}
	}

	return _utf8s
}

// License returns information about the license of the image loader for the
// format.
//
// The returned string should be a shorthand for a well known license, e.g.
// "LGPL", "GPL", "QPL", "GPL/QPL", or "other" to indicate some other license.
//
// The function returns the following values:
//
//   - utf8 (optional): string describing the license of the pixbuf format.
func (format *PixbufFormat) License() string {
	var _arg0 *C.GdkPixbufFormat // out
	var _cret *C.gchar           // in

	_arg0 = (*C.GdkPixbufFormat)(gextras.StructNative(unsafe.Pointer(format)))

	_cret = C.gdk_pixbuf_format_get_license(_arg0)
	runtime.KeepAlive(format)

	var _utf8 string // out

	if _cret != nil {
		_utf8 = C.GoString((*C.gchar)(unsafe.Pointer(_cret)))
		defer C.free(unsafe.Pointer(_cret))
	}

	return _utf8
}

// MIMETypes returns the mime types supported by the format.
//
// The function returns the following values:
//
//   - utf8s (optional): array of mime types.
func (format *PixbufFormat) MIMETypes() []string {
	var _arg0 *C.GdkPixbufFormat // out
	var _cret **C.gchar          // in

	_arg0 = (*C.GdkPixbufFormat)(gextras.StructNative(unsafe.Pointer(format)))

	_cret = C.gdk_pixbuf_format_get_mime_types(_arg0)
	runtime.KeepAlive(format)

	var _utf8s []string // out

	if _cret != nil {
		defer C.free(unsafe.Pointer(_cret))
		{
			var i int
			var z *C.gchar
			for p := _cret; *p != z; p = &unsafe.Slice(p, 2)[1] {
				i++
			}

			src := unsafe.Slice(_cret, i)
			_utf8s = make([]string, i)
			for i := range src {
				_utf8s[i] = C.GoString((*C.gchar)(unsafe.Pointer(src[i])))
				defer C.free(unsafe.Pointer(src[i]))
			}
		}
	}

	return _utf8s
}

// Name returns the name of the format.
//
// The function returns the following values:
//
//   - utf8 (optional): name of the format.
func (format *PixbufFormat) Name() string {
	var _arg0 *C.GdkPixbufFormat // out
	var _cret *C.gchar           // in

	_arg0 = (*C.GdkPixbufFormat)(gextras.StructNative(unsafe.Pointer(format)))

	_cret = C.gdk_pixbuf_format_get_name(_arg0)
	runtime.KeepAlive(format)

	var _utf8 string // out

	if _cret != nil {
		_utf8 = C.GoString((*C.gchar)(unsafe.Pointer(_cret)))
		defer C.free(unsafe.Pointer(_cret))
	}

	return _utf8
}

// IsDisabled returns whether this image format is disabled.
//
// See gdk_pixbuf_format_set_disabled().
//
// The function returns the following values:
//
//   - ok: whether this image format is disabled.
func (format *PixbufFormat) IsDisabled() bool {
	var _arg0 *C.GdkPixbufFormat // out
	var _cret C.gboolean         // in

	_arg0 = (*C.GdkPixbufFormat)(gextras.StructNative(unsafe.Pointer(format)))

	_cret = C.gdk_pixbuf_format_is_disabled(_arg0)
	runtime.KeepAlive(format)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// IsSaveOptionSupported returns TRUE if the save option specified by option_key
// is supported when saving a pixbuf using the module implementing format.
//
// See gdk_pixbuf_save() for more information about option keys.
//
// The function takes the following parameters:
//
//   - optionKey: name of an option.
//
// The function returns the following values:
//
//   - ok: TRUE if the specified option is supported.
func (format *PixbufFormat) IsSaveOptionSupported(optionKey string) bool {
	var _arg0 *C.GdkPixbufFormat // out
	var _arg1 *C.gchar           // out
	var _cret C.gboolean         // in

	_arg0 = (*C.GdkPixbufFormat)(gextras.StructNative(unsafe.Pointer(format)))
	_arg1 = (*C.gchar)(unsafe.Pointer(C.CString(optionKey)))
	defer C.free(unsafe.Pointer(_arg1))

	_cret = C.gdk_pixbuf_format_is_save_option_supported(_arg0, _arg1)
	runtime.KeepAlive(format)
	runtime.KeepAlive(optionKey)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// IsScalable returns whether this image format is scalable.
//
// If a file is in a scalable format, it is preferable to load it at the desired
// size, rather than loading it at the default size and scaling the resulting
// pixbuf to the desired size.
//
// The function returns the following values:
//
//   - ok: whether this image format is scalable.
func (format *PixbufFormat) IsScalable() bool {
	var _arg0 *C.GdkPixbufFormat // out
	var _cret C.gboolean         // in

	_arg0 = (*C.GdkPixbufFormat)(gextras.StructNative(unsafe.Pointer(format)))

	_cret = C.gdk_pixbuf_format_is_scalable(_arg0)
	runtime.KeepAlive(format)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// IsWritable returns whether pixbufs can be saved in the given format.
//
// The function returns the following values:
//
//   - ok: whether pixbufs can be saved in the given format.
func (format *PixbufFormat) IsWritable() bool {
	var _arg0 *C.GdkPixbufFormat // out
	var _cret C.gboolean         // in

	_arg0 = (*C.GdkPixbufFormat)(gextras.StructNative(unsafe.Pointer(format)))

	_cret = C.gdk_pixbuf_format_is_writable(_arg0)
	runtime.KeepAlive(format)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// SetDisabled disables or enables an image format.
//
// If a format is disabled, GdkPixbuf won't use the image loader for this format
// to load images.
//
// Applications can use this to avoid using image loaders with an inappropriate
// license, see gdk_pixbuf_format_get_license().
//
// The function takes the following parameters:
//
//   - disabled: TRUE to disable the format format.
func (format *PixbufFormat) SetDisabled(disabled bool) {
	var _arg0 *C.GdkPixbufFormat // out
	var _arg1 C.gboolean         // out

	_arg0 = (*C.GdkPixbufFormat)(gextras.StructNative(unsafe.Pointer(format)))
	if disabled {
		_arg1 = C.TRUE
	}

	C.gdk_pixbuf_format_set_disabled(_arg0, _arg1)
	runtime.KeepAlive(format)
	runtime.KeepAlive(disabled)
}

// PixbufLoaderClass: instance of this type is always passed by reference.
type PixbufLoaderClass struct {
	*pixbufLoaderClass
}

// pixbufLoaderClass is the struct that's finalized.
type pixbufLoaderClass struct {
	native *C.GdkPixbufLoaderClass
}

// NewPixbufFromImage creates a new Pixbuf from a stdlib image.Image. It
// contains a fast path for *image.RGBA while resorting to
// copying/converting the image otherwise.
func NewPixbufFromImage(img image.Image) *Pixbuf {
	bounds := img.Bounds()
	var pixbuf *Pixbuf

	switch img := img.(type) {
	case *image.RGBA:
		bytes := glib.NewBytesWithGo(img.Pix)
		pixbuf = NewPixbufFromBytes(bytes, ColorspaceRGB, true, 8, bounds.Dx(), bounds.Dy(), img.Stride)
	default:
		pixbuf = NewPixbuf(ColorspaceRGB, true, 8, bounds.Dx(), bounds.Dy())
		pixbuf.ReadPixelBytes().Use(func(b []byte) {
			// For information on how this works, refer to
			// pkg/cairo/surface_image.go.
			rgba := image.RGBA{
				Pix:    b,
				Stride: bounds.Dx(),
				Rect:   bounds,
			}
			draw.Draw(&rgba, rgba.Rect, img, image.Point{}, draw.Over)
			swizzle.BGRA(rgba.Pix)
		})
	}

	return pixbuf
}
