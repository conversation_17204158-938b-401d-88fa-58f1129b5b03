// Code generated by girgen. DO NOT EDIT.

package pango

import (
	"runtime"
	"unsafe"

	"github.com/diamondburned/gotk4/pkg/core/gbox"
	"github.com/diamondburned/gotk4/pkg/core/gextras"
	coreglib "github.com/diamondburned/gotk4/pkg/core/glib"
)

// #include <stdlib.h>
// #include <pango/pango.h>
import "C"

//export _gotk4_pango1_AttrDataCopyFunc
func _gotk4_pango1_AttrDataCopyFunc(arg1 C.gconstpointer) (cret C.gpointer) {
	var fn AttrDataCopyFunc
	{
		v := gbox.Get(uintptr(arg1))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(AttrDataCopyFunc)
	}

	gpointer := fn()

	var _ unsafe.Pointer

	cret = (C.gpointer)(unsafe.Pointer(gpointer))

	return cret
}

//export _gotk4_pango1_AttrFilterFunc
func _gotk4_pango1_AttrFilterFunc(arg1 *C.PangoAttribute, arg2 C.gpointer) (cret C.gboolean) {
	var fn AttrFilterFunc
	{
		v := gbox.Get(uintptr(arg2))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(AttrFilterFunc)
	}

	var _attribute *Attribute // out

	_attribute = (*Attribute)(gextras.NewStructNative(unsafe.Pointer(arg1)))

	ok := fn(_attribute)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_pango1_FontsetForEachFunc
func _gotk4_pango1_FontsetForEachFunc(arg1 *C.PangoFontset, arg2 *C.PangoFont, arg3 C.gpointer) (cret C.gboolean) {
	var fn FontsetForEachFunc
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(FontsetForEachFunc)
	}

	var _fontset Fontsetter // out
	var _font Fonter        // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type pango.Fontsetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Fontsetter)
			return ok
		})
		rv, ok := casted.(Fontsetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching pango.Fontsetter")
		}
		_fontset = rv
	}
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type pango.Fonter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Fonter)
			return ok
		})
		rv, ok := casted.(Fonter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching pango.Fonter")
		}
		_font = rv
	}

	ok := fn(_fontset, _font)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_pango1_FontClass_describe
func _gotk4_pango1_FontClass_describe(arg0 *C.PangoFont) (cret *C.PangoFontDescription) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontOverrides](instance0)
	if overrides.Describe == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontOverrides.Describe, got none")
	}

	fontDescription := overrides.Describe()

	var _ *FontDescription

	cret = (*C.PangoFontDescription)(gextras.StructNative(unsafe.Pointer(fontDescription)))
	runtime.SetFinalizer(gextras.StructIntern(unsafe.Pointer(fontDescription)), nil)

	return cret
}

//export _gotk4_pango1_FontClass_describe_absolute
func _gotk4_pango1_FontClass_describe_absolute(arg0 *C.PangoFont) (cret *C.PangoFontDescription) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontOverrides](instance0)
	if overrides.DescribeAbsolute == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontOverrides.DescribeAbsolute, got none")
	}

	fontDescription := overrides.DescribeAbsolute()

	var _ *FontDescription

	cret = (*C.PangoFontDescription)(gextras.StructNative(unsafe.Pointer(fontDescription)))
	runtime.SetFinalizer(gextras.StructIntern(unsafe.Pointer(fontDescription)), nil)

	return cret
}

//export _gotk4_pango1_FontClass_get_coverage
func _gotk4_pango1_FontClass_get_coverage(arg0 *C.PangoFont, arg1 *C.PangoLanguage) (cret *C.PangoCoverage) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontOverrides](instance0)
	if overrides.Coverage == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontOverrides.Coverage, got none")
	}

	var _language *Language // out

	_language = (*Language)(gextras.NewStructNative(unsafe.Pointer(arg1)))

	coverage := overrides.Coverage(_language)

	var _ *Coverage

	cret = (*C.PangoCoverage)(unsafe.Pointer(coreglib.InternObject(coverage).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(coverage).Native()))

	return cret
}

//export _gotk4_pango1_FontClass_get_font_map
func _gotk4_pango1_FontClass_get_font_map(arg0 *C.PangoFont) (cret *C.PangoFontMap) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontOverrides](instance0)
	if overrides.FontMap == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontOverrides.FontMap, got none")
	}

	fontMap := overrides.FontMap()

	var _ FontMapper

	if fontMap != nil {
		cret = (*C.PangoFontMap)(unsafe.Pointer(coreglib.InternObject(fontMap).Native()))
	}

	return cret
}

//export _gotk4_pango1_FontClass_get_glyph_extents
func _gotk4_pango1_FontClass_get_glyph_extents(arg0 *C.PangoFont, arg1 C.PangoGlyph, arg2 *C.PangoRectangle, arg3 *C.PangoRectangle) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontOverrides](instance0)
	if overrides.GlyphExtents == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontOverrides.GlyphExtents, got none")
	}

	var _glyph Glyph // out

	_glyph = Glyph(arg1)

	inkRect, logicalRect := overrides.GlyphExtents(_glyph)

	var _ *Rectangle
	var _ *Rectangle

	if inkRect != nil {
		if inkRect != nil {
			*arg2 = *(*C.PangoRectangle)(gextras.StructNative(unsafe.Pointer(inkRect)))
		}
	}
	if logicalRect != nil {
		if logicalRect != nil {
			*arg3 = *(*C.PangoRectangle)(gextras.StructNative(unsafe.Pointer(logicalRect)))
		}
	}
}

//export _gotk4_pango1_FontClass_get_metrics
func _gotk4_pango1_FontClass_get_metrics(arg0 *C.PangoFont, arg1 *C.PangoLanguage) (cret *C.PangoFontMetrics) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontOverrides](instance0)
	if overrides.Metrics == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontOverrides.Metrics, got none")
	}

	var _language *Language // out

	if arg1 != nil {
		_language = (*Language)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	}

	fontMetrics := overrides.Metrics(_language)

	var _ *FontMetrics

	cret = (*C.PangoFontMetrics)(gextras.StructNative(unsafe.Pointer(fontMetrics)))

	return cret
}

//export _gotk4_pango1_FontFaceClass_describe
func _gotk4_pango1_FontFaceClass_describe(arg0 *C.PangoFontFace) (cret *C.PangoFontDescription) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontFaceOverrides](instance0)
	if overrides.Describe == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontFaceOverrides.Describe, got none")
	}

	fontDescription := overrides.Describe()

	var _ *FontDescription

	cret = (*C.PangoFontDescription)(gextras.StructNative(unsafe.Pointer(fontDescription)))
	runtime.SetFinalizer(gextras.StructIntern(unsafe.Pointer(fontDescription)), nil)

	return cret
}

//export _gotk4_pango1_FontFaceClass_get_face_name
func _gotk4_pango1_FontFaceClass_get_face_name(arg0 *C.PangoFontFace) (cret *C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontFaceOverrides](instance0)
	if overrides.FaceName == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontFaceOverrides.FaceName, got none")
	}

	utf8 := overrides.FaceName()

	var _ string

	cret = (*C.char)(unsafe.Pointer(C.CString(utf8)))
	defer C.free(unsafe.Pointer(cret))

	return cret
}

//export _gotk4_pango1_FontFaceClass_get_family
func _gotk4_pango1_FontFaceClass_get_family(arg0 *C.PangoFontFace) (cret *C.PangoFontFamily) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontFaceOverrides](instance0)
	if overrides.Family == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontFaceOverrides.Family, got none")
	}

	fontFamily := overrides.Family()

	var _ FontFamilier

	cret = (*C.PangoFontFamily)(unsafe.Pointer(coreglib.InternObject(fontFamily).Native()))

	return cret
}

//export _gotk4_pango1_FontFaceClass_is_synthesized
func _gotk4_pango1_FontFaceClass_is_synthesized(arg0 *C.PangoFontFace) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontFaceOverrides](instance0)
	if overrides.IsSynthesized == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontFaceOverrides.IsSynthesized, got none")
	}

	ok := overrides.IsSynthesized()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_pango1_FontFaceClass_list_sizes
func _gotk4_pango1_FontFaceClass_list_sizes(arg0 *C.PangoFontFace, arg1 **C.int, arg2 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontFaceOverrides](instance0)
	if overrides.ListSizes == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontFaceOverrides.ListSizes, got none")
	}

	sizes := overrides.ListSizes()

	var _ []int

	*arg2 = (C.int)(len(sizes))
	*arg1 = (*C.int)(C.calloc(C.size_t(len(sizes)), C.size_t(unsafe.Sizeof(uint(0)))))
	{
		out := unsafe.Slice((*C.int)(*arg1), len(sizes))
		for i := range sizes {
			out[i] = C.int(sizes[i])
		}
	}
}

//export _gotk4_pango1_FontFamilyClass_get_face
func _gotk4_pango1_FontFamilyClass_get_face(arg0 *C.PangoFontFamily, arg1 *C.char) (cret *C.PangoFontFace) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontFamilyOverrides](instance0)
	if overrides.Face == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontFamilyOverrides.Face, got none")
	}

	var _name string // out

	if arg1 != nil {
		_name = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	}

	fontFace := overrides.Face(_name)

	var _ FontFacer

	if fontFace != nil {
		cret = (*C.PangoFontFace)(unsafe.Pointer(coreglib.InternObject(fontFace).Native()))
	}

	return cret
}

//export _gotk4_pango1_FontFamilyClass_get_name
func _gotk4_pango1_FontFamilyClass_get_name(arg0 *C.PangoFontFamily) (cret *C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontFamilyOverrides](instance0)
	if overrides.Name == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontFamilyOverrides.Name, got none")
	}

	utf8 := overrides.Name()

	var _ string

	cret = (*C.char)(unsafe.Pointer(C.CString(utf8)))
	defer C.free(unsafe.Pointer(cret))

	return cret
}

//export _gotk4_pango1_FontFamilyClass_is_monospace
func _gotk4_pango1_FontFamilyClass_is_monospace(arg0 *C.PangoFontFamily) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontFamilyOverrides](instance0)
	if overrides.IsMonospace == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontFamilyOverrides.IsMonospace, got none")
	}

	ok := overrides.IsMonospace()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_pango1_FontFamilyClass_is_variable
func _gotk4_pango1_FontFamilyClass_is_variable(arg0 *C.PangoFontFamily) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontFamilyOverrides](instance0)
	if overrides.IsVariable == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontFamilyOverrides.IsVariable, got none")
	}

	ok := overrides.IsVariable()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_pango1_FontFamilyClass_list_faces
func _gotk4_pango1_FontFamilyClass_list_faces(arg0 *C.PangoFontFamily, arg1 ***C.PangoFontFace, arg2 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontFamilyOverrides](instance0)
	if overrides.ListFaces == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontFamilyOverrides.ListFaces, got none")
	}

	faces := overrides.ListFaces()

	var _ []FontFacer

	if faces != nil {
		*arg2 = (C.int)(len(faces))
		*arg1 = (**C.PangoFontFace)(C.calloc(C.size_t(len(faces)), C.size_t(unsafe.Sizeof(uint(0)))))
		{
			out := unsafe.Slice((**C.PangoFontFace)(*arg1), len(faces))
			for i := range faces {
				out[i] = (*C.PangoFontFace)(unsafe.Pointer(coreglib.InternObject(faces[i]).Native()))
			}
		}
	}
}

//export _gotk4_pango1_FontMapClass_changed
func _gotk4_pango1_FontMapClass_changed(arg0 *C.PangoFontMap) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontMapOverrides](instance0)
	if overrides.Changed == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontMapOverrides.Changed, got none")
	}

	overrides.Changed()
}

//export _gotk4_pango1_FontMapClass_get_family
func _gotk4_pango1_FontMapClass_get_family(arg0 *C.PangoFontMap, arg1 *C.char) (cret *C.PangoFontFamily) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontMapOverrides](instance0)
	if overrides.Family == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontMapOverrides.Family, got none")
	}

	var _name string // out

	_name = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	fontFamily := overrides.Family(_name)

	var _ FontFamilier

	cret = (*C.PangoFontFamily)(unsafe.Pointer(coreglib.InternObject(fontFamily).Native()))

	return cret
}

//export _gotk4_pango1_FontMapClass_get_serial
func _gotk4_pango1_FontMapClass_get_serial(arg0 *C.PangoFontMap) (cret C.guint) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontMapOverrides](instance0)
	if overrides.Serial == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontMapOverrides.Serial, got none")
	}

	guint := overrides.Serial()

	var _ uint

	cret = C.guint(guint)

	return cret
}

//export _gotk4_pango1_FontMapClass_list_families
func _gotk4_pango1_FontMapClass_list_families(arg0 *C.PangoFontMap, arg1 ***C.PangoFontFamily, arg2 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontMapOverrides](instance0)
	if overrides.ListFamilies == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontMapOverrides.ListFamilies, got none")
	}

	families := overrides.ListFamilies()

	var _ []FontFamilier

	*arg2 = (C.int)(len(families))
	*arg1 = (**C.PangoFontFamily)(C.calloc(C.size_t(len(families)), C.size_t(unsafe.Sizeof(uint(0)))))
	{
		out := unsafe.Slice((**C.PangoFontFamily)(*arg1), len(families))
		for i := range families {
			out[i] = (*C.PangoFontFamily)(unsafe.Pointer(coreglib.InternObject(families[i]).Native()))
		}
	}
}

//export _gotk4_pango1_FontMapClass_load_font
func _gotk4_pango1_FontMapClass_load_font(arg0 *C.PangoFontMap, arg1 *C.PangoContext, arg2 *C.PangoFontDescription) (cret *C.PangoFont) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontMapOverrides](instance0)
	if overrides.LoadFont == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontMapOverrides.LoadFont, got none")
	}

	var _context *Context      // out
	var _desc *FontDescription // out

	_context = wrapContext(coreglib.Take(unsafe.Pointer(arg1)))
	_desc = (*FontDescription)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	font := overrides.LoadFont(_context, _desc)

	var _ Fonter

	if font != nil {
		cret = (*C.PangoFont)(unsafe.Pointer(coreglib.InternObject(font).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(font).Native()))
	}

	return cret
}

//export _gotk4_pango1_FontMapClass_load_fontset
func _gotk4_pango1_FontMapClass_load_fontset(arg0 *C.PangoFontMap, arg1 *C.PangoContext, arg2 *C.PangoFontDescription, arg3 *C.PangoLanguage) (cret *C.PangoFontset) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontMapOverrides](instance0)
	if overrides.LoadFontset == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontMapOverrides.LoadFontset, got none")
	}

	var _context *Context      // out
	var _desc *FontDescription // out
	var _language *Language    // out

	_context = wrapContext(coreglib.Take(unsafe.Pointer(arg1)))
	_desc = (*FontDescription)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_language = (*Language)(gextras.NewStructNative(unsafe.Pointer(arg3)))

	fontset := overrides.LoadFontset(_context, _desc, _language)

	var _ Fontsetter

	if fontset != nil {
		cret = (*C.PangoFontset)(unsafe.Pointer(coreglib.InternObject(fontset).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(fontset).Native()))
	}

	return cret
}

//export _gotk4_pango1_FontsetClass_get_font
func _gotk4_pango1_FontsetClass_get_font(arg0 *C.PangoFontset, arg1 C.guint) (cret *C.PangoFont) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontsetOverrides](instance0)
	if overrides.Font == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontsetOverrides.Font, got none")
	}

	var _wc uint // out

	_wc = uint(arg1)

	font := overrides.Font(_wc)

	var _ Fonter

	cret = (*C.PangoFont)(unsafe.Pointer(coreglib.InternObject(font).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(font).Native()))

	return cret
}

//export _gotk4_pango1_FontsetClass_get_language
func _gotk4_pango1_FontsetClass_get_language(arg0 *C.PangoFontset) (cret *C.PangoLanguage) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontsetOverrides](instance0)
	if overrides.Language == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontsetOverrides.Language, got none")
	}

	language := overrides.Language()

	var _ *Language

	cret = (*C.PangoLanguage)(gextras.StructNative(unsafe.Pointer(language)))
	runtime.SetFinalizer(gextras.StructIntern(unsafe.Pointer(language)), nil)

	return cret
}

//export _gotk4_pango1_FontsetClass_get_metrics
func _gotk4_pango1_FontsetClass_get_metrics(arg0 *C.PangoFontset) (cret *C.PangoFontMetrics) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FontsetOverrides](instance0)
	if overrides.Metrics == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FontsetOverrides.Metrics, got none")
	}

	fontMetrics := overrides.Metrics()

	var _ *FontMetrics

	cret = (*C.PangoFontMetrics)(gextras.StructNative(unsafe.Pointer(fontMetrics)))

	return cret
}

//export _gotk4_pango1_RendererClass_begin
func _gotk4_pango1_RendererClass_begin(arg0 *C.PangoRenderer) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RendererOverrides](instance0)
	if overrides.Begin == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RendererOverrides.Begin, got none")
	}

	overrides.Begin()
}

//export _gotk4_pango1_RendererClass_draw_error_underline
func _gotk4_pango1_RendererClass_draw_error_underline(arg0 *C.PangoRenderer, arg1 C.int, arg2 C.int, arg3 C.int, arg4 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RendererOverrides](instance0)
	if overrides.DrawErrorUnderline == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RendererOverrides.DrawErrorUnderline, got none")
	}

	var _x int      // out
	var _y int      // out
	var _width int  // out
	var _height int // out

	_x = int(arg1)
	_y = int(arg2)
	_width = int(arg3)
	_height = int(arg4)

	overrides.DrawErrorUnderline(_x, _y, _width, _height)
}

//export _gotk4_pango1_RendererClass_draw_glyph
func _gotk4_pango1_RendererClass_draw_glyph(arg0 *C.PangoRenderer, arg1 *C.PangoFont, arg2 C.PangoGlyph, arg3 C.double, arg4 C.double) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RendererOverrides](instance0)
	if overrides.DrawGlyph == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RendererOverrides.DrawGlyph, got none")
	}

	var _font Fonter // out
	var _glyph Glyph // out
	var _x float64   // out
	var _y float64   // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type pango.Fonter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Fonter)
			return ok
		})
		rv, ok := casted.(Fonter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching pango.Fonter")
		}
		_font = rv
	}
	_glyph = Glyph(arg2)
	_x = float64(arg3)
	_y = float64(arg4)

	overrides.DrawGlyph(_font, _glyph, _x, _y)
}

//export _gotk4_pango1_RendererClass_draw_glyph_item
func _gotk4_pango1_RendererClass_draw_glyph_item(arg0 *C.PangoRenderer, arg1 *C.char, arg2 *C.PangoGlyphItem, arg3 C.int, arg4 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RendererOverrides](instance0)
	if overrides.DrawGlyphItem == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RendererOverrides.DrawGlyphItem, got none")
	}

	var _text string          // out
	var _glyphItem *GlyphItem // out
	var _x int                // out
	var _y int                // out

	if arg1 != nil {
		_text = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	}
	_glyphItem = (*GlyphItem)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_x = int(arg3)
	_y = int(arg4)

	overrides.DrawGlyphItem(_text, _glyphItem, _x, _y)
}

//export _gotk4_pango1_RendererClass_draw_glyphs
func _gotk4_pango1_RendererClass_draw_glyphs(arg0 *C.PangoRenderer, arg1 *C.PangoFont, arg2 *C.PangoGlyphString, arg3 C.int, arg4 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RendererOverrides](instance0)
	if overrides.DrawGlyphs == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RendererOverrides.DrawGlyphs, got none")
	}

	var _font Fonter         // out
	var _glyphs *GlyphString // out
	var _x int               // out
	var _y int               // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type pango.Fonter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Fonter)
			return ok
		})
		rv, ok := casted.(Fonter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching pango.Fonter")
		}
		_font = rv
	}
	_glyphs = (*GlyphString)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_x = int(arg3)
	_y = int(arg4)

	overrides.DrawGlyphs(_font, _glyphs, _x, _y)
}

//export _gotk4_pango1_RendererClass_draw_rectangle
func _gotk4_pango1_RendererClass_draw_rectangle(arg0 *C.PangoRenderer, arg1 C.PangoRenderPart, arg2 C.int, arg3 C.int, arg4 C.int, arg5 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RendererOverrides](instance0)
	if overrides.DrawRectangle == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RendererOverrides.DrawRectangle, got none")
	}

	var _part RenderPart // out
	var _x int           // out
	var _y int           // out
	var _width int       // out
	var _height int      // out

	_part = RenderPart(arg1)
	_x = int(arg2)
	_y = int(arg3)
	_width = int(arg4)
	_height = int(arg5)

	overrides.DrawRectangle(_part, _x, _y, _width, _height)
}

//export _gotk4_pango1_RendererClass_draw_shape
func _gotk4_pango1_RendererClass_draw_shape(arg0 *C.PangoRenderer, arg1 *C.PangoAttrShape, arg2 C.int, arg3 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RendererOverrides](instance0)
	if overrides.DrawShape == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RendererOverrides.DrawShape, got none")
	}

	var _attr *AttrShape // out
	var _x int           // out
	var _y int           // out

	_attr = (*AttrShape)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_x = int(arg2)
	_y = int(arg3)

	overrides.DrawShape(_attr, _x, _y)
}

//export _gotk4_pango1_RendererClass_draw_trapezoid
func _gotk4_pango1_RendererClass_draw_trapezoid(arg0 *C.PangoRenderer, arg1 C.PangoRenderPart, arg2 C.double, arg3 C.double, arg4 C.double, arg5 C.double, arg6 C.double, arg7 C.double) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RendererOverrides](instance0)
	if overrides.DrawTrapezoid == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RendererOverrides.DrawTrapezoid, got none")
	}

	var _part RenderPart // out
	var _y1 float64      // out
	var _x11 float64     // out
	var _x21 float64     // out
	var _y2 float64      // out
	var _x12 float64     // out
	var _x22 float64     // out

	_part = RenderPart(arg1)
	_y1 = float64(arg2)
	_x11 = float64(arg3)
	_x21 = float64(arg4)
	_y2 = float64(arg5)
	_x12 = float64(arg6)
	_x22 = float64(arg7)

	overrides.DrawTrapezoid(_part, _y1, _x11, _x21, _y2, _x12, _x22)
}

//export _gotk4_pango1_RendererClass_end
func _gotk4_pango1_RendererClass_end(arg0 *C.PangoRenderer) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RendererOverrides](instance0)
	if overrides.End == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RendererOverrides.End, got none")
	}

	overrides.End()
}

//export _gotk4_pango1_RendererClass_part_changed
func _gotk4_pango1_RendererClass_part_changed(arg0 *C.PangoRenderer, arg1 C.PangoRenderPart) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RendererOverrides](instance0)
	if overrides.PartChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RendererOverrides.PartChanged, got none")
	}

	var _part RenderPart // out

	_part = RenderPart(arg1)

	overrides.PartChanged(_part)
}

//export _gotk4_pango1_RendererClass_prepare_run
func _gotk4_pango1_RendererClass_prepare_run(arg0 *C.PangoRenderer, arg1 *C.PangoLayoutRun) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RendererOverrides](instance0)
	if overrides.PrepareRun == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RendererOverrides.PrepareRun, got none")
	}

	var _run *LayoutRun // out

	_run = (*GlyphItem)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	type _ = *LayoutRun
	type _ = *GlyphItem

	overrides.PrepareRun(_run)
}
