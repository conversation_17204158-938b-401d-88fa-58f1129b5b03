// Code generated by girgen. DO NOT EDIT.

package gio

import (
	"context"
	"runtime"
	"unsafe"

	"github.com/diamondburned/gotk4/pkg/core/gbox"
	"github.com/diamondburned/gotk4/pkg/core/gcancel"
	"github.com/diamondburned/gotk4/pkg/core/gerror"
	"github.com/diamondburned/gotk4/pkg/core/gextras"
	coreglib "github.com/diamondburned/gotk4/pkg/core/glib"
	"github.com/diamondburned/gotk4/pkg/glib/v2"
)

// #include <stdlib.h>
// #include <gio/gio.h>
// #include <glib-object.h>
import "C"

//export _gotk4_gio2_AsyncReadyCallback
func _gotk4_gio2_AsyncReadyCallback(arg1 *C.GObject, arg2 *C.GAsyncResult, arg3 C.gpointer) {
	var fn AsyncReadyCallback
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(AsyncReadyCallback)
	}

	var _res AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_res = rv
	}

	fn(_res)
}

//export _gotk4_gio2_BusAcquiredCallback
func _gotk4_gio2_BusAcquiredCallback(arg1 *C.GDBusConnection, arg2 *C.gchar, arg3 C.gpointer) {
	var fn BusAcquiredCallback
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(BusAcquiredCallback)
	}

	var _connection *DBusConnection // out
	var _name string                // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_name = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	fn(_connection, _name)
}

//export _gotk4_gio2_BusNameAcquiredCallback
func _gotk4_gio2_BusNameAcquiredCallback(arg1 *C.GDBusConnection, arg2 *C.gchar, arg3 C.gpointer) {
	var fn BusNameAcquiredCallback
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(BusNameAcquiredCallback)
	}

	var _connection *DBusConnection // out
	var _name string                // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_name = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	fn(_connection, _name)
}

//export _gotk4_gio2_BusNameAppearedCallback
func _gotk4_gio2_BusNameAppearedCallback(arg1 *C.GDBusConnection, arg2 *C.gchar, arg3 *C.gchar, arg4 C.gpointer) {
	var fn BusNameAppearedCallback
	{
		v := gbox.Get(uintptr(arg4))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(BusNameAppearedCallback)
	}

	var _connection *DBusConnection // out
	var _name string                // out
	var _nameOwner string           // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_name = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_nameOwner = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))

	fn(_connection, _name, _nameOwner)
}

//export _gotk4_gio2_BusNameLostCallback
func _gotk4_gio2_BusNameLostCallback(arg1 *C.GDBusConnection, arg2 *C.gchar, arg3 C.gpointer) {
	var fn BusNameLostCallback
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(BusNameLostCallback)
	}

	var _connection *DBusConnection // out
	var _name string                // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_name = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	fn(_connection, _name)
}

//export _gotk4_gio2_BusNameVanishedCallback
func _gotk4_gio2_BusNameVanishedCallback(arg1 *C.GDBusConnection, arg2 *C.gchar, arg3 C.gpointer) {
	var fn BusNameVanishedCallback
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(BusNameVanishedCallback)
	}

	var _connection *DBusConnection // out
	var _name string                // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_name = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	fn(_connection, _name)
}

//export _gotk4_gio2_DBusInterfaceGetPropertyFunc
func _gotk4_gio2_DBusInterfaceGetPropertyFunc(arg1 *C.GDBusConnection, arg2 *C.gchar, arg3 *C.gchar, arg4 *C.gchar, arg5 *C.gchar, arg6 **C.GError, arg7 C.gpointer) (cret *C.GVariant) {
	var fn DBusInterfaceGetPropertyFunc
	{
		v := gbox.Get(uintptr(arg7))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(DBusInterfaceGetPropertyFunc)
	}

	var _connection *DBusConnection // out
	var _sender string              // out
	var _objectPath string          // out
	var _interfaceName string       // out
	var _propertyName string        // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_sender = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_objectPath = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))
	_interfaceName = C.GoString((*C.gchar)(unsafe.Pointer(arg4)))
	_propertyName = C.GoString((*C.gchar)(unsafe.Pointer(arg5)))

	err, variant := fn(_connection, _sender, _objectPath, _interfaceName, _propertyName)

	var _ error
	var _ *glib.Variant

	if err != nil && arg6 != nil {
		*arg6 = (*C.GError)(gerror.New(err))
	}
	cret = (*C.GVariant)(gextras.StructNative(unsafe.Pointer(variant)))

	return cret
}

//export _gotk4_gio2_DBusInterfaceMethodCallFunc
func _gotk4_gio2_DBusInterfaceMethodCallFunc(arg1 *C.GDBusConnection, arg2 *C.gchar, arg3 *C.gchar, arg4 *C.gchar, arg5 *C.gchar, arg6 *C.GVariant, arg7 *C.GDBusMethodInvocation, arg8 C.gpointer) {
	var fn DBusInterfaceMethodCallFunc
	{
		v := gbox.Get(uintptr(arg8))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(DBusInterfaceMethodCallFunc)
	}

	var _connection *DBusConnection       // out
	var _sender string                    // out
	var _objectPath string                // out
	var _interfaceName string             // out
	var _methodName string                // out
	var _parameters *glib.Variant         // out
	var _invocation *DBusMethodInvocation // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_sender = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_objectPath = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))
	_interfaceName = C.GoString((*C.gchar)(unsafe.Pointer(arg4)))
	_methodName = C.GoString((*C.gchar)(unsafe.Pointer(arg5)))
	_parameters = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg6)))
	C.g_variant_ref(arg6)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_parameters)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)
	_invocation = wrapDBusMethodInvocation(coreglib.AssumeOwnership(unsafe.Pointer(arg7)))

	fn(_connection, _sender, _objectPath, _interfaceName, _methodName, _parameters, _invocation)
}

//export _gotk4_gio2_DBusInterfaceSetPropertyFunc
func _gotk4_gio2_DBusInterfaceSetPropertyFunc(arg1 *C.GDBusConnection, arg2 *C.gchar, arg3 *C.gchar, arg4 *C.gchar, arg5 *C.gchar, arg6 *C.GVariant, arg7 **C.GError, arg8 C.gpointer) (cret C.gboolean) {
	var fn DBusInterfaceSetPropertyFunc
	{
		v := gbox.Get(uintptr(arg8))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(DBusInterfaceSetPropertyFunc)
	}

	var _connection *DBusConnection // out
	var _sender string              // out
	var _objectPath string          // out
	var _interfaceName string       // out
	var _propertyName string        // out
	var _value *glib.Variant        // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_sender = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_objectPath = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))
	_interfaceName = C.GoString((*C.gchar)(unsafe.Pointer(arg4)))
	_propertyName = C.GoString((*C.gchar)(unsafe.Pointer(arg5)))
	_value = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg6)))
	C.g_variant_ref(arg6)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_value)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)

	err, ok := fn(_connection, _sender, _objectPath, _interfaceName, _propertyName, _value)

	var _ error
	var _ bool

	if err != nil && arg7 != nil {
		*arg7 = (*C.GError)(gerror.New(err))
	}
	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_DBusMessageFilterFunction
func _gotk4_gio2_DBusMessageFilterFunction(arg1 *C.GDBusConnection, arg2 *C.GDBusMessage, arg3 C.gboolean, arg4 C.gpointer) (cret *C.GDBusMessage) {
	var fn DBusMessageFilterFunction
	{
		v := gbox.Get(uintptr(arg4))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(DBusMessageFilterFunction)
	}

	var _connection *DBusConnection // out
	var _message *DBusMessage       // out
	var _incoming bool              // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_message = wrapDBusMessage(coreglib.AssumeOwnership(unsafe.Pointer(arg2)))
	if arg3 != 0 {
		_incoming = true
	}

	dBusMessage := fn(_connection, _message, _incoming)

	var _ *DBusMessage

	if dBusMessage != nil {
		cret = (*C.GDBusMessage)(unsafe.Pointer(coreglib.InternObject(dBusMessage).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(dBusMessage).Native()))
	}

	return cret
}

//export _gotk4_gio2_DBusSignalCallback
func _gotk4_gio2_DBusSignalCallback(arg1 *C.GDBusConnection, arg2 *C.gchar, arg3 *C.gchar, arg4 *C.gchar, arg5 *C.gchar, arg6 *C.GVariant, arg7 C.gpointer) {
	var fn DBusSignalCallback
	{
		v := gbox.Get(uintptr(arg7))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(DBusSignalCallback)
	}

	var _connection *DBusConnection // out
	var _senderName string          // out
	var _objectPath string          // out
	var _interfaceName string       // out
	var _signalName string          // out
	var _parameters *glib.Variant   // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))
	if arg2 != nil {
		_senderName = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	}
	_objectPath = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))
	_interfaceName = C.GoString((*C.gchar)(unsafe.Pointer(arg4)))
	_signalName = C.GoString((*C.gchar)(unsafe.Pointer(arg5)))
	_parameters = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg6)))
	C.g_variant_ref(arg6)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_parameters)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)

	fn(_connection, _senderName, _objectPath, _interfaceName, _signalName, _parameters)
}

//export _gotk4_gio2_DBusSubtreeDispatchFunc
func _gotk4_gio2_DBusSubtreeDispatchFunc(arg1 *C.GDBusConnection, arg2 *C.gchar, arg3 *C.gchar, arg4 *C.gchar, arg5 *C.gchar, arg6 *C.gpointer, arg7 C.gpointer) (cret *C.GDBusInterfaceVTable) {
	var fn DBusSubtreeDispatchFunc
	{
		v := gbox.Get(uintptr(arg7))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(DBusSubtreeDispatchFunc)
	}

	var _connection *DBusConnection // out
	var _sender string              // out
	var _objectPath string          // out
	var _interfaceName string       // out
	var _node string                // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_sender = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_objectPath = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))
	_interfaceName = C.GoString((*C.gchar)(unsafe.Pointer(arg4)))
	_node = C.GoString((*C.gchar)(unsafe.Pointer(arg5)))

	outUserData, dBusInterfaceVTable := fn(_connection, _sender, _objectPath, _interfaceName, _node)

	var _ unsafe.Pointer
	var _ *DBusInterfaceVTable

	*arg6 = (C.gpointer)(unsafe.Pointer(outUserData))
	if dBusInterfaceVTable != nil {
		cret = (*C.GDBusInterfaceVTable)(gextras.StructNative(unsafe.Pointer(dBusInterfaceVTable)))
	}

	return cret
}

//export _gotk4_gio2_DBusSubtreeEnumerateFunc
func _gotk4_gio2_DBusSubtreeEnumerateFunc(arg1 *C.GDBusConnection, arg2 *C.gchar, arg3 *C.gchar, arg4 C.gpointer) (cret **C.gchar) {
	var fn DBusSubtreeEnumerateFunc
	{
		v := gbox.Get(uintptr(arg4))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(DBusSubtreeEnumerateFunc)
	}

	var _connection *DBusConnection // out
	var _sender string              // out
	var _objectPath string          // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_sender = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_objectPath = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))

	utf8s := fn(_connection, _sender, _objectPath)

	var _ []string

	{
		cret = (**C.gchar)(C.calloc(C.size_t((len(utf8s) + 1)), C.size_t(unsafe.Sizeof(uint(0)))))
		{
			out := unsafe.Slice(cret, len(utf8s)+1)
			var zero *C.gchar
			out[len(utf8s)] = zero
			for i := range utf8s {
				out[i] = (*C.gchar)(unsafe.Pointer(C.CString(utf8s[i])))
			}
		}
	}

	return cret
}

//export _gotk4_gio2_DBusSubtreeIntrospectFunc
func _gotk4_gio2_DBusSubtreeIntrospectFunc(arg1 *C.GDBusConnection, arg2 *C.gchar, arg3 *C.gchar, arg4 *C.gchar, arg5 C.gpointer) (cret **C.GDBusInterfaceInfo) {
	var fn DBusSubtreeIntrospectFunc
	{
		v := gbox.Get(uintptr(arg5))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(DBusSubtreeIntrospectFunc)
	}

	var _connection *DBusConnection // out
	var _sender string              // out
	var _objectPath string          // out
	var _node string                // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_sender = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_objectPath = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))
	_node = C.GoString((*C.gchar)(unsafe.Pointer(arg4)))

	dBusInterfaceInfos := fn(_connection, _sender, _objectPath, _node)

	var _ []*DBusInterfaceInfo

	if dBusInterfaceInfos != nil {
		{
			cret = (**C.GDBusInterfaceInfo)(C.calloc(C.size_t((len(dBusInterfaceInfos) + 1)), C.size_t(unsafe.Sizeof(uint(0)))))
			{
				out := unsafe.Slice(cret, len(dBusInterfaceInfos)+1)
				var zero *C.GDBusInterfaceInfo
				out[len(dBusInterfaceInfos)] = zero
				for i := range dBusInterfaceInfos {
					out[i] = (*C.GDBusInterfaceInfo)(gextras.StructNative(unsafe.Pointer(dBusInterfaceInfos[i])))
				}
			}
		}
	}

	return cret
}

//export _gotk4_gio2_SettingsBindGetMapping
func _gotk4_gio2_SettingsBindGetMapping(arg1 *C.GValue, arg2 *C.GVariant, arg3 C.gpointer) (cret C.gboolean) {
	var fn SettingsBindGetMapping
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(SettingsBindGetMapping)
	}

	var _value *coreglib.Value // out
	var _variant *glib.Variant // out

	_value = coreglib.ValueFromNative(unsafe.Pointer(arg1))
	_variant = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	C.g_variant_ref(arg2)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_variant)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)

	ok := fn(_value, _variant)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_SettingsBindSetMapping
func _gotk4_gio2_SettingsBindSetMapping(arg1 *C.GValue, arg2 *C.GVariantType, arg3 C.gpointer) (cret *C.GVariant) {
	var fn SettingsBindSetMapping
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(SettingsBindSetMapping)
	}

	var _value *coreglib.Value          // out
	var _expectedType *glib.VariantType // out

	_value = coreglib.ValueFromNative(unsafe.Pointer(arg1))
	_expectedType = (*glib.VariantType)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	variant := fn(_value, _expectedType)

	var _ *glib.Variant

	cret = (*C.GVariant)(gextras.StructNative(unsafe.Pointer(variant)))

	return cret
}

//export _gotk4_gio2_SettingsGetMapping
func _gotk4_gio2_SettingsGetMapping(arg1 *C.GVariant, arg2 *C.gpointer, arg3 C.gpointer) (cret C.gboolean) {
	var fn SettingsGetMapping
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(SettingsGetMapping)
	}

	var _value *glib.Variant // out

	_value = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	C.g_variant_ref(arg1)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_value)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)

	result, ok := fn(_value)

	var _ unsafe.Pointer
	var _ bool

	*arg2 = (C.gpointer)(unsafe.Pointer(result))
	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_VFSFileLookupFunc
func _gotk4_gio2_VFSFileLookupFunc(arg1 *C.GVfs, arg2 *C.char, arg3 C.gpointer) (cret *C.GFile) {
	var fn VFSFileLookupFunc
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(VFSFileLookupFunc)
	}

	var _vfs *VFS          // out
	var _identifier string // out

	_vfs = wrapVFS(coreglib.Take(unsafe.Pointer(arg1)))
	_identifier = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	file := fn(_vfs, _identifier)

	var _ *File

	cret = (*C.GFile)(unsafe.Pointer(coreglib.InternObject(file).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(file).Native()))

	return cret
}

//export _gotk4_gio2_ActionGroup_ConnectActionAdded
func _gotk4_gio2_ActionGroup_ConnectActionAdded(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(actionName string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(actionName string))
	}

	var _actionName string // out

	_actionName = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_actionName)
}

//export _gotk4_gio2_ActionGroup_ConnectActionEnabledChanged
func _gotk4_gio2_ActionGroup_ConnectActionEnabledChanged(arg0 C.gpointer, arg1 *C.gchar, arg2 C.gboolean, arg3 C.guintptr) {
	var f func(actionName string, enabled bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(actionName string, enabled bool))
	}

	var _actionName string // out
	var _enabled bool      // out

	_actionName = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	if arg2 != 0 {
		_enabled = true
	}

	f(_actionName, _enabled)
}

//export _gotk4_gio2_ActionGroup_ConnectActionRemoved
func _gotk4_gio2_ActionGroup_ConnectActionRemoved(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(actionName string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(actionName string))
	}

	var _actionName string // out

	_actionName = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_actionName)
}

//export _gotk4_gio2_ActionGroup_ConnectActionStateChanged
func _gotk4_gio2_ActionGroup_ConnectActionStateChanged(arg0 C.gpointer, arg1 *C.gchar, arg2 *C.GVariant, arg3 C.guintptr) {
	var f func(actionName string, value *glib.Variant)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(actionName string, value *glib.Variant))
	}

	var _actionName string   // out
	var _value *glib.Variant // out

	_actionName = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	_value = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	C.g_variant_ref(arg2)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_value)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)

	f(_actionName, _value)
}

//export _gotk4_gio2_DBusObject_ConnectInterfaceAdded
func _gotk4_gio2_DBusObject_ConnectInterfaceAdded(arg0 C.gpointer, arg1 *C.GDBusInterface, arg2 C.guintptr) {
	var f func(iface DBusInterfacer)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(iface DBusInterfacer))
	}

	var _iface DBusInterfacer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.DBusInterfacer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(DBusInterfacer)
			return ok
		})
		rv, ok := casted.(DBusInterfacer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.DBusInterfacer")
		}
		_iface = rv
	}

	f(_iface)
}

//export _gotk4_gio2_DBusObject_ConnectInterfaceRemoved
func _gotk4_gio2_DBusObject_ConnectInterfaceRemoved(arg0 C.gpointer, arg1 *C.GDBusInterface, arg2 C.guintptr) {
	var f func(iface DBusInterfacer)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(iface DBusInterfacer))
	}

	var _iface DBusInterfacer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.DBusInterfacer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(DBusInterfacer)
			return ok
		})
		rv, ok := casted.(DBusInterfacer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.DBusInterfacer")
		}
		_iface = rv
	}

	f(_iface)
}

//export _gotk4_gio2_DBusObjectManager_ConnectInterfaceAdded
func _gotk4_gio2_DBusObjectManager_ConnectInterfaceAdded(arg0 C.gpointer, arg1 *C.GDBusObject, arg2 *C.GDBusInterface, arg3 C.guintptr) {
	var f func(object DBusObjector, iface DBusInterfacer)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object DBusObjector, iface DBusInterfacer))
	}

	var _object DBusObjector  // out
	var _iface DBusInterfacer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.DBusObjector is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(DBusObjector)
			return ok
		})
		rv, ok := casted.(DBusObjector)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.DBusObjector")
		}
		_object = rv
	}
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gio.DBusInterfacer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(DBusInterfacer)
			return ok
		})
		rv, ok := casted.(DBusInterfacer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.DBusInterfacer")
		}
		_iface = rv
	}

	f(_object, _iface)
}

//export _gotk4_gio2_DBusObjectManager_ConnectInterfaceRemoved
func _gotk4_gio2_DBusObjectManager_ConnectInterfaceRemoved(arg0 C.gpointer, arg1 *C.GDBusObject, arg2 *C.GDBusInterface, arg3 C.guintptr) {
	var f func(object DBusObjector, iface DBusInterfacer)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object DBusObjector, iface DBusInterfacer))
	}

	var _object DBusObjector  // out
	var _iface DBusInterfacer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.DBusObjector is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(DBusObjector)
			return ok
		})
		rv, ok := casted.(DBusObjector)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.DBusObjector")
		}
		_object = rv
	}
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gio.DBusInterfacer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(DBusInterfacer)
			return ok
		})
		rv, ok := casted.(DBusInterfacer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.DBusInterfacer")
		}
		_iface = rv
	}

	f(_object, _iface)
}

//export _gotk4_gio2_DBusObjectManager_ConnectObjectAdded
func _gotk4_gio2_DBusObjectManager_ConnectObjectAdded(arg0 C.gpointer, arg1 *C.GDBusObject, arg2 C.guintptr) {
	var f func(object DBusObjector)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object DBusObjector))
	}

	var _object DBusObjector // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.DBusObjector is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(DBusObjector)
			return ok
		})
		rv, ok := casted.(DBusObjector)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.DBusObjector")
		}
		_object = rv
	}

	f(_object)
}

//export _gotk4_gio2_DBusObjectManager_ConnectObjectRemoved
func _gotk4_gio2_DBusObjectManager_ConnectObjectRemoved(arg0 C.gpointer, arg1 *C.GDBusObject, arg2 C.guintptr) {
	var f func(object DBusObjector)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object DBusObjector))
	}

	var _object DBusObjector // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.DBusObjector is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(DBusObjector)
			return ok
		})
		rv, ok := casted.(DBusObjector)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.DBusObjector")
		}
		_object = rv
	}

	f(_object)
}

//export _gotk4_gio2_Drive_ConnectChanged
func _gotk4_gio2_Drive_ConnectChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_Drive_ConnectDisconnected
func _gotk4_gio2_Drive_ConnectDisconnected(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_Drive_ConnectEjectButton
func _gotk4_gio2_Drive_ConnectEjectButton(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_Drive_ConnectStopButton
func _gotk4_gio2_Drive_ConnectStopButton(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_DtlsConnection_ConnectAcceptCertificate
func _gotk4_gio2_DtlsConnection_ConnectAcceptCertificate(arg0 C.gpointer, arg1 *C.GTlsCertificate, arg2 C.GTlsCertificateFlags, arg3 C.guintptr) (cret C.gboolean) {
	var f func(peerCert TLSCertificater, errors TLSCertificateFlags) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(peerCert TLSCertificater, errors TLSCertificateFlags) (ok bool))
	}

	var _peerCert TLSCertificater   // out
	var _errors TLSCertificateFlags // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.TLSCertificater is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TLSCertificater)
			return ok
		})
		rv, ok := casted.(TLSCertificater)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.TLSCertificater")
		}
		_peerCert = rv
	}
	_errors = TLSCertificateFlags(arg2)

	ok := f(_peerCert, _errors)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_ListModel_ConnectItemsChanged
func _gotk4_gio2_ListModel_ConnectItemsChanged(arg0 C.gpointer, arg1 C.guint, arg2 C.guint, arg3 C.guint, arg4 C.guintptr) {
	var f func(position, removed, added uint)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(position, removed, added uint))
	}

	var _position uint // out
	var _removed uint  // out
	var _added uint    // out

	_position = uint(arg1)
	_removed = uint(arg2)
	_added = uint(arg3)

	f(_position, _removed, _added)
}

//export _gotk4_gio2_MemoryMonitor_ConnectLowMemoryWarning
func _gotk4_gio2_MemoryMonitor_ConnectLowMemoryWarning(arg0 C.gpointer, arg1 C.GMemoryMonitorWarningLevel, arg2 C.guintptr) {
	var f func(level MemoryMonitorWarningLevel)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(level MemoryMonitorWarningLevel))
	}

	var _level MemoryMonitorWarningLevel // out

	_level = MemoryMonitorWarningLevel(arg1)

	f(_level)
}

//export _gotk4_gio2_Mount_ConnectChanged
func _gotk4_gio2_Mount_ConnectChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_Mount_ConnectPreUnmount
func _gotk4_gio2_Mount_ConnectPreUnmount(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_Mount_ConnectUnmounted
func _gotk4_gio2_Mount_ConnectUnmounted(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_NetworkMonitor_ConnectNetworkChanged
func _gotk4_gio2_NetworkMonitor_ConnectNetworkChanged(arg0 C.gpointer, arg1 C.gboolean, arg2 C.guintptr) {
	var f func(networkAvailable bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(networkAvailable bool))
	}

	var _networkAvailable bool // out

	if arg1 != 0 {
		_networkAvailable = true
	}

	f(_networkAvailable)
}

//export _gotk4_gio2_Volume_ConnectChanged
func _gotk4_gio2_Volume_ConnectChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_Volume_ConnectRemoved
func _gotk4_gio2_Volume_ConnectRemoved(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_AppInfoMonitor_ConnectChanged
func _gotk4_gio2_AppInfoMonitor_ConnectChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_AppLaunchContextClass_get_display
func _gotk4_gio2_AppLaunchContextClass_get_display(arg0 *C.GAppLaunchContext, arg1 *C.GAppInfo, arg2 *C.GList) (cret *C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[AppLaunchContextOverrides](instance0)
	if overrides.Display == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected AppLaunchContextOverrides.Display, got none")
	}

	var _info AppInfor // out
	var _files []Filer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AppInfor is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AppInfor)
			return ok
		})
		rv, ok := casted.(AppInfor)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AppInfor")
		}
		_info = rv
	}
	_files = make([]Filer, 0, gextras.ListSize(unsafe.Pointer(arg2)))
	gextras.MoveList(unsafe.Pointer(arg2), false, func(v unsafe.Pointer) {
		src := (*C.GFile)(v)
		var dst Filer // out
		{
			objptr := unsafe.Pointer(src)
			if objptr == nil {
				panic("object of type gio.Filer is nil")
			}

			object := coreglib.Take(objptr)
			casted := object.WalkCast(func(obj coreglib.Objector) bool {
				_, ok := obj.(Filer)
				return ok
			})
			rv, ok := casted.(Filer)
			if !ok {
				panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Filer")
			}
			dst = rv
		}
		_files = append(_files, dst)
	})

	utf8 := overrides.Display(_info, _files)

	var _ string

	if utf8 != "" {
		cret = (*C.char)(unsafe.Pointer(C.CString(utf8)))
	}

	return cret
}

//export _gotk4_gio2_AppLaunchContextClass_get_startup_notify_id
func _gotk4_gio2_AppLaunchContextClass_get_startup_notify_id(arg0 *C.GAppLaunchContext, arg1 *C.GAppInfo, arg2 *C.GList) (cret *C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[AppLaunchContextOverrides](instance0)
	if overrides.StartupNotifyID == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected AppLaunchContextOverrides.StartupNotifyID, got none")
	}

	var _info AppInfor // out
	var _files []Filer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AppInfor is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AppInfor)
			return ok
		})
		rv, ok := casted.(AppInfor)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AppInfor")
		}
		_info = rv
	}
	_files = make([]Filer, 0, gextras.ListSize(unsafe.Pointer(arg2)))
	gextras.MoveList(unsafe.Pointer(arg2), false, func(v unsafe.Pointer) {
		src := (*C.GFile)(v)
		var dst Filer // out
		{
			objptr := unsafe.Pointer(src)
			if objptr == nil {
				panic("object of type gio.Filer is nil")
			}

			object := coreglib.Take(objptr)
			casted := object.WalkCast(func(obj coreglib.Objector) bool {
				_, ok := obj.(Filer)
				return ok
			})
			rv, ok := casted.(Filer)
			if !ok {
				panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Filer")
			}
			dst = rv
		}
		_files = append(_files, dst)
	})

	utf8 := overrides.StartupNotifyID(_info, _files)

	var _ string

	if utf8 != "" {
		cret = (*C.char)(unsafe.Pointer(C.CString(utf8)))
	}

	return cret
}

//export _gotk4_gio2_AppLaunchContextClass_launch_failed
func _gotk4_gio2_AppLaunchContextClass_launch_failed(arg0 *C.GAppLaunchContext, arg1 *C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[AppLaunchContextOverrides](instance0)
	if overrides.LaunchFailed == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected AppLaunchContextOverrides.LaunchFailed, got none")
	}

	var _startupNotifyId string // out

	_startupNotifyId = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	overrides.LaunchFailed(_startupNotifyId)
}

//export _gotk4_gio2_AppLaunchContextClass_launch_started
func _gotk4_gio2_AppLaunchContextClass_launch_started(arg0 *C.GAppLaunchContext, arg1 *C.GAppInfo, arg2 *C.GVariant) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[AppLaunchContextOverrides](instance0)
	if overrides.LaunchStarted == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected AppLaunchContextOverrides.LaunchStarted, got none")
	}

	var _info AppInfor              // out
	var _platformData *glib.Variant // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AppInfor is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AppInfor)
			return ok
		})
		rv, ok := casted.(AppInfor)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AppInfor")
		}
		_info = rv
	}
	_platformData = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	C.g_variant_ref(arg2)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_platformData)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)

	overrides.LaunchStarted(_info, _platformData)
}

//export _gotk4_gio2_AppLaunchContextClass_launched
func _gotk4_gio2_AppLaunchContextClass_launched(arg0 *C.GAppLaunchContext, arg1 *C.GAppInfo, arg2 *C.GVariant) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[AppLaunchContextOverrides](instance0)
	if overrides.Launched == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected AppLaunchContextOverrides.Launched, got none")
	}

	var _info AppInfor              // out
	var _platformData *glib.Variant // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AppInfor is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AppInfor)
			return ok
		})
		rv, ok := casted.(AppInfor)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AppInfor")
		}
		_info = rv
	}
	_platformData = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	C.g_variant_ref(arg2)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_platformData)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)

	overrides.Launched(_info, _platformData)
}

//export _gotk4_gio2_AppLaunchContext_ConnectLaunchFailed
func _gotk4_gio2_AppLaunchContext_ConnectLaunchFailed(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(startupNotifyId string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(startupNotifyId string))
	}

	var _startupNotifyId string // out

	_startupNotifyId = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_startupNotifyId)
}

//export _gotk4_gio2_AppLaunchContext_ConnectLaunchStarted
func _gotk4_gio2_AppLaunchContext_ConnectLaunchStarted(arg0 C.gpointer, arg1 *C.GAppInfo, arg2 *C.GVariant, arg3 C.guintptr) {
	var f func(info AppInfor, platformData *glib.Variant)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(info AppInfor, platformData *glib.Variant))
	}

	var _info AppInfor              // out
	var _platformData *glib.Variant // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AppInfor is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AppInfor)
			return ok
		})
		rv, ok := casted.(AppInfor)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AppInfor")
		}
		_info = rv
	}
	if arg2 != nil {
		_platformData = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg2)))
		C.g_variant_ref(arg2)
		runtime.SetFinalizer(
			gextras.StructIntern(unsafe.Pointer(_platformData)),
			func(intern *struct{ C unsafe.Pointer }) {
				C.g_variant_unref((*C.GVariant)(intern.C))
			},
		)
	}

	f(_info, _platformData)
}

//export _gotk4_gio2_AppLaunchContext_ConnectLaunched
func _gotk4_gio2_AppLaunchContext_ConnectLaunched(arg0 C.gpointer, arg1 *C.GAppInfo, arg2 *C.GVariant, arg3 C.guintptr) {
	var f func(info AppInfor, platformData *glib.Variant)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(info AppInfor, platformData *glib.Variant))
	}

	var _info AppInfor              // out
	var _platformData *glib.Variant // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AppInfor is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AppInfor)
			return ok
		})
		rv, ok := casted.(AppInfor)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AppInfor")
		}
		_info = rv
	}
	_platformData = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	C.g_variant_ref(arg2)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_platformData)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)

	f(_info, _platformData)
}

//export _gotk4_gio2_ApplicationClass_activate
func _gotk4_gio2_ApplicationClass_activate(arg0 *C.GApplication) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.Activate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.Activate, got none")
	}

	overrides.Activate()
}

//export _gotk4_gio2_ApplicationClass_add_platform_data
func _gotk4_gio2_ApplicationClass_add_platform_data(arg0 *C.GApplication, arg1 *C.GVariantBuilder) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.AddPlatformData == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.AddPlatformData, got none")
	}

	var _builder *glib.VariantBuilder // out

	_builder = (*glib.VariantBuilder)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	C.g_variant_builder_ref(arg1)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_builder)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_builder_unref((*C.GVariantBuilder)(intern.C))
		},
	)

	overrides.AddPlatformData(_builder)
}

//export _gotk4_gio2_ApplicationClass_after_emit
func _gotk4_gio2_ApplicationClass_after_emit(arg0 *C.GApplication, arg1 *C.GVariant) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.AfterEmit == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.AfterEmit, got none")
	}

	var _platformData *glib.Variant // out

	_platformData = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	C.g_variant_ref(arg1)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_platformData)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)

	overrides.AfterEmit(_platformData)
}

//export _gotk4_gio2_ApplicationClass_before_emit
func _gotk4_gio2_ApplicationClass_before_emit(arg0 *C.GApplication, arg1 *C.GVariant) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.BeforeEmit == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.BeforeEmit, got none")
	}

	var _platformData *glib.Variant // out

	_platformData = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	C.g_variant_ref(arg1)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_platformData)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)

	overrides.BeforeEmit(_platformData)
}

//export _gotk4_gio2_ApplicationClass_command_line
func _gotk4_gio2_ApplicationClass_command_line(arg0 *C.GApplication, arg1 *C.GApplicationCommandLine) (cret C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.CommandLine == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.CommandLine, got none")
	}

	var _commandLine *ApplicationCommandLine // out

	_commandLine = wrapApplicationCommandLine(coreglib.Take(unsafe.Pointer(arg1)))

	gint := overrides.CommandLine(_commandLine)

	var _ int

	cret = C.int(gint)

	return cret
}

//export _gotk4_gio2_ApplicationClass_dbus_register
func _gotk4_gio2_ApplicationClass_dbus_register(arg0 *C.GApplication, arg1 *C.GDBusConnection, arg2 *C.gchar, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.DBusRegister == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.DBusRegister, got none")
	}

	var _connection *DBusConnection // out
	var _objectPath string          // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_objectPath = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	_goerr := overrides.DBusRegister(_connection, _objectPath)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_ApplicationClass_dbus_unregister
func _gotk4_gio2_ApplicationClass_dbus_unregister(arg0 *C.GApplication, arg1 *C.GDBusConnection, arg2 *C.gchar) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.DBusUnregister == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.DBusUnregister, got none")
	}

	var _connection *DBusConnection // out
	var _objectPath string          // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_objectPath = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	overrides.DBusUnregister(_connection, _objectPath)
}

//export _gotk4_gio2_ApplicationClass_handle_local_options
func _gotk4_gio2_ApplicationClass_handle_local_options(arg0 *C.GApplication, arg1 *C.GVariantDict) (cret C.gint) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.HandleLocalOptions == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.HandleLocalOptions, got none")
	}

	var _options *glib.VariantDict // out

	_options = (*glib.VariantDict)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	C.g_variant_dict_ref(arg1)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_options)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_dict_unref((*C.GVariantDict)(intern.C))
		},
	)

	gint := overrides.HandleLocalOptions(_options)

	var _ int

	cret = C.gint(gint)

	return cret
}

//export _gotk4_gio2_ApplicationClass_name_lost
func _gotk4_gio2_ApplicationClass_name_lost(arg0 *C.GApplication) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.NameLost == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.NameLost, got none")
	}

	ok := overrides.NameLost()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_ApplicationClass_open
func _gotk4_gio2_ApplicationClass_open(arg0 *C.GApplication, arg1 **C.GFile, arg2 C.gint, arg3 *C.gchar) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.Open == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.Open, got none")
	}

	var _files []Filer // out
	var _hint string   // out

	{
		src := unsafe.Slice((**C.GFile)(arg1), arg2)
		_files = make([]Filer, arg2)
		for i := 0; i < int(arg2); i++ {
			{
				objptr := unsafe.Pointer(src[i])
				if objptr == nil {
					panic("object of type gio.Filer is nil")
				}

				object := coreglib.Take(objptr)
				casted := object.WalkCast(func(obj coreglib.Objector) bool {
					_, ok := obj.(Filer)
					return ok
				})
				rv, ok := casted.(Filer)
				if !ok {
					panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Filer")
				}
				_files[i] = rv
			}
		}
	}
	_hint = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))

	overrides.Open(_files, _hint)
}

//export _gotk4_gio2_ApplicationClass_quit_mainloop
func _gotk4_gio2_ApplicationClass_quit_mainloop(arg0 *C.GApplication) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.QuitMainloop == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.QuitMainloop, got none")
	}

	overrides.QuitMainloop()
}

//export _gotk4_gio2_ApplicationClass_run_mainloop
func _gotk4_gio2_ApplicationClass_run_mainloop(arg0 *C.GApplication) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.RunMainloop == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.RunMainloop, got none")
	}

	overrides.RunMainloop()
}

//export _gotk4_gio2_ApplicationClass_shutdown
func _gotk4_gio2_ApplicationClass_shutdown(arg0 *C.GApplication) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.Shutdown == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.Shutdown, got none")
	}

	overrides.Shutdown()
}

//export _gotk4_gio2_ApplicationClass_startup
func _gotk4_gio2_ApplicationClass_startup(arg0 *C.GApplication) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.Startup == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.Startup, got none")
	}

	overrides.Startup()
}

//export _gotk4_gio2_Application_ConnectActivate
func _gotk4_gio2_Application_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_Application_ConnectCommandLine
func _gotk4_gio2_Application_ConnectCommandLine(arg0 C.gpointer, arg1 *C.GApplicationCommandLine, arg2 C.guintptr) (cret C.gint) {
	var f func(commandLine *ApplicationCommandLine) (gint int)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(commandLine *ApplicationCommandLine) (gint int))
	}

	var _commandLine *ApplicationCommandLine // out

	_commandLine = wrapApplicationCommandLine(coreglib.Take(unsafe.Pointer(arg1)))

	gint := f(_commandLine)

	var _ int

	cret = C.gint(gint)

	return cret
}

//export _gotk4_gio2_Application_ConnectHandleLocalOptions
func _gotk4_gio2_Application_ConnectHandleLocalOptions(arg0 C.gpointer, arg1 *C.GVariantDict, arg2 C.guintptr) (cret C.gint) {
	var f func(options *glib.VariantDict) (gint int)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(options *glib.VariantDict) (gint int))
	}

	var _options *glib.VariantDict // out

	_options = (*glib.VariantDict)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	C.g_variant_dict_ref(arg1)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_options)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_dict_unref((*C.GVariantDict)(intern.C))
		},
	)

	gint := f(_options)

	var _ int

	cret = C.gint(gint)

	return cret
}

//export _gotk4_gio2_Application_ConnectNameLost
func _gotk4_gio2_Application_ConnectNameLost(arg0 C.gpointer, arg1 C.guintptr) (cret C.gboolean) {
	var f func() (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (ok bool))
	}

	ok := f()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_Application_ConnectOpen
func _gotk4_gio2_Application_ConnectOpen(arg0 C.gpointer, arg1 **C.GFile, arg2 C.gint, arg3 *C.gchar, arg4 C.guintptr) {
	var f func(files []Filer, hint string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(files []Filer, hint string))
	}

	var _files []Filer // out
	var _hint string   // out

	{
		src := unsafe.Slice((**C.GFile)(arg1), arg2)
		_files = make([]Filer, arg2)
		for i := 0; i < int(arg2); i++ {
			{
				objptr := unsafe.Pointer(src[i])
				if objptr == nil {
					panic("object of type gio.Filer is nil")
				}

				object := coreglib.Take(objptr)
				casted := object.WalkCast(func(obj coreglib.Objector) bool {
					_, ok := obj.(Filer)
					return ok
				})
				rv, ok := casted.(Filer)
				if !ok {
					panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Filer")
				}
				_files[i] = rv
			}
		}
	}
	_hint = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))

	f(_files, _hint)
}

//export _gotk4_gio2_Application_ConnectShutdown
func _gotk4_gio2_Application_ConnectShutdown(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_Application_ConnectStartup
func _gotk4_gio2_Application_ConnectStartup(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_ApplicationCommandLineClass_done
func _gotk4_gio2_ApplicationCommandLineClass_done(arg0 *C.GApplicationCommandLine) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationCommandLineOverrides](instance0)
	if overrides.Done == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationCommandLineOverrides.Done, got none")
	}

	overrides.Done()
}

//export _gotk4_gio2_ApplicationCommandLineClass_get_stdin
func _gotk4_gio2_ApplicationCommandLineClass_get_stdin(arg0 *C.GApplicationCommandLine) (cret *C.GInputStream) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationCommandLineOverrides](instance0)
	if overrides.Stdin == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationCommandLineOverrides.Stdin, got none")
	}

	inputStream := overrides.Stdin()

	var _ InputStreamer

	if inputStream != nil {
		cret = (*C.GInputStream)(unsafe.Pointer(coreglib.InternObject(inputStream).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(inputStream).Native()))
	}

	return cret
}

//export _gotk4_gio2_ApplicationCommandLineClass_print_literal
func _gotk4_gio2_ApplicationCommandLineClass_print_literal(arg0 *C.GApplicationCommandLine, arg1 *C.gchar) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationCommandLineOverrides](instance0)
	if overrides.PrintLiteral == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationCommandLineOverrides.PrintLiteral, got none")
	}

	var _message string // out

	_message = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	overrides.PrintLiteral(_message)
}

//export _gotk4_gio2_ApplicationCommandLineClass_printerr_literal
func _gotk4_gio2_ApplicationCommandLineClass_printerr_literal(arg0 *C.GApplicationCommandLine, arg1 *C.gchar) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationCommandLineOverrides](instance0)
	if overrides.PrinterrLiteral == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationCommandLineOverrides.PrinterrLiteral, got none")
	}

	var _message string // out

	_message = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	overrides.PrinterrLiteral(_message)
}

//export _gotk4_gio2_BufferedInputStreamClass_fill
func _gotk4_gio2_BufferedInputStreamClass_fill(arg0 *C.GBufferedInputStream, arg1 C.gssize, arg2 *C.GCancellable, _cerr **C.GError) (cret C.gssize) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[BufferedInputStreamOverrides](instance0)
	if overrides.Fill == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected BufferedInputStreamOverrides.Fill, got none")
	}

	var _cancellable context.Context // out
	var _count int                   // out

	if arg2 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg2))
	}
	_count = int(arg1)

	gssize, _goerr := overrides.Fill(_cancellable, _count)

	var _ int
	var _ error

	cret = C.gssize(gssize)
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_BufferedInputStreamClass_fill_finish
func _gotk4_gio2_BufferedInputStreamClass_fill_finish(arg0 *C.GBufferedInputStream, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.gssize) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[BufferedInputStreamOverrides](instance0)
	if overrides.FillFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected BufferedInputStreamOverrides.FillFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	gssize, _goerr := overrides.FillFinish(_result)

	var _ int
	var _ error

	cret = C.gssize(gssize)
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_CancellableClass_cancelled
func _gotk4_gio2_CancellableClass_cancelled(arg0 *C.GCancellable) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CancellableOverrides](instance0)
	if overrides.Cancelled == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CancellableOverrides.Cancelled, got none")
	}

	overrides.Cancelled()
}

//export _gotk4_gio2_Cancellable_ConnectCancelled
func _gotk4_gio2_Cancellable_ConnectCancelled(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_DBusAuthObserver_ConnectAllowMechanism
func _gotk4_gio2_DBusAuthObserver_ConnectAllowMechanism(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) (cret C.gboolean) {
	var f func(mechanism string) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(mechanism string) (ok bool))
	}

	var _mechanism string // out

	_mechanism = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	ok := f(_mechanism)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_DBusAuthObserver_ConnectAuthorizeAuthenticatedPeer
func _gotk4_gio2_DBusAuthObserver_ConnectAuthorizeAuthenticatedPeer(arg0 C.gpointer, arg1 *C.GIOStream, arg2 *C.GCredentials, arg3 C.guintptr) (cret C.gboolean) {
	var f func(stream IOStreamer, credentials *Credentials) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(stream IOStreamer, credentials *Credentials) (ok bool))
	}

	var _stream IOStreamer        // out
	var _credentials *Credentials // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.IOStreamer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(IOStreamer)
			return ok
		})
		rv, ok := casted.(IOStreamer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.IOStreamer")
		}
		_stream = rv
	}
	if arg2 != nil {
		_credentials = wrapCredentials(coreglib.Take(unsafe.Pointer(arg2)))
	}

	ok := f(_stream, _credentials)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_DBusConnection_ConnectClosed
func _gotk4_gio2_DBusConnection_ConnectClosed(arg0 C.gpointer, arg1 C.gboolean, arg2 *C.GError, arg3 C.guintptr) {
	var f func(remotePeerVanished bool, err error)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(remotePeerVanished bool, err error))
	}

	var _remotePeerVanished bool // out
	var _err error               // out

	if arg1 != 0 {
		_remotePeerVanished = true
	}
	if arg2 != nil {
		_err = gerror.Take(unsafe.Pointer(arg2))
	}

	f(_remotePeerVanished, _err)
}

//export _gotk4_gio2_DBusInterfaceSkeletonClass_flush
func _gotk4_gio2_DBusInterfaceSkeletonClass_flush(arg0 *C.GDBusInterfaceSkeleton) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[DBusInterfaceSkeletonOverrides](instance0)
	if overrides.Flush == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected DBusInterfaceSkeletonOverrides.Flush, got none")
	}

	overrides.Flush()
}

//export _gotk4_gio2_DBusInterfaceSkeletonClass_g_authorize_method
func _gotk4_gio2_DBusInterfaceSkeletonClass_g_authorize_method(arg0 *C.GDBusInterfaceSkeleton, arg1 *C.GDBusMethodInvocation) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[DBusInterfaceSkeletonOverrides](instance0)
	if overrides.GAuthorizeMethod == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected DBusInterfaceSkeletonOverrides.GAuthorizeMethod, got none")
	}

	var _invocation *DBusMethodInvocation // out

	_invocation = wrapDBusMethodInvocation(coreglib.Take(unsafe.Pointer(arg1)))

	ok := overrides.GAuthorizeMethod(_invocation)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_DBusInterfaceSkeletonClass_get_info
func _gotk4_gio2_DBusInterfaceSkeletonClass_get_info(arg0 *C.GDBusInterfaceSkeleton) (cret *C.GDBusInterfaceInfo) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[DBusInterfaceSkeletonOverrides](instance0)
	if overrides.Info == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected DBusInterfaceSkeletonOverrides.Info, got none")
	}

	dBusInterfaceInfo := overrides.Info()

	var _ *DBusInterfaceInfo

	cret = (*C.GDBusInterfaceInfo)(gextras.StructNative(unsafe.Pointer(dBusInterfaceInfo)))

	return cret
}

//export _gotk4_gio2_DBusInterfaceSkeletonClass_get_properties
func _gotk4_gio2_DBusInterfaceSkeletonClass_get_properties(arg0 *C.GDBusInterfaceSkeleton) (cret *C.GVariant) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[DBusInterfaceSkeletonOverrides](instance0)
	if overrides.Properties == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected DBusInterfaceSkeletonOverrides.Properties, got none")
	}

	variant := overrides.Properties()

	var _ *glib.Variant

	cret = (*C.GVariant)(gextras.StructNative(unsafe.Pointer(variant)))

	return cret
}

//export _gotk4_gio2_DBusInterfaceSkeletonClass_get_vtable
func _gotk4_gio2_DBusInterfaceSkeletonClass_get_vtable(arg0 *C.GDBusInterfaceSkeleton) (cret *C.GDBusInterfaceVTable) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[DBusInterfaceSkeletonOverrides](instance0)
	if overrides.Vtable == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected DBusInterfaceSkeletonOverrides.Vtable, got none")
	}

	dBusInterfaceVTable := overrides.Vtable()

	var _ *DBusInterfaceVTable

	cret = (*C.GDBusInterfaceVTable)(gextras.StructNative(unsafe.Pointer(dBusInterfaceVTable)))

	return cret
}

//export _gotk4_gio2_DBusInterfaceSkeleton_ConnectGAuthorizeMethod
func _gotk4_gio2_DBusInterfaceSkeleton_ConnectGAuthorizeMethod(arg0 C.gpointer, arg1 *C.GDBusMethodInvocation, arg2 C.guintptr) (cret C.gboolean) {
	var f func(invocation *DBusMethodInvocation) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(invocation *DBusMethodInvocation) (ok bool))
	}

	var _invocation *DBusMethodInvocation // out

	_invocation = wrapDBusMethodInvocation(coreglib.Take(unsafe.Pointer(arg1)))

	ok := f(_invocation)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_DBusObjectManagerClientClass_interface_proxy_signal
func _gotk4_gio2_DBusObjectManagerClientClass_interface_proxy_signal(arg0 *C.GDBusObjectManagerClient, arg1 *C.GDBusObjectProxy, arg2 *C.GDBusProxy, arg3 *C.gchar, arg4 *C.gchar, arg5 *C.GVariant) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[DBusObjectManagerClientOverrides](instance0)
	if overrides.InterfaceProxySignal == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected DBusObjectManagerClientOverrides.InterfaceProxySignal, got none")
	}

	var _objectProxy *DBusObjectProxy // out
	var _interfaceProxy *DBusProxy    // out
	var _senderName string            // out
	var _signalName string            // out
	var _parameters *glib.Variant     // out

	_objectProxy = wrapDBusObjectProxy(coreglib.Take(unsafe.Pointer(arg1)))
	_interfaceProxy = wrapDBusProxy(coreglib.Take(unsafe.Pointer(arg2)))
	_senderName = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))
	_signalName = C.GoString((*C.gchar)(unsafe.Pointer(arg4)))
	_parameters = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg5)))
	C.g_variant_ref(arg5)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_parameters)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)

	overrides.InterfaceProxySignal(_objectProxy, _interfaceProxy, _senderName, _signalName, _parameters)
}

//export _gotk4_gio2_DBusObjectManagerClient_ConnectInterfaceProxyPropertiesChanged
func _gotk4_gio2_DBusObjectManagerClient_ConnectInterfaceProxyPropertiesChanged(arg0 C.gpointer, arg1 *C.GDBusObjectProxy, arg2 *C.GDBusProxy, arg3 *C.GVariant, arg4 **C.gchar, arg5 C.guintptr) {
	var f func(objectProxy *DBusObjectProxy, interfaceProxy *DBusProxy, changedProperties *glib.Variant, invalidatedProperties []string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg5))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(objectProxy *DBusObjectProxy, interfaceProxy *DBusProxy, changedProperties *glib.Variant, invalidatedProperties []string))
	}

	var _objectProxy *DBusObjectProxy    // out
	var _interfaceProxy *DBusProxy       // out
	var _changedProperties *glib.Variant // out
	var _invalidatedProperties []string  // out

	_objectProxy = wrapDBusObjectProxy(coreglib.Take(unsafe.Pointer(arg1)))
	_interfaceProxy = wrapDBusProxy(coreglib.Take(unsafe.Pointer(arg2)))
	_changedProperties = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg3)))
	C.g_variant_ref(arg3)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_changedProperties)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)
	{
		var i int
		var z *C.gchar
		for p := arg4; *p != z; p = &unsafe.Slice(p, 2)[1] {
			i++
		}

		src := unsafe.Slice(arg4, i)
		_invalidatedProperties = make([]string, i)
		for i := range src {
			_invalidatedProperties[i] = C.GoString((*C.gchar)(unsafe.Pointer(src[i])))
		}
	}

	f(_objectProxy, _interfaceProxy, _changedProperties, _invalidatedProperties)
}

//export _gotk4_gio2_DBusObjectManagerClient_ConnectInterfaceProxySignal
func _gotk4_gio2_DBusObjectManagerClient_ConnectInterfaceProxySignal(arg0 C.gpointer, arg1 *C.GDBusObjectProxy, arg2 *C.GDBusProxy, arg3 *C.gchar, arg4 *C.gchar, arg5 *C.GVariant, arg6 C.guintptr) {
	var f func(objectProxy *DBusObjectProxy, interfaceProxy *DBusProxy, senderName, signalName string, parameters *glib.Variant)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg6))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(objectProxy *DBusObjectProxy, interfaceProxy *DBusProxy, senderName, signalName string, parameters *glib.Variant))
	}

	var _objectProxy *DBusObjectProxy // out
	var _interfaceProxy *DBusProxy    // out
	var _senderName string            // out
	var _signalName string            // out
	var _parameters *glib.Variant     // out

	_objectProxy = wrapDBusObjectProxy(coreglib.Take(unsafe.Pointer(arg1)))
	_interfaceProxy = wrapDBusProxy(coreglib.Take(unsafe.Pointer(arg2)))
	_senderName = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))
	_signalName = C.GoString((*C.gchar)(unsafe.Pointer(arg4)))
	_parameters = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg5)))
	C.g_variant_ref(arg5)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_parameters)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)

	f(_objectProxy, _interfaceProxy, _senderName, _signalName, _parameters)
}

//export _gotk4_gio2_DBusObjectSkeletonClass_authorize_method
func _gotk4_gio2_DBusObjectSkeletonClass_authorize_method(arg0 *C.GDBusObjectSkeleton, arg1 *C.GDBusInterfaceSkeleton, arg2 *C.GDBusMethodInvocation) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[DBusObjectSkeletonOverrides](instance0)
	if overrides.AuthorizeMethod == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected DBusObjectSkeletonOverrides.AuthorizeMethod, got none")
	}

	var _interface_ DBusInterfaceSkeletonner // out
	var _invocation *DBusMethodInvocation    // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.DBusInterfaceSkeletonner is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(DBusInterfaceSkeletonner)
			return ok
		})
		rv, ok := casted.(DBusInterfaceSkeletonner)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.DBusInterfaceSkeletonner")
		}
		_interface_ = rv
	}
	_invocation = wrapDBusMethodInvocation(coreglib.Take(unsafe.Pointer(arg2)))

	ok := overrides.AuthorizeMethod(_interface_, _invocation)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_DBusObjectSkeleton_ConnectAuthorizeMethod
func _gotk4_gio2_DBusObjectSkeleton_ConnectAuthorizeMethod(arg0 C.gpointer, arg1 *C.GDBusInterfaceSkeleton, arg2 *C.GDBusMethodInvocation, arg3 C.guintptr) (cret C.gboolean) {
	var f func(iface DBusInterfaceSkeletonner, invocation *DBusMethodInvocation) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(iface DBusInterfaceSkeletonner, invocation *DBusMethodInvocation) (ok bool))
	}

	var _iface DBusInterfaceSkeletonner   // out
	var _invocation *DBusMethodInvocation // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.DBusInterfaceSkeletonner is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(DBusInterfaceSkeletonner)
			return ok
		})
		rv, ok := casted.(DBusInterfaceSkeletonner)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.DBusInterfaceSkeletonner")
		}
		_iface = rv
	}
	_invocation = wrapDBusMethodInvocation(coreglib.Take(unsafe.Pointer(arg2)))

	ok := f(_iface, _invocation)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_DBusProxyClass_g_signal
func _gotk4_gio2_DBusProxyClass_g_signal(arg0 *C.GDBusProxy, arg1 *C.gchar, arg2 *C.gchar, arg3 *C.GVariant) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[DBusProxyOverrides](instance0)
	if overrides.GSignal == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected DBusProxyOverrides.GSignal, got none")
	}

	var _senderName string        // out
	var _signalName string        // out
	var _parameters *glib.Variant // out

	_senderName = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	_signalName = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_parameters = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg3)))
	C.g_variant_ref(arg3)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_parameters)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)

	overrides.GSignal(_senderName, _signalName, _parameters)
}

//export _gotk4_gio2_DBusProxy_ConnectGPropertiesChanged
func _gotk4_gio2_DBusProxy_ConnectGPropertiesChanged(arg0 C.gpointer, arg1 *C.GVariant, arg2 **C.gchar, arg3 C.guintptr) {
	var f func(changedProperties *glib.Variant, invalidatedProperties []string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(changedProperties *glib.Variant, invalidatedProperties []string))
	}

	var _changedProperties *glib.Variant // out
	var _invalidatedProperties []string  // out

	_changedProperties = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	C.g_variant_ref(arg1)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_changedProperties)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)
	{
		var i int
		var z *C.gchar
		for p := arg2; *p != z; p = &unsafe.Slice(p, 2)[1] {
			i++
		}

		src := unsafe.Slice(arg2, i)
		_invalidatedProperties = make([]string, i)
		for i := range src {
			_invalidatedProperties[i] = C.GoString((*C.gchar)(unsafe.Pointer(src[i])))
		}
	}

	f(_changedProperties, _invalidatedProperties)
}

//export _gotk4_gio2_DBusProxy_ConnectGSignal
func _gotk4_gio2_DBusProxy_ConnectGSignal(arg0 C.gpointer, arg1 *C.gchar, arg2 *C.gchar, arg3 *C.GVariant, arg4 C.guintptr) {
	var f func(senderName, signalName string, parameters *glib.Variant)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(senderName, signalName string, parameters *glib.Variant))
	}

	var _senderName string        // out
	var _signalName string        // out
	var _parameters *glib.Variant // out

	if arg1 != nil {
		_senderName = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	}
	_signalName = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_parameters = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg3)))
	C.g_variant_ref(arg3)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_parameters)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_variant_unref((*C.GVariant)(intern.C))
		},
	)

	f(_senderName, _signalName, _parameters)
}

//export _gotk4_gio2_DBusServer_ConnectNewConnection
func _gotk4_gio2_DBusServer_ConnectNewConnection(arg0 C.gpointer, arg1 *C.GDBusConnection, arg2 C.guintptr) (cret C.gboolean) {
	var f func(connection *DBusConnection) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(connection *DBusConnection) (ok bool))
	}

	var _connection *DBusConnection // out

	_connection = wrapDBusConnection(coreglib.Take(unsafe.Pointer(arg1)))

	ok := f(_connection)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_DebugControllerDBusClass_authorize
func _gotk4_gio2_DebugControllerDBusClass_authorize(arg0 *C.GDebugControllerDBus, arg1 *C.GDBusMethodInvocation) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[DebugControllerDBusOverrides](instance0)
	if overrides.Authorize == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected DebugControllerDBusOverrides.Authorize, got none")
	}

	var _invocation *DBusMethodInvocation // out

	_invocation = wrapDBusMethodInvocation(coreglib.Take(unsafe.Pointer(arg1)))

	ok := overrides.Authorize(_invocation)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_DebugControllerDBus_ConnectAuthorize
func _gotk4_gio2_DebugControllerDBus_ConnectAuthorize(arg0 C.gpointer, arg1 *C.GDBusMethodInvocation, arg2 C.guintptr) (cret C.gboolean) {
	var f func(invocation *DBusMethodInvocation) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(invocation *DBusMethodInvocation) (ok bool))
	}

	var _invocation *DBusMethodInvocation // out

	_invocation = wrapDBusMethodInvocation(coreglib.Take(unsafe.Pointer(arg1)))

	ok := f(_invocation)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_FileEnumeratorClass_close_finish
func _gotk4_gio2_FileEnumeratorClass_close_finish(arg0 *C.GFileEnumerator, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileEnumeratorOverrides](instance0)
	if overrides.CloseFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileEnumeratorOverrides.CloseFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	_goerr := overrides.CloseFinish(_result)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_FileEnumeratorClass_close_fn
func _gotk4_gio2_FileEnumeratorClass_close_fn(arg0 *C.GFileEnumerator, arg1 *C.GCancellable, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileEnumeratorOverrides](instance0)
	if overrides.CloseFn == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileEnumeratorOverrides.CloseFn, got none")
	}

	var _cancellable context.Context // out

	if arg1 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg1))
	}

	_goerr := overrides.CloseFn(_cancellable)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_FileEnumeratorClass_next_file
func _gotk4_gio2_FileEnumeratorClass_next_file(arg0 *C.GFileEnumerator, arg1 *C.GCancellable, _cerr **C.GError) (cret *C.GFileInfo) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileEnumeratorOverrides](instance0)
	if overrides.NextFile == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileEnumeratorOverrides.NextFile, got none")
	}

	var _cancellable context.Context // out

	if arg1 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg1))
	}

	fileInfo, _goerr := overrides.NextFile(_cancellable)

	var _ *FileInfo
	var _ error

	if fileInfo != nil {
		cret = (*C.GFileInfo)(unsafe.Pointer(coreglib.InternObject(fileInfo).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(fileInfo).Native()))
	}
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_FileEnumeratorClass_next_files_finish
func _gotk4_gio2_FileEnumeratorClass_next_files_finish(arg0 *C.GFileEnumerator, arg1 *C.GAsyncResult, _cerr **C.GError) (cret *C.GList) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileEnumeratorOverrides](instance0)
	if overrides.NextFilesFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileEnumeratorOverrides.NextFilesFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	list, _goerr := overrides.NextFilesFinish(_result)

	var _ []*FileInfo
	var _ error

	for i := len(list) - 1; i >= 0; i-- {
		src := list[i]
		var dst *C.GFileInfo // out
		dst = (*C.GFileInfo)(unsafe.Pointer(coreglib.InternObject(src).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(src).Native()))
		cret = C.g_list_prepend(cret, C.gpointer(unsafe.Pointer(dst)))
	}
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_FileIOStreamClass_can_seek
func _gotk4_gio2_FileIOStreamClass_can_seek(arg0 *C.GFileIOStream) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileIOStreamOverrides](instance0)
	if overrides.CanSeek == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileIOStreamOverrides.CanSeek, got none")
	}

	ok := overrides.CanSeek()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_FileIOStreamClass_can_truncate
func _gotk4_gio2_FileIOStreamClass_can_truncate(arg0 *C.GFileIOStream) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileIOStreamOverrides](instance0)
	if overrides.CanTruncate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileIOStreamOverrides.CanTruncate, got none")
	}

	ok := overrides.CanTruncate()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_FileIOStreamClass_get_etag
func _gotk4_gio2_FileIOStreamClass_get_etag(arg0 *C.GFileIOStream) (cret *C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileIOStreamOverrides](instance0)
	if overrides.ETag == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileIOStreamOverrides.ETag, got none")
	}

	utf8 := overrides.ETag()

	var _ string

	if utf8 != "" {
		cret = (*C.char)(unsafe.Pointer(C.CString(utf8)))
	}

	return cret
}

//export _gotk4_gio2_FileIOStreamClass_query_info
func _gotk4_gio2_FileIOStreamClass_query_info(arg0 *C.GFileIOStream, arg1 *C.char, arg2 *C.GCancellable, _cerr **C.GError) (cret *C.GFileInfo) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileIOStreamOverrides](instance0)
	if overrides.QueryInfo == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileIOStreamOverrides.QueryInfo, got none")
	}

	var _cancellable context.Context // out
	var _attributes string           // out

	if arg2 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg2))
	}
	_attributes = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	fileInfo, _goerr := overrides.QueryInfo(_cancellable, _attributes)

	var _ *FileInfo
	var _ error

	cret = (*C.GFileInfo)(unsafe.Pointer(coreglib.InternObject(fileInfo).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(fileInfo).Native()))
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_FileIOStreamClass_query_info_finish
func _gotk4_gio2_FileIOStreamClass_query_info_finish(arg0 *C.GFileIOStream, arg1 *C.GAsyncResult, _cerr **C.GError) (cret *C.GFileInfo) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileIOStreamOverrides](instance0)
	if overrides.QueryInfoFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileIOStreamOverrides.QueryInfoFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	fileInfo, _goerr := overrides.QueryInfoFinish(_result)

	var _ *FileInfo
	var _ error

	cret = (*C.GFileInfo)(unsafe.Pointer(coreglib.InternObject(fileInfo).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(fileInfo).Native()))
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_FileIOStreamClass_seek
func _gotk4_gio2_FileIOStreamClass_seek(arg0 *C.GFileIOStream, arg1 C.goffset, arg2 C.GSeekType, arg3 *C.GCancellable, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileIOStreamOverrides](instance0)
	if overrides.Seek == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileIOStreamOverrides.Seek, got none")
	}

	var _cancellable context.Context // out
	var _offset int64                // out
	var _typ glib.SeekType           // out

	if arg3 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg3))
	}
	_offset = int64(arg1)
	_typ = glib.SeekType(arg2)

	_goerr := overrides.Seek(_cancellable, _offset, _typ)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_FileIOStreamClass_tell
func _gotk4_gio2_FileIOStreamClass_tell(arg0 *C.GFileIOStream) (cret C.goffset) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileIOStreamOverrides](instance0)
	if overrides.Tell == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileIOStreamOverrides.Tell, got none")
	}

	gint64 := overrides.Tell()

	var _ int64

	cret = C.goffset(gint64)

	return cret
}

//export _gotk4_gio2_FileIOStreamClass_truncate_fn
func _gotk4_gio2_FileIOStreamClass_truncate_fn(arg0 *C.GFileIOStream, arg1 C.goffset, arg2 *C.GCancellable, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileIOStreamOverrides](instance0)
	if overrides.TruncateFn == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileIOStreamOverrides.TruncateFn, got none")
	}

	var _cancellable context.Context // out
	var _size int64                  // out

	if arg2 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg2))
	}
	_size = int64(arg1)

	_goerr := overrides.TruncateFn(_cancellable, _size)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_FileInputStreamClass_can_seek
func _gotk4_gio2_FileInputStreamClass_can_seek(arg0 *C.GFileInputStream) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileInputStreamOverrides](instance0)
	if overrides.CanSeek == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileInputStreamOverrides.CanSeek, got none")
	}

	ok := overrides.CanSeek()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_FileInputStreamClass_query_info
func _gotk4_gio2_FileInputStreamClass_query_info(arg0 *C.GFileInputStream, arg1 *C.char, arg2 *C.GCancellable, _cerr **C.GError) (cret *C.GFileInfo) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileInputStreamOverrides](instance0)
	if overrides.QueryInfo == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileInputStreamOverrides.QueryInfo, got none")
	}

	var _cancellable context.Context // out
	var _attributes string           // out

	if arg2 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg2))
	}
	_attributes = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	fileInfo, _goerr := overrides.QueryInfo(_cancellable, _attributes)

	var _ *FileInfo
	var _ error

	cret = (*C.GFileInfo)(unsafe.Pointer(coreglib.InternObject(fileInfo).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(fileInfo).Native()))
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_FileInputStreamClass_query_info_finish
func _gotk4_gio2_FileInputStreamClass_query_info_finish(arg0 *C.GFileInputStream, arg1 *C.GAsyncResult, _cerr **C.GError) (cret *C.GFileInfo) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileInputStreamOverrides](instance0)
	if overrides.QueryInfoFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileInputStreamOverrides.QueryInfoFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	fileInfo, _goerr := overrides.QueryInfoFinish(_result)

	var _ *FileInfo
	var _ error

	cret = (*C.GFileInfo)(unsafe.Pointer(coreglib.InternObject(fileInfo).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(fileInfo).Native()))
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_FileInputStreamClass_seek
func _gotk4_gio2_FileInputStreamClass_seek(arg0 *C.GFileInputStream, arg1 C.goffset, arg2 C.GSeekType, arg3 *C.GCancellable, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileInputStreamOverrides](instance0)
	if overrides.Seek == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileInputStreamOverrides.Seek, got none")
	}

	var _cancellable context.Context // out
	var _offset int64                // out
	var _typ glib.SeekType           // out

	if arg3 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg3))
	}
	_offset = int64(arg1)
	_typ = glib.SeekType(arg2)

	_goerr := overrides.Seek(_cancellable, _offset, _typ)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_FileInputStreamClass_tell
func _gotk4_gio2_FileInputStreamClass_tell(arg0 *C.GFileInputStream) (cret C.goffset) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileInputStreamOverrides](instance0)
	if overrides.Tell == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileInputStreamOverrides.Tell, got none")
	}

	gint64 := overrides.Tell()

	var _ int64

	cret = C.goffset(gint64)

	return cret
}

//export _gotk4_gio2_FileMonitorClass_cancel
func _gotk4_gio2_FileMonitorClass_cancel(arg0 *C.GFileMonitor) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileMonitorOverrides](instance0)
	if overrides.Cancel == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileMonitorOverrides.Cancel, got none")
	}

	ok := overrides.Cancel()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_FileMonitorClass_changed
func _gotk4_gio2_FileMonitorClass_changed(arg0 *C.GFileMonitor, arg1 *C.GFile, arg2 *C.GFile, arg3 C.GFileMonitorEvent) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileMonitorOverrides](instance0)
	if overrides.Changed == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileMonitorOverrides.Changed, got none")
	}

	var _file Filer                 // out
	var _otherFile Filer            // out
	var _eventType FileMonitorEvent // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Filer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Filer)
			return ok
		})
		rv, ok := casted.(Filer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Filer")
		}
		_file = rv
	}
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gio.Filer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Filer)
			return ok
		})
		rv, ok := casted.(Filer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Filer")
		}
		_otherFile = rv
	}
	_eventType = FileMonitorEvent(arg3)

	overrides.Changed(_file, _otherFile, _eventType)
}

//export _gotk4_gio2_FileMonitor_ConnectChanged
func _gotk4_gio2_FileMonitor_ConnectChanged(arg0 C.gpointer, arg1 *C.GFile, arg2 *C.GFile, arg3 C.GFileMonitorEvent, arg4 C.guintptr) {
	var f func(file, otherFile Filer, eventType FileMonitorEvent)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(file, otherFile Filer, eventType FileMonitorEvent))
	}

	var _file Filer                 // out
	var _otherFile Filer            // out
	var _eventType FileMonitorEvent // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Filer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Filer)
			return ok
		})
		rv, ok := casted.(Filer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Filer")
		}
		_file = rv
	}
	if arg2 != nil {
		{
			objptr := unsafe.Pointer(arg2)

			object := coreglib.Take(objptr)
			casted := object.WalkCast(func(obj coreglib.Objector) bool {
				_, ok := obj.(Filer)
				return ok
			})
			rv, ok := casted.(Filer)
			if !ok {
				panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Filer")
			}
			_otherFile = rv
		}
	}
	_eventType = FileMonitorEvent(arg3)

	f(_file, _otherFile, _eventType)
}

//export _gotk4_gio2_FileOutputStreamClass_can_seek
func _gotk4_gio2_FileOutputStreamClass_can_seek(arg0 *C.GFileOutputStream) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileOutputStreamOverrides](instance0)
	if overrides.CanSeek == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileOutputStreamOverrides.CanSeek, got none")
	}

	ok := overrides.CanSeek()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_FileOutputStreamClass_can_truncate
func _gotk4_gio2_FileOutputStreamClass_can_truncate(arg0 *C.GFileOutputStream) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileOutputStreamOverrides](instance0)
	if overrides.CanTruncate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileOutputStreamOverrides.CanTruncate, got none")
	}

	ok := overrides.CanTruncate()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_FileOutputStreamClass_get_etag
func _gotk4_gio2_FileOutputStreamClass_get_etag(arg0 *C.GFileOutputStream) (cret *C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileOutputStreamOverrides](instance0)
	if overrides.ETag == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileOutputStreamOverrides.ETag, got none")
	}

	utf8 := overrides.ETag()

	var _ string

	if utf8 != "" {
		cret = (*C.char)(unsafe.Pointer(C.CString(utf8)))
	}

	return cret
}

//export _gotk4_gio2_FileOutputStreamClass_query_info
func _gotk4_gio2_FileOutputStreamClass_query_info(arg0 *C.GFileOutputStream, arg1 *C.char, arg2 *C.GCancellable, _cerr **C.GError) (cret *C.GFileInfo) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileOutputStreamOverrides](instance0)
	if overrides.QueryInfo == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileOutputStreamOverrides.QueryInfo, got none")
	}

	var _cancellable context.Context // out
	var _attributes string           // out

	if arg2 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg2))
	}
	_attributes = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	fileInfo, _goerr := overrides.QueryInfo(_cancellable, _attributes)

	var _ *FileInfo
	var _ error

	cret = (*C.GFileInfo)(unsafe.Pointer(coreglib.InternObject(fileInfo).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(fileInfo).Native()))
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_FileOutputStreamClass_query_info_finish
func _gotk4_gio2_FileOutputStreamClass_query_info_finish(arg0 *C.GFileOutputStream, arg1 *C.GAsyncResult, _cerr **C.GError) (cret *C.GFileInfo) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileOutputStreamOverrides](instance0)
	if overrides.QueryInfoFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileOutputStreamOverrides.QueryInfoFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	fileInfo, _goerr := overrides.QueryInfoFinish(_result)

	var _ *FileInfo
	var _ error

	cret = (*C.GFileInfo)(unsafe.Pointer(coreglib.InternObject(fileInfo).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(fileInfo).Native()))
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_FileOutputStreamClass_seek
func _gotk4_gio2_FileOutputStreamClass_seek(arg0 *C.GFileOutputStream, arg1 C.goffset, arg2 C.GSeekType, arg3 *C.GCancellable, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileOutputStreamOverrides](instance0)
	if overrides.Seek == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileOutputStreamOverrides.Seek, got none")
	}

	var _cancellable context.Context // out
	var _offset int64                // out
	var _typ glib.SeekType           // out

	if arg3 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg3))
	}
	_offset = int64(arg1)
	_typ = glib.SeekType(arg2)

	_goerr := overrides.Seek(_cancellable, _offset, _typ)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_FileOutputStreamClass_tell
func _gotk4_gio2_FileOutputStreamClass_tell(arg0 *C.GFileOutputStream) (cret C.goffset) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileOutputStreamOverrides](instance0)
	if overrides.Tell == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileOutputStreamOverrides.Tell, got none")
	}

	gint64 := overrides.Tell()

	var _ int64

	cret = C.goffset(gint64)

	return cret
}

//export _gotk4_gio2_FileOutputStreamClass_truncate_fn
func _gotk4_gio2_FileOutputStreamClass_truncate_fn(arg0 *C.GFileOutputStream, arg1 C.goffset, arg2 *C.GCancellable, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FileOutputStreamOverrides](instance0)
	if overrides.TruncateFn == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FileOutputStreamOverrides.TruncateFn, got none")
	}

	var _cancellable context.Context // out
	var _size int64                  // out

	if arg2 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg2))
	}
	_size = int64(arg1)

	_goerr := overrides.TruncateFn(_cancellable, _size)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_FilenameCompleterClass_got_completion_data
func _gotk4_gio2_FilenameCompleterClass_got_completion_data(arg0 *C.GFilenameCompleter) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FilenameCompleterOverrides](instance0)
	if overrides.GotCompletionData == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FilenameCompleterOverrides.GotCompletionData, got none")
	}

	overrides.GotCompletionData()
}

//export _gotk4_gio2_FilenameCompleter_ConnectGotCompletionData
func _gotk4_gio2_FilenameCompleter_ConnectGotCompletionData(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_IOStreamClass_close_finish
func _gotk4_gio2_IOStreamClass_close_finish(arg0 *C.GIOStream, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IOStreamOverrides](instance0)
	if overrides.CloseFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IOStreamOverrides.CloseFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	_goerr := overrides.CloseFinish(_result)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_IOStreamClass_close_fn
func _gotk4_gio2_IOStreamClass_close_fn(arg0 *C.GIOStream, arg1 *C.GCancellable, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IOStreamOverrides](instance0)
	if overrides.CloseFn == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IOStreamOverrides.CloseFn, got none")
	}

	var _cancellable context.Context // out

	if arg1 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg1))
	}

	_goerr := overrides.CloseFn(_cancellable)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_IOStreamClass_get_input_stream
func _gotk4_gio2_IOStreamClass_get_input_stream(arg0 *C.GIOStream) (cret *C.GInputStream) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IOStreamOverrides](instance0)
	if overrides.InputStream == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IOStreamOverrides.InputStream, got none")
	}

	inputStream := overrides.InputStream()

	var _ InputStreamer

	cret = (*C.GInputStream)(unsafe.Pointer(coreglib.InternObject(inputStream).Native()))

	return cret
}

//export _gotk4_gio2_IOStreamClass_get_output_stream
func _gotk4_gio2_IOStreamClass_get_output_stream(arg0 *C.GIOStream) (cret *C.GOutputStream) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IOStreamOverrides](instance0)
	if overrides.OutputStream == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IOStreamOverrides.OutputStream, got none")
	}

	outputStream := overrides.OutputStream()

	var _ OutputStreamer

	cret = (*C.GOutputStream)(unsafe.Pointer(coreglib.InternObject(outputStream).Native()))

	return cret
}

//export _gotk4_gio2_InetAddressClass_to_string
func _gotk4_gio2_InetAddressClass_to_string(arg0 *C.GInetAddress) (cret *C.gchar) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[InetAddressOverrides](instance0)
	if overrides.String == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected InetAddressOverrides.String, got none")
	}

	utf8 := overrides.String()

	var _ string

	cret = (*C.gchar)(unsafe.Pointer(C.CString(utf8)))

	return cret
}

//export _gotk4_gio2_InputStreamClass_close_finish
func _gotk4_gio2_InputStreamClass_close_finish(arg0 *C.GInputStream, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[InputStreamOverrides](instance0)
	if overrides.CloseFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected InputStreamOverrides.CloseFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	_goerr := overrides.CloseFinish(_result)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_InputStreamClass_close_fn
func _gotk4_gio2_InputStreamClass_close_fn(arg0 *C.GInputStream, arg1 *C.GCancellable, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[InputStreamOverrides](instance0)
	if overrides.CloseFn == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected InputStreamOverrides.CloseFn, got none")
	}

	var _cancellable context.Context // out

	if arg1 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg1))
	}

	_goerr := overrides.CloseFn(_cancellable)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_InputStreamClass_read_finish
func _gotk4_gio2_InputStreamClass_read_finish(arg0 *C.GInputStream, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.gssize) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[InputStreamOverrides](instance0)
	if overrides.ReadFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected InputStreamOverrides.ReadFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	gssize, _goerr := overrides.ReadFinish(_result)

	var _ int
	var _ error

	cret = C.gssize(gssize)
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_InputStreamClass_skip
func _gotk4_gio2_InputStreamClass_skip(arg0 *C.GInputStream, arg1 C.gsize, arg2 *C.GCancellable, _cerr **C.GError) (cret C.gssize) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[InputStreamOverrides](instance0)
	if overrides.Skip == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected InputStreamOverrides.Skip, got none")
	}

	var _cancellable context.Context // out
	var _count uint                  // out

	if arg2 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg2))
	}
	_count = uint(arg1)

	gssize, _goerr := overrides.Skip(_cancellable, _count)

	var _ int
	var _ error

	cret = C.gssize(gssize)
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_InputStreamClass_skip_finish
func _gotk4_gio2_InputStreamClass_skip_finish(arg0 *C.GInputStream, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.gssize) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[InputStreamOverrides](instance0)
	if overrides.SkipFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected InputStreamOverrides.SkipFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	gssize, _goerr := overrides.SkipFinish(_result)

	var _ int
	var _ error

	cret = C.gssize(gssize)
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_MenuAttributeIterClass_get_next
func _gotk4_gio2_MenuAttributeIterClass_get_next(arg0 *C.GMenuAttributeIter, arg1 **C.gchar, arg2 **C.GVariant) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MenuAttributeIterOverrides](instance0)
	if overrides.Next == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MenuAttributeIterOverrides.Next, got none")
	}

	outName, value, ok := overrides.Next()

	var _ string
	var _ *glib.Variant
	var _ bool

	if outName != "" {
		*arg1 = (*C.gchar)(unsafe.Pointer(C.CString(outName)))
		defer C.free(unsafe.Pointer(*arg1))
	}
	if value != nil {
		if value != nil {
			*arg2 = (*C.GVariant)(gextras.StructNative(unsafe.Pointer(value)))
		}
	}
	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_MenuLinkIterClass_get_next
func _gotk4_gio2_MenuLinkIterClass_get_next(arg0 *C.GMenuLinkIter, arg1 **C.gchar, arg2 **C.GMenuModel) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MenuLinkIterOverrides](instance0)
	if overrides.Next == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MenuLinkIterOverrides.Next, got none")
	}

	outLink, value, ok := overrides.Next()

	var _ string
	var _ MenuModeller
	var _ bool

	if outLink != "" {
		*arg1 = (*C.gchar)(unsafe.Pointer(C.CString(outLink)))
		defer C.free(unsafe.Pointer(*arg1))
	}
	if value != nil {
		if value != nil {
			*arg2 = (*C.GMenuModel)(unsafe.Pointer(coreglib.InternObject(value).Native()))
			C.g_object_ref(C.gpointer(coreglib.InternObject(value).Native()))
		}
	}
	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_MenuModelClass_get_item_attribute_value
func _gotk4_gio2_MenuModelClass_get_item_attribute_value(arg0 *C.GMenuModel, arg1 C.gint, arg2 *C.gchar, arg3 *C.GVariantType) (cret *C.GVariant) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MenuModelOverrides](instance0)
	if overrides.ItemAttributeValue == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MenuModelOverrides.ItemAttributeValue, got none")
	}

	var _itemIndex int                  // out
	var _attribute string               // out
	var _expectedType *glib.VariantType // out

	_itemIndex = int(arg1)
	_attribute = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	if arg3 != nil {
		_expectedType = (*glib.VariantType)(gextras.NewStructNative(unsafe.Pointer(arg3)))
	}

	variant := overrides.ItemAttributeValue(_itemIndex, _attribute, _expectedType)

	var _ *glib.Variant

	if variant != nil {
		cret = (*C.GVariant)(gextras.StructNative(unsafe.Pointer(variant)))
	}

	return cret
}

//export _gotk4_gio2_MenuModelClass_get_item_attributes
func _gotk4_gio2_MenuModelClass_get_item_attributes(arg0 *C.GMenuModel, arg1 C.gint, arg2 **C.GHashTable) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MenuModelOverrides](instance0)
	if overrides.ItemAttributes == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MenuModelOverrides.ItemAttributes, got none")
	}

	var _itemIndex int // out

	_itemIndex = int(arg1)

	attributes := overrides.ItemAttributes(_itemIndex)

	var _ map[string]*glib.Variant

	*arg2 = C.g_hash_table_new_full(nil, nil, (*[0]byte)(C.free), (*[0]byte)(C.free))
	for ksrc, vsrc := range attributes {
		var kdst *C.gchar    // out
		var vdst *C.GVariant // out
		kdst = (*C.gchar)(unsafe.Pointer(C.CString(ksrc)))
		defer C.free(unsafe.Pointer(kdst))
		vdst = (*C.GVariant)(gextras.StructNative(unsafe.Pointer(vsrc)))
		C.g_hash_table_insert(*arg2, C.gpointer(unsafe.Pointer(kdst)), C.gpointer(unsafe.Pointer(vdst)))
	}
}

//export _gotk4_gio2_MenuModelClass_get_item_link
func _gotk4_gio2_MenuModelClass_get_item_link(arg0 *C.GMenuModel, arg1 C.gint, arg2 *C.gchar) (cret *C.GMenuModel) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MenuModelOverrides](instance0)
	if overrides.ItemLink == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MenuModelOverrides.ItemLink, got none")
	}

	var _itemIndex int // out
	var _link string   // out

	_itemIndex = int(arg1)
	_link = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	menuModel := overrides.ItemLink(_itemIndex, _link)

	var _ MenuModeller

	if menuModel != nil {
		cret = (*C.GMenuModel)(unsafe.Pointer(coreglib.InternObject(menuModel).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(menuModel).Native()))
	}

	return cret
}

//export _gotk4_gio2_MenuModelClass_get_item_links
func _gotk4_gio2_MenuModelClass_get_item_links(arg0 *C.GMenuModel, arg1 C.gint, arg2 **C.GHashTable) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MenuModelOverrides](instance0)
	if overrides.ItemLinks == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MenuModelOverrides.ItemLinks, got none")
	}

	var _itemIndex int // out

	_itemIndex = int(arg1)

	links := overrides.ItemLinks(_itemIndex)

	var _ map[string]MenuModeller

	*arg2 = C.g_hash_table_new_full(nil, nil, (*[0]byte)(C.free), (*[0]byte)(C.free))
	for ksrc, vsrc := range links {
		var kdst *C.gchar      // out
		var vdst *C.GMenuModel // out
		kdst = (*C.gchar)(unsafe.Pointer(C.CString(ksrc)))
		defer C.free(unsafe.Pointer(kdst))
		vdst = (*C.GMenuModel)(unsafe.Pointer(coreglib.InternObject(vsrc).Native()))
		C.g_hash_table_insert(*arg2, C.gpointer(unsafe.Pointer(kdst)), C.gpointer(unsafe.Pointer(vdst)))
	}
}

//export _gotk4_gio2_MenuModelClass_get_n_items
func _gotk4_gio2_MenuModelClass_get_n_items(arg0 *C.GMenuModel) (cret C.gint) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MenuModelOverrides](instance0)
	if overrides.NItems == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MenuModelOverrides.NItems, got none")
	}

	gint := overrides.NItems()

	var _ int

	cret = C.gint(gint)

	return cret
}

//export _gotk4_gio2_MenuModelClass_is_mutable
func _gotk4_gio2_MenuModelClass_is_mutable(arg0 *C.GMenuModel) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MenuModelOverrides](instance0)
	if overrides.IsMutable == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MenuModelOverrides.IsMutable, got none")
	}

	ok := overrides.IsMutable()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_MenuModelClass_iterate_item_attributes
func _gotk4_gio2_MenuModelClass_iterate_item_attributes(arg0 *C.GMenuModel, arg1 C.gint) (cret *C.GMenuAttributeIter) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MenuModelOverrides](instance0)
	if overrides.IterateItemAttributes == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MenuModelOverrides.IterateItemAttributes, got none")
	}

	var _itemIndex int // out

	_itemIndex = int(arg1)

	menuAttributeIter := overrides.IterateItemAttributes(_itemIndex)

	var _ MenuAttributeIterer

	cret = (*C.GMenuAttributeIter)(unsafe.Pointer(coreglib.InternObject(menuAttributeIter).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(menuAttributeIter).Native()))

	return cret
}

//export _gotk4_gio2_MenuModelClass_iterate_item_links
func _gotk4_gio2_MenuModelClass_iterate_item_links(arg0 *C.GMenuModel, arg1 C.gint) (cret *C.GMenuLinkIter) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MenuModelOverrides](instance0)
	if overrides.IterateItemLinks == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MenuModelOverrides.IterateItemLinks, got none")
	}

	var _itemIndex int // out

	_itemIndex = int(arg1)

	menuLinkIter := overrides.IterateItemLinks(_itemIndex)

	var _ MenuLinkIterer

	cret = (*C.GMenuLinkIter)(unsafe.Pointer(coreglib.InternObject(menuLinkIter).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(menuLinkIter).Native()))

	return cret
}

//export _gotk4_gio2_MenuModel_ConnectItemsChanged
func _gotk4_gio2_MenuModel_ConnectItemsChanged(arg0 C.gpointer, arg1 C.gint, arg2 C.gint, arg3 C.gint, arg4 C.guintptr) {
	var f func(position, removed, added int)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(position, removed, added int))
	}

	var _position int // out
	var _removed int  // out
	var _added int    // out

	_position = int(arg1)
	_removed = int(arg2)
	_added = int(arg3)

	f(_position, _removed, _added)
}

//export _gotk4_gio2_MountOperationClass_aborted
func _gotk4_gio2_MountOperationClass_aborted(arg0 *C.GMountOperation) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MountOperationOverrides](instance0)
	if overrides.Aborted == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MountOperationOverrides.Aborted, got none")
	}

	overrides.Aborted()
}

//export _gotk4_gio2_MountOperationClass_ask_password
func _gotk4_gio2_MountOperationClass_ask_password(arg0 *C.GMountOperation, arg1 *C.char, arg2 *C.char, arg3 *C.char, arg4 C.GAskPasswordFlags) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MountOperationOverrides](instance0)
	if overrides.AskPassword == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MountOperationOverrides.AskPassword, got none")
	}

	var _message string         // out
	var _defaultUser string     // out
	var _defaultDomain string   // out
	var _flags AskPasswordFlags // out

	_message = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	_defaultUser = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_defaultDomain = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))
	_flags = AskPasswordFlags(arg4)

	overrides.AskPassword(_message, _defaultUser, _defaultDomain, _flags)
}

//export _gotk4_gio2_MountOperationClass_ask_question
func _gotk4_gio2_MountOperationClass_ask_question(arg0 *C.GMountOperation, arg1 *C.char, arg2 **C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MountOperationOverrides](instance0)
	if overrides.AskQuestion == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MountOperationOverrides.AskQuestion, got none")
	}

	var _message string   // out
	var _choices []string // out

	_message = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	{
		var i int
		var z *C.char
		for p := arg2; *p != z; p = &unsafe.Slice(p, 2)[1] {
			i++
		}

		src := unsafe.Slice(arg2, i)
		_choices = make([]string, i)
		for i := range src {
			_choices[i] = C.GoString((*C.gchar)(unsafe.Pointer(src[i])))
		}
	}

	overrides.AskQuestion(_message, _choices)
}

//export _gotk4_gio2_MountOperationClass_reply
func _gotk4_gio2_MountOperationClass_reply(arg0 *C.GMountOperation, arg1 C.GMountOperationResult) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MountOperationOverrides](instance0)
	if overrides.Reply == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MountOperationOverrides.Reply, got none")
	}

	var _result MountOperationResult // out

	_result = MountOperationResult(arg1)

	overrides.Reply(_result)
}

//export _gotk4_gio2_MountOperationClass_show_unmount_progress
func _gotk4_gio2_MountOperationClass_show_unmount_progress(arg0 *C.GMountOperation, arg1 *C.gchar, arg2 C.gint64, arg3 C.gint64) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MountOperationOverrides](instance0)
	if overrides.ShowUnmountProgress == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MountOperationOverrides.ShowUnmountProgress, got none")
	}

	var _message string  // out
	var _timeLeft int64  // out
	var _bytesLeft int64 // out

	_message = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	_timeLeft = int64(arg2)
	_bytesLeft = int64(arg3)

	overrides.ShowUnmountProgress(_message, _timeLeft, _bytesLeft)
}

//export _gotk4_gio2_MountOperation_ConnectAborted
func _gotk4_gio2_MountOperation_ConnectAborted(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_MountOperation_ConnectAskPassword
func _gotk4_gio2_MountOperation_ConnectAskPassword(arg0 C.gpointer, arg1 *C.gchar, arg2 *C.gchar, arg3 *C.gchar, arg4 C.GAskPasswordFlags, arg5 C.guintptr) {
	var f func(message, defaultUser, defaultDomain string, flags AskPasswordFlags)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg5))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(message, defaultUser, defaultDomain string, flags AskPasswordFlags))
	}

	var _message string         // out
	var _defaultUser string     // out
	var _defaultDomain string   // out
	var _flags AskPasswordFlags // out

	_message = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	_defaultUser = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_defaultDomain = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))
	_flags = AskPasswordFlags(arg4)

	f(_message, _defaultUser, _defaultDomain, _flags)
}

//export _gotk4_gio2_MountOperation_ConnectAskQuestion
func _gotk4_gio2_MountOperation_ConnectAskQuestion(arg0 C.gpointer, arg1 *C.gchar, arg2 **C.gchar, arg3 C.guintptr) {
	var f func(message string, choices []string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(message string, choices []string))
	}

	var _message string   // out
	var _choices []string // out

	_message = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	{
		var i int
		var z *C.gchar
		for p := arg2; *p != z; p = &unsafe.Slice(p, 2)[1] {
			i++
		}

		src := unsafe.Slice(arg2, i)
		_choices = make([]string, i)
		for i := range src {
			_choices[i] = C.GoString((*C.gchar)(unsafe.Pointer(src[i])))
		}
	}

	f(_message, _choices)
}

//export _gotk4_gio2_MountOperation_ConnectReply
func _gotk4_gio2_MountOperation_ConnectReply(arg0 C.gpointer, arg1 C.GMountOperationResult, arg2 C.guintptr) {
	var f func(result MountOperationResult)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(result MountOperationResult))
	}

	var _result MountOperationResult // out

	_result = MountOperationResult(arg1)

	f(_result)
}

//export _gotk4_gio2_MountOperation_ConnectShowUnmountProgress
func _gotk4_gio2_MountOperation_ConnectShowUnmountProgress(arg0 C.gpointer, arg1 *C.gchar, arg2 C.gint64, arg3 C.gint64, arg4 C.guintptr) {
	var f func(message string, timeLeft, bytesLeft int64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(message string, timeLeft, bytesLeft int64))
	}

	var _message string  // out
	var _timeLeft int64  // out
	var _bytesLeft int64 // out

	_message = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	_timeLeft = int64(arg2)
	_bytesLeft = int64(arg3)

	f(_message, _timeLeft, _bytesLeft)
}

//export _gotk4_gio2_OutputStreamClass_close_finish
func _gotk4_gio2_OutputStreamClass_close_finish(arg0 *C.GOutputStream, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[OutputStreamOverrides](instance0)
	if overrides.CloseFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected OutputStreamOverrides.CloseFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	_goerr := overrides.CloseFinish(_result)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_OutputStreamClass_close_fn
func _gotk4_gio2_OutputStreamClass_close_fn(arg0 *C.GOutputStream, arg1 *C.GCancellable, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[OutputStreamOverrides](instance0)
	if overrides.CloseFn == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected OutputStreamOverrides.CloseFn, got none")
	}

	var _cancellable context.Context // out

	if arg1 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg1))
	}

	_goerr := overrides.CloseFn(_cancellable)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_OutputStreamClass_flush
func _gotk4_gio2_OutputStreamClass_flush(arg0 *C.GOutputStream, arg1 *C.GCancellable, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[OutputStreamOverrides](instance0)
	if overrides.Flush == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected OutputStreamOverrides.Flush, got none")
	}

	var _cancellable context.Context // out

	if arg1 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg1))
	}

	_goerr := overrides.Flush(_cancellable)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_OutputStreamClass_flush_finish
func _gotk4_gio2_OutputStreamClass_flush_finish(arg0 *C.GOutputStream, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[OutputStreamOverrides](instance0)
	if overrides.FlushFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected OutputStreamOverrides.FlushFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	_goerr := overrides.FlushFinish(_result)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_OutputStreamClass_splice
func _gotk4_gio2_OutputStreamClass_splice(arg0 *C.GOutputStream, arg1 *C.GInputStream, arg2 C.GOutputStreamSpliceFlags, arg3 *C.GCancellable, _cerr **C.GError) (cret C.gssize) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[OutputStreamOverrides](instance0)
	if overrides.Splice == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected OutputStreamOverrides.Splice, got none")
	}

	var _cancellable context.Context   // out
	var _source InputStreamer          // out
	var _flags OutputStreamSpliceFlags // out

	if arg3 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg3))
	}
	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.InputStreamer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(InputStreamer)
			return ok
		})
		rv, ok := casted.(InputStreamer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.InputStreamer")
		}
		_source = rv
	}
	_flags = OutputStreamSpliceFlags(arg2)

	gssize, _goerr := overrides.Splice(_cancellable, _source, _flags)

	var _ int
	var _ error

	cret = C.gssize(gssize)
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_OutputStreamClass_splice_finish
func _gotk4_gio2_OutputStreamClass_splice_finish(arg0 *C.GOutputStream, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.gssize) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[OutputStreamOverrides](instance0)
	if overrides.SpliceFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected OutputStreamOverrides.SpliceFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	gssize, _goerr := overrides.SpliceFinish(_result)

	var _ int
	var _ error

	cret = C.gssize(gssize)
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_OutputStreamClass_write_finish
func _gotk4_gio2_OutputStreamClass_write_finish(arg0 *C.GOutputStream, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.gssize) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[OutputStreamOverrides](instance0)
	if overrides.WriteFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected OutputStreamOverrides.WriteFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	gssize, _goerr := overrides.WriteFinish(_result)

	var _ int
	var _ error

	cret = C.gssize(gssize)
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_OutputStreamClass_writev_finish
func _gotk4_gio2_OutputStreamClass_writev_finish(arg0 *C.GOutputStream, arg1 *C.GAsyncResult, arg2 *C.gsize, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[OutputStreamOverrides](instance0)
	if overrides.WritevFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected OutputStreamOverrides.WritevFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	bytesWritten, _goerr := overrides.WritevFinish(_result)

	var _ uint
	var _ error

	*arg2 = C.gsize(bytesWritten)
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_PermissionClass_acquire
func _gotk4_gio2_PermissionClass_acquire(arg0 *C.GPermission, arg1 *C.GCancellable, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PermissionOverrides](instance0)
	if overrides.Acquire == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PermissionOverrides.Acquire, got none")
	}

	var _cancellable context.Context // out

	if arg1 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg1))
	}

	_goerr := overrides.Acquire(_cancellable)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_PermissionClass_acquire_finish
func _gotk4_gio2_PermissionClass_acquire_finish(arg0 *C.GPermission, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PermissionOverrides](instance0)
	if overrides.AcquireFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PermissionOverrides.AcquireFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	_goerr := overrides.AcquireFinish(_result)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_PermissionClass_release
func _gotk4_gio2_PermissionClass_release(arg0 *C.GPermission, arg1 *C.GCancellable, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PermissionOverrides](instance0)
	if overrides.Release == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PermissionOverrides.Release, got none")
	}

	var _cancellable context.Context // out

	if arg1 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg1))
	}

	_goerr := overrides.Release(_cancellable)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_PermissionClass_release_finish
func _gotk4_gio2_PermissionClass_release_finish(arg0 *C.GPermission, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PermissionOverrides](instance0)
	if overrides.ReleaseFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PermissionOverrides.ReleaseFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	_goerr := overrides.ReleaseFinish(_result)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_ResolverClass_lookup_by_address
func _gotk4_gio2_ResolverClass_lookup_by_address(arg0 *C.GResolver, arg1 *C.GInetAddress, arg2 *C.GCancellable, _cerr **C.GError) (cret *C.gchar) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ResolverOverrides](instance0)
	if overrides.LookupByAddress == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ResolverOverrides.LookupByAddress, got none")
	}

	var _cancellable context.Context // out
	var _address *InetAddress        // out

	if arg2 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg2))
	}
	_address = wrapInetAddress(coreglib.Take(unsafe.Pointer(arg1)))

	utf8, _goerr := overrides.LookupByAddress(_cancellable, _address)

	var _ string
	var _ error

	cret = (*C.gchar)(unsafe.Pointer(C.CString(utf8)))
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_ResolverClass_lookup_by_address_finish
func _gotk4_gio2_ResolverClass_lookup_by_address_finish(arg0 *C.GResolver, arg1 *C.GAsyncResult, _cerr **C.GError) (cret *C.gchar) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ResolverOverrides](instance0)
	if overrides.LookupByAddressFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ResolverOverrides.LookupByAddressFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	utf8, _goerr := overrides.LookupByAddressFinish(_result)

	var _ string
	var _ error

	cret = (*C.gchar)(unsafe.Pointer(C.CString(utf8)))
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_ResolverClass_lookup_by_name
func _gotk4_gio2_ResolverClass_lookup_by_name(arg0 *C.GResolver, arg1 *C.gchar, arg2 *C.GCancellable, _cerr **C.GError) (cret *C.GList) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ResolverOverrides](instance0)
	if overrides.LookupByName == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ResolverOverrides.LookupByName, got none")
	}

	var _cancellable context.Context // out
	var _hostname string             // out

	if arg2 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg2))
	}
	_hostname = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	list, _goerr := overrides.LookupByName(_cancellable, _hostname)

	var _ []*InetAddress
	var _ error

	for i := len(list) - 1; i >= 0; i-- {
		src := list[i]
		var dst *C.GInetAddress // out
		dst = (*C.GInetAddress)(unsafe.Pointer(coreglib.InternObject(src).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(src).Native()))
		cret = C.g_list_prepend(cret, C.gpointer(unsafe.Pointer(dst)))
	}
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_ResolverClass_lookup_by_name_finish
func _gotk4_gio2_ResolverClass_lookup_by_name_finish(arg0 *C.GResolver, arg1 *C.GAsyncResult, _cerr **C.GError) (cret *C.GList) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ResolverOverrides](instance0)
	if overrides.LookupByNameFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ResolverOverrides.LookupByNameFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	list, _goerr := overrides.LookupByNameFinish(_result)

	var _ []*InetAddress
	var _ error

	for i := len(list) - 1; i >= 0; i-- {
		src := list[i]
		var dst *C.GInetAddress // out
		dst = (*C.GInetAddress)(unsafe.Pointer(coreglib.InternObject(src).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(src).Native()))
		cret = C.g_list_prepend(cret, C.gpointer(unsafe.Pointer(dst)))
	}
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_ResolverClass_lookup_by_name_with_flags
func _gotk4_gio2_ResolverClass_lookup_by_name_with_flags(arg0 *C.GResolver, arg1 *C.gchar, arg2 C.GResolverNameLookupFlags, arg3 *C.GCancellable, _cerr **C.GError) (cret *C.GList) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ResolverOverrides](instance0)
	if overrides.LookupByNameWithFlags == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ResolverOverrides.LookupByNameWithFlags, got none")
	}

	var _cancellable context.Context   // out
	var _hostname string               // out
	var _flags ResolverNameLookupFlags // out

	if arg3 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg3))
	}
	_hostname = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	_flags = ResolverNameLookupFlags(arg2)

	list, _goerr := overrides.LookupByNameWithFlags(_cancellable, _hostname, _flags)

	var _ []*InetAddress
	var _ error

	for i := len(list) - 1; i >= 0; i-- {
		src := list[i]
		var dst *C.GInetAddress // out
		dst = (*C.GInetAddress)(unsafe.Pointer(coreglib.InternObject(src).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(src).Native()))
		cret = C.g_list_prepend(cret, C.gpointer(unsafe.Pointer(dst)))
	}
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_ResolverClass_lookup_by_name_with_flags_finish
func _gotk4_gio2_ResolverClass_lookup_by_name_with_flags_finish(arg0 *C.GResolver, arg1 *C.GAsyncResult, _cerr **C.GError) (cret *C.GList) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ResolverOverrides](instance0)
	if overrides.LookupByNameWithFlagsFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ResolverOverrides.LookupByNameWithFlagsFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	list, _goerr := overrides.LookupByNameWithFlagsFinish(_result)

	var _ []*InetAddress
	var _ error

	for i := len(list) - 1; i >= 0; i-- {
		src := list[i]
		var dst *C.GInetAddress // out
		dst = (*C.GInetAddress)(unsafe.Pointer(coreglib.InternObject(src).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(src).Native()))
		cret = C.g_list_prepend(cret, C.gpointer(unsafe.Pointer(dst)))
	}
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_ResolverClass_lookup_records
func _gotk4_gio2_ResolverClass_lookup_records(arg0 *C.GResolver, arg1 *C.gchar, arg2 C.GResolverRecordType, arg3 *C.GCancellable, _cerr **C.GError) (cret *C.GList) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ResolverOverrides](instance0)
	if overrides.LookupRecords == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ResolverOverrides.LookupRecords, got none")
	}

	var _cancellable context.Context   // out
	var _rrname string                 // out
	var _recordType ResolverRecordType // out

	if arg3 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg3))
	}
	_rrname = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	_recordType = ResolverRecordType(arg2)

	list, _goerr := overrides.LookupRecords(_cancellable, _rrname, _recordType)

	var _ []*glib.Variant
	var _ error

	for i := len(list) - 1; i >= 0; i-- {
		src := list[i]
		var dst *C.GVariant // out
		dst = (*C.GVariant)(gextras.StructNative(unsafe.Pointer(src)))
		cret = C.g_list_prepend(cret, C.gpointer(unsafe.Pointer(dst)))
	}
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_ResolverClass_lookup_records_finish
func _gotk4_gio2_ResolverClass_lookup_records_finish(arg0 *C.GResolver, arg1 *C.GAsyncResult, _cerr **C.GError) (cret *C.GList) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ResolverOverrides](instance0)
	if overrides.LookupRecordsFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ResolverOverrides.LookupRecordsFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	list, _goerr := overrides.LookupRecordsFinish(_result)

	var _ []*glib.Variant
	var _ error

	for i := len(list) - 1; i >= 0; i-- {
		src := list[i]
		var dst *C.GVariant // out
		dst = (*C.GVariant)(gextras.StructNative(unsafe.Pointer(src)))
		cret = C.g_list_prepend(cret, C.gpointer(unsafe.Pointer(dst)))
	}
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_ResolverClass_lookup_service_finish
func _gotk4_gio2_ResolverClass_lookup_service_finish(arg0 *C.GResolver, arg1 *C.GAsyncResult, _cerr **C.GError) (cret *C.GList) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ResolverOverrides](instance0)
	if overrides.LookupServiceFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ResolverOverrides.LookupServiceFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	list, _goerr := overrides.LookupServiceFinish(_result)

	var _ []*SrvTarget
	var _ error

	for i := len(list) - 1; i >= 0; i-- {
		src := list[i]
		var dst *C.GSrvTarget // out
		dst = (*C.GSrvTarget)(gextras.StructNative(unsafe.Pointer(src)))
		runtime.SetFinalizer(gextras.StructIntern(unsafe.Pointer(src)), nil)
		cret = C.g_list_prepend(cret, C.gpointer(unsafe.Pointer(dst)))
	}
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_ResolverClass_reload
func _gotk4_gio2_ResolverClass_reload(arg0 *C.GResolver) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ResolverOverrides](instance0)
	if overrides.Reload == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ResolverOverrides.Reload, got none")
	}

	overrides.Reload()
}

//export _gotk4_gio2_Resolver_ConnectReload
func _gotk4_gio2_Resolver_ConnectReload(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gio2_SettingsClass_change_event
func _gotk4_gio2_SettingsClass_change_event(arg0 *C.GSettings, arg1 *C.GQuark, arg2 C.gint) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SettingsOverrides](instance0)
	if overrides.ChangeEvent == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SettingsOverrides.ChangeEvent, got none")
	}

	var _keys *glib.Quark // out
	var _nKeys int        // out

	_keys = (*glib.Quark)(unsafe.Pointer(arg1))
	_nKeys = int(arg2)

	ok := overrides.ChangeEvent(_keys, _nKeys)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_SettingsClass_changed
func _gotk4_gio2_SettingsClass_changed(arg0 *C.GSettings, arg1 *C.gchar) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SettingsOverrides](instance0)
	if overrides.Changed == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SettingsOverrides.Changed, got none")
	}

	var _key string // out

	_key = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	overrides.Changed(_key)
}

//export _gotk4_gio2_SettingsClass_writable_change_event
func _gotk4_gio2_SettingsClass_writable_change_event(arg0 *C.GSettings, arg1 C.GQuark) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SettingsOverrides](instance0)
	if overrides.WritableChangeEvent == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SettingsOverrides.WritableChangeEvent, got none")
	}

	var _key glib.Quark // out

	_key = glib.Quark(arg1)

	ok := overrides.WritableChangeEvent(_key)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_SettingsClass_writable_changed
func _gotk4_gio2_SettingsClass_writable_changed(arg0 *C.GSettings, arg1 *C.gchar) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SettingsOverrides](instance0)
	if overrides.WritableChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SettingsOverrides.WritableChanged, got none")
	}

	var _key string // out

	_key = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	overrides.WritableChanged(_key)
}

//export _gotk4_gio2_Settings_ConnectChangeEvent
func _gotk4_gio2_Settings_ConnectChangeEvent(arg0 C.gpointer, arg1 C.gpointer, arg2 C.gint, arg3 C.guintptr) (cret C.gboolean) {
	var f func(keys []glib.Quark) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(keys []glib.Quark) (ok bool))
	}

	var _keys []glib.Quark // out

	_keys = make([]glib.Quark, arg2)
	copy(_keys, unsafe.Slice((*glib.Quark)(unsafe.Pointer(arg1)), arg2))

	ok := f(_keys)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_Settings_ConnectChanged
func _gotk4_gio2_Settings_ConnectChanged(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(key string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(key string))
	}

	var _key string // out

	_key = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_key)
}

//export _gotk4_gio2_Settings_ConnectWritableChangeEvent
func _gotk4_gio2_Settings_ConnectWritableChangeEvent(arg0 C.gpointer, arg1 C.guint, arg2 C.guintptr) (cret C.gboolean) {
	var f func(key uint) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(key uint) (ok bool))
	}

	var _key uint // out

	_key = uint(arg1)

	ok := f(_key)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_Settings_ConnectWritableChanged
func _gotk4_gio2_Settings_ConnectWritableChanged(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(key string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(key string))
	}

	var _key string // out

	_key = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_key)
}

//export _gotk4_gio2_SimpleAction_ConnectActivate
func _gotk4_gio2_SimpleAction_ConnectActivate(arg0 C.gpointer, arg1 *C.GVariant, arg2 C.guintptr) {
	var f func(parameter *glib.Variant)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(parameter *glib.Variant))
	}

	var _parameter *glib.Variant // out

	if arg1 != nil {
		_parameter = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg1)))
		C.g_variant_ref(arg1)
		runtime.SetFinalizer(
			gextras.StructIntern(unsafe.Pointer(_parameter)),
			func(intern *struct{ C unsafe.Pointer }) {
				C.g_variant_unref((*C.GVariant)(intern.C))
			},
		)
	}

	f(_parameter)
}

//export _gotk4_gio2_SimpleAction_ConnectChangeState
func _gotk4_gio2_SimpleAction_ConnectChangeState(arg0 C.gpointer, arg1 *C.GVariant, arg2 C.guintptr) {
	var f func(value *glib.Variant)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(value *glib.Variant))
	}

	var _value *glib.Variant // out

	if arg1 != nil {
		_value = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg1)))
		C.g_variant_ref(arg1)
		runtime.SetFinalizer(
			gextras.StructIntern(unsafe.Pointer(_value)),
			func(intern *struct{ C unsafe.Pointer }) {
				C.g_variant_unref((*C.GVariant)(intern.C))
			},
		)
	}

	f(_value)
}

//export _gotk4_gio2_SocketAddressClass_get_family
func _gotk4_gio2_SocketAddressClass_get_family(arg0 *C.GSocketAddress) (cret C.GSocketFamily) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SocketAddressOverrides](instance0)
	if overrides.Family == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SocketAddressOverrides.Family, got none")
	}

	socketFamily := overrides.Family()

	var _ SocketFamily

	cret = C.GSocketFamily(socketFamily)

	return cret
}

//export _gotk4_gio2_SocketAddressClass_get_native_size
func _gotk4_gio2_SocketAddressClass_get_native_size(arg0 *C.GSocketAddress) (cret C.gssize) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SocketAddressOverrides](instance0)
	if overrides.NativeSize == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SocketAddressOverrides.NativeSize, got none")
	}

	gssize := overrides.NativeSize()

	var _ int

	cret = C.gssize(gssize)

	return cret
}

//export _gotk4_gio2_SocketAddressClass_to_native
func _gotk4_gio2_SocketAddressClass_to_native(arg0 *C.GSocketAddress, arg1 C.gpointer, arg2 C.gsize, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SocketAddressOverrides](instance0)
	if overrides.ToNative == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SocketAddressOverrides.ToNative, got none")
	}

	var _dest unsafe.Pointer // out
	var _destlen uint        // out

	_dest = (unsafe.Pointer)(unsafe.Pointer(arg1))
	_destlen = uint(arg2)

	_goerr := overrides.ToNative(_dest, _destlen)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_SocketAddressEnumeratorClass_next
func _gotk4_gio2_SocketAddressEnumeratorClass_next(arg0 *C.GSocketAddressEnumerator, arg1 *C.GCancellable, _cerr **C.GError) (cret *C.GSocketAddress) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SocketAddressEnumeratorOverrides](instance0)
	if overrides.Next == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SocketAddressEnumeratorOverrides.Next, got none")
	}

	var _cancellable context.Context // out

	if arg1 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg1))
	}

	socketAddress, _goerr := overrides.Next(_cancellable)

	var _ SocketAddresser
	var _ error

	if socketAddress != nil {
		cret = (*C.GSocketAddress)(unsafe.Pointer(coreglib.InternObject(socketAddress).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(socketAddress).Native()))
	}
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_SocketAddressEnumeratorClass_next_finish
func _gotk4_gio2_SocketAddressEnumeratorClass_next_finish(arg0 *C.GSocketAddressEnumerator, arg1 *C.GAsyncResult, _cerr **C.GError) (cret *C.GSocketAddress) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SocketAddressEnumeratorOverrides](instance0)
	if overrides.NextFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SocketAddressEnumeratorOverrides.NextFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	socketAddress, _goerr := overrides.NextFinish(_result)

	var _ SocketAddresser
	var _ error

	if socketAddress != nil {
		cret = (*C.GSocketAddress)(unsafe.Pointer(coreglib.InternObject(socketAddress).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(socketAddress).Native()))
	}
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_SocketClientClass_event
func _gotk4_gio2_SocketClientClass_event(arg0 *C.GSocketClient, arg1 C.GSocketClientEvent, arg2 *C.GSocketConnectable, arg3 *C.GIOStream) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SocketClientOverrides](instance0)
	if overrides.Event == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SocketClientOverrides.Event, got none")
	}

	var _event SocketClientEvent        // out
	var _connectable SocketConnectabler // out
	var _connection IOStreamer          // out

	_event = SocketClientEvent(arg1)
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gio.SocketConnectabler is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(SocketConnectabler)
			return ok
		})
		rv, ok := casted.(SocketConnectabler)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.SocketConnectabler")
		}
		_connectable = rv
	}
	{
		objptr := unsafe.Pointer(arg3)
		if objptr == nil {
			panic("object of type gio.IOStreamer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(IOStreamer)
			return ok
		})
		rv, ok := casted.(IOStreamer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.IOStreamer")
		}
		_connection = rv
	}

	overrides.Event(_event, _connectable, _connection)
}

//export _gotk4_gio2_SocketClient_ConnectEvent
func _gotk4_gio2_SocketClient_ConnectEvent(arg0 C.gpointer, arg1 C.GSocketClientEvent, arg2 *C.GSocketConnectable, arg3 *C.GIOStream, arg4 C.guintptr) {
	var f func(event SocketClientEvent, connectable SocketConnectabler, connection IOStreamer)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(event SocketClientEvent, connectable SocketConnectabler, connection IOStreamer))
	}

	var _event SocketClientEvent        // out
	var _connectable SocketConnectabler // out
	var _connection IOStreamer          // out

	_event = SocketClientEvent(arg1)
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gio.SocketConnectabler is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(SocketConnectabler)
			return ok
		})
		rv, ok := casted.(SocketConnectabler)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.SocketConnectabler")
		}
		_connectable = rv
	}
	if arg3 != nil {
		{
			objptr := unsafe.Pointer(arg3)

			object := coreglib.Take(objptr)
			casted := object.WalkCast(func(obj coreglib.Objector) bool {
				_, ok := obj.(IOStreamer)
				return ok
			})
			rv, ok := casted.(IOStreamer)
			if !ok {
				panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.IOStreamer")
			}
			_connection = rv
		}
	}

	f(_event, _connectable, _connection)
}

//export _gotk4_gio2_SocketControlMessageClass_get_level
func _gotk4_gio2_SocketControlMessageClass_get_level(arg0 *C.GSocketControlMessage) (cret C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SocketControlMessageOverrides](instance0)
	if overrides.Level == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SocketControlMessageOverrides.Level, got none")
	}

	gint := overrides.Level()

	var _ int

	cret = C.int(gint)

	return cret
}

//export _gotk4_gio2_SocketControlMessageClass_get_size
func _gotk4_gio2_SocketControlMessageClass_get_size(arg0 *C.GSocketControlMessage) (cret C.gsize) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SocketControlMessageOverrides](instance0)
	if overrides.Size == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SocketControlMessageOverrides.Size, got none")
	}

	gsize := overrides.Size()

	var _ uint

	cret = C.gsize(gsize)

	return cret
}

//export _gotk4_gio2_SocketControlMessageClass_get_type
func _gotk4_gio2_SocketControlMessageClass_get_type(arg0 *C.GSocketControlMessage) (cret C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SocketControlMessageOverrides](instance0)
	if overrides.Type == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SocketControlMessageOverrides.Type, got none")
	}

	gint := overrides.Type()

	var _ int

	cret = C.int(gint)

	return cret
}

//export _gotk4_gio2_SocketControlMessageClass_serialize
func _gotk4_gio2_SocketControlMessageClass_serialize(arg0 *C.GSocketControlMessage, arg1 C.gpointer) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SocketControlMessageOverrides](instance0)
	if overrides.Serialize == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SocketControlMessageOverrides.Serialize, got none")
	}

	var _data unsafe.Pointer // out

	_data = (unsafe.Pointer)(unsafe.Pointer(arg1))

	overrides.Serialize(_data)
}

//export _gotk4_gio2_SocketListenerClass_changed
func _gotk4_gio2_SocketListenerClass_changed(arg0 *C.GSocketListener) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SocketListenerOverrides](instance0)
	if overrides.Changed == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SocketListenerOverrides.Changed, got none")
	}

	overrides.Changed()
}

//export _gotk4_gio2_SocketListenerClass_event
func _gotk4_gio2_SocketListenerClass_event(arg0 *C.GSocketListener, arg1 C.GSocketListenerEvent, arg2 *C.GSocket) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SocketListenerOverrides](instance0)
	if overrides.Event == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SocketListenerOverrides.Event, got none")
	}

	var _event SocketListenerEvent // out
	var _socket *Socket            // out

	_event = SocketListenerEvent(arg1)
	_socket = wrapSocket(coreglib.Take(unsafe.Pointer(arg2)))

	overrides.Event(_event, _socket)
}

//export _gotk4_gio2_SocketListener_ConnectEvent
func _gotk4_gio2_SocketListener_ConnectEvent(arg0 C.gpointer, arg1 C.GSocketListenerEvent, arg2 *C.GSocket, arg3 C.guintptr) {
	var f func(event SocketListenerEvent, socket *Socket)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(event SocketListenerEvent, socket *Socket))
	}

	var _event SocketListenerEvent // out
	var _socket *Socket            // out

	_event = SocketListenerEvent(arg1)
	_socket = wrapSocket(coreglib.Take(unsafe.Pointer(arg2)))

	f(_event, _socket)
}

//export _gotk4_gio2_SocketServiceClass_incoming
func _gotk4_gio2_SocketServiceClass_incoming(arg0 *C.GSocketService, arg1 *C.GSocketConnection, arg2 *C.GObject) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SocketServiceOverrides](instance0)
	if overrides.Incoming == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SocketServiceOverrides.Incoming, got none")
	}

	var _connection *SocketConnection  // out
	var _sourceObject *coreglib.Object // out

	_connection = wrapSocketConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_sourceObject = coreglib.Take(unsafe.Pointer(arg2))

	ok := overrides.Incoming(_connection, _sourceObject)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_SocketService_ConnectIncoming
func _gotk4_gio2_SocketService_ConnectIncoming(arg0 C.gpointer, arg1 *C.GSocketConnection, arg2 *C.GObject, arg3 C.guintptr) (cret C.gboolean) {
	var f func(connection *SocketConnection, sourceObject *coreglib.Object) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(connection *SocketConnection, sourceObject *coreglib.Object) (ok bool))
	}

	var _connection *SocketConnection  // out
	var _sourceObject *coreglib.Object // out

	_connection = wrapSocketConnection(coreglib.Take(unsafe.Pointer(arg1)))
	if arg2 != nil {
		_sourceObject = coreglib.Take(unsafe.Pointer(arg2))
	}

	ok := f(_connection, _sourceObject)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_ThreadedSocketServiceClass_run
func _gotk4_gio2_ThreadedSocketServiceClass_run(arg0 *C.GThreadedSocketService, arg1 *C.GSocketConnection, arg2 *C.GObject) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ThreadedSocketServiceOverrides](instance0)
	if overrides.Run == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ThreadedSocketServiceOverrides.Run, got none")
	}

	var _connection *SocketConnection  // out
	var _sourceObject *coreglib.Object // out

	_connection = wrapSocketConnection(coreglib.Take(unsafe.Pointer(arg1)))
	_sourceObject = coreglib.Take(unsafe.Pointer(arg2))

	ok := overrides.Run(_connection, _sourceObject)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_ThreadedSocketService_ConnectRun
func _gotk4_gio2_ThreadedSocketService_ConnectRun(arg0 C.gpointer, arg1 *C.GSocketConnection, arg2 *C.GObject, arg3 C.guintptr) (cret C.gboolean) {
	var f func(connection *SocketConnection, sourceObject *coreglib.Object) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(connection *SocketConnection, sourceObject *coreglib.Object) (ok bool))
	}

	var _connection *SocketConnection  // out
	var _sourceObject *coreglib.Object // out

	_connection = wrapSocketConnection(coreglib.Take(unsafe.Pointer(arg1)))
	if arg2 != nil {
		_sourceObject = coreglib.Take(unsafe.Pointer(arg2))
	}

	ok := f(_connection, _sourceObject)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_TlsCertificateClass_verify
func _gotk4_gio2_TlsCertificateClass_verify(arg0 *C.GTlsCertificate, arg1 *C.GSocketConnectable, arg2 *C.GTlsCertificate) (cret C.GTlsCertificateFlags) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSCertificateOverrides](instance0)
	if overrides.Verify == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSCertificateOverrides.Verify, got none")
	}

	var _identity SocketConnectabler // out
	var _trustedCa TLSCertificater   // out

	if arg1 != nil {
		{
			objptr := unsafe.Pointer(arg1)

			object := coreglib.Take(objptr)
			casted := object.WalkCast(func(obj coreglib.Objector) bool {
				_, ok := obj.(SocketConnectabler)
				return ok
			})
			rv, ok := casted.(SocketConnectabler)
			if !ok {
				panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.SocketConnectabler")
			}
			_identity = rv
		}
	}
	if arg2 != nil {
		{
			objptr := unsafe.Pointer(arg2)

			object := coreglib.Take(objptr)
			casted := object.WalkCast(func(obj coreglib.Objector) bool {
				_, ok := obj.(TLSCertificater)
				return ok
			})
			rv, ok := casted.(TLSCertificater)
			if !ok {
				panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.TLSCertificater")
			}
			_trustedCa = rv
		}
	}

	tlsCertificateFlags := overrides.Verify(_identity, _trustedCa)

	var _ TLSCertificateFlags

	cret = C.GTlsCertificateFlags(tlsCertificateFlags)

	return cret
}

//export _gotk4_gio2_TlsConnectionClass_accept_certificate
func _gotk4_gio2_TlsConnectionClass_accept_certificate(arg0 *C.GTlsConnection, arg1 *C.GTlsCertificate, arg2 C.GTlsCertificateFlags) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSConnectionOverrides](instance0)
	if overrides.AcceptCertificate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSConnectionOverrides.AcceptCertificate, got none")
	}

	var _peerCert TLSCertificater   // out
	var _errors TLSCertificateFlags // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.TLSCertificater is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TLSCertificater)
			return ok
		})
		rv, ok := casted.(TLSCertificater)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.TLSCertificater")
		}
		_peerCert = rv
	}
	_errors = TLSCertificateFlags(arg2)

	ok := overrides.AcceptCertificate(_peerCert, _errors)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_TlsConnectionClass_get_binding_data
func _gotk4_gio2_TlsConnectionClass_get_binding_data(arg0 *C.GTlsConnection, arg1 C.GTlsChannelBindingType, arg2 *C.GByteArray, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSConnectionOverrides](instance0)
	if overrides.BindingData == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSConnectionOverrides.BindingData, got none")
	}

	var _typ TLSChannelBindingType // out
	var _data []byte               // out

	_typ = TLSChannelBindingType(arg1)
	_data = make([]byte, arg2.len)
	copy(_data, unsafe.Slice((*byte)(arg2.data), arg2.len))

	_goerr := overrides.BindingData(_typ, _data)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_TlsConnectionClass_get_negotiated_protocol
func _gotk4_gio2_TlsConnectionClass_get_negotiated_protocol(arg0 *C.GTlsConnection) (cret *C.gchar) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSConnectionOverrides](instance0)
	if overrides.NegotiatedProtocol == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSConnectionOverrides.NegotiatedProtocol, got none")
	}

	utf8 := overrides.NegotiatedProtocol()

	var _ string

	if utf8 != "" {
		cret = (*C.gchar)(unsafe.Pointer(C.CString(utf8)))
		defer C.free(unsafe.Pointer(cret))
	}

	return cret
}

//export _gotk4_gio2_TlsConnectionClass_handshake
func _gotk4_gio2_TlsConnectionClass_handshake(arg0 *C.GTlsConnection, arg1 *C.GCancellable, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSConnectionOverrides](instance0)
	if overrides.Handshake == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSConnectionOverrides.Handshake, got none")
	}

	var _cancellable context.Context // out

	if arg1 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg1))
	}

	_goerr := overrides.Handshake(_cancellable)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_TlsConnectionClass_handshake_finish
func _gotk4_gio2_TlsConnectionClass_handshake_finish(arg0 *C.GTlsConnection, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSConnectionOverrides](instance0)
	if overrides.HandshakeFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSConnectionOverrides.HandshakeFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	_goerr := overrides.HandshakeFinish(_result)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_TlsConnection_ConnectAcceptCertificate
func _gotk4_gio2_TlsConnection_ConnectAcceptCertificate(arg0 C.gpointer, arg1 *C.GTlsCertificate, arg2 C.GTlsCertificateFlags, arg3 C.guintptr) (cret C.gboolean) {
	var f func(peerCert TLSCertificater, errors TLSCertificateFlags) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(peerCert TLSCertificater, errors TLSCertificateFlags) (ok bool))
	}

	var _peerCert TLSCertificater   // out
	var _errors TLSCertificateFlags // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.TLSCertificater is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TLSCertificater)
			return ok
		})
		rv, ok := casted.(TLSCertificater)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.TLSCertificater")
		}
		_peerCert = rv
	}
	_errors = TLSCertificateFlags(arg2)

	ok := f(_peerCert, _errors)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_TlsDatabaseClass_create_certificate_handle
func _gotk4_gio2_TlsDatabaseClass_create_certificate_handle(arg0 *C.GTlsDatabase, arg1 *C.GTlsCertificate) (cret *C.gchar) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSDatabaseOverrides](instance0)
	if overrides.CreateCertificateHandle == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSDatabaseOverrides.CreateCertificateHandle, got none")
	}

	var _certificate TLSCertificater // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.TLSCertificater is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TLSCertificater)
			return ok
		})
		rv, ok := casted.(TLSCertificater)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.TLSCertificater")
		}
		_certificate = rv
	}

	utf8 := overrides.CreateCertificateHandle(_certificate)

	var _ string

	if utf8 != "" {
		cret = (*C.gchar)(unsafe.Pointer(C.CString(utf8)))
	}

	return cret
}

//export _gotk4_gio2_TlsDatabaseClass_lookup_certificate_for_handle
func _gotk4_gio2_TlsDatabaseClass_lookup_certificate_for_handle(arg0 *C.GTlsDatabase, arg1 *C.gchar, arg2 *C.GTlsInteraction, arg3 C.GTlsDatabaseLookupFlags, arg4 *C.GCancellable, _cerr **C.GError) (cret *C.GTlsCertificate) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSDatabaseOverrides](instance0)
	if overrides.LookupCertificateForHandle == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSDatabaseOverrides.LookupCertificateForHandle, got none")
	}

	var _cancellable context.Context  // out
	var _handle string                // out
	var _interaction *TLSInteraction  // out
	var _flags TLSDatabaseLookupFlags // out

	if arg4 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg4))
	}
	_handle = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	if arg2 != nil {
		_interaction = wrapTLSInteraction(coreglib.Take(unsafe.Pointer(arg2)))
	}
	_flags = TLSDatabaseLookupFlags(arg3)

	tlsCertificate, _goerr := overrides.LookupCertificateForHandle(_cancellable, _handle, _interaction, _flags)

	var _ TLSCertificater
	var _ error

	if tlsCertificate != nil {
		cret = (*C.GTlsCertificate)(unsafe.Pointer(coreglib.InternObject(tlsCertificate).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(tlsCertificate).Native()))
	}
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_TlsDatabaseClass_lookup_certificate_for_handle_finish
func _gotk4_gio2_TlsDatabaseClass_lookup_certificate_for_handle_finish(arg0 *C.GTlsDatabase, arg1 *C.GAsyncResult, _cerr **C.GError) (cret *C.GTlsCertificate) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSDatabaseOverrides](instance0)
	if overrides.LookupCertificateForHandleFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSDatabaseOverrides.LookupCertificateForHandleFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	tlsCertificate, _goerr := overrides.LookupCertificateForHandleFinish(_result)

	var _ TLSCertificater
	var _ error

	cret = (*C.GTlsCertificate)(unsafe.Pointer(coreglib.InternObject(tlsCertificate).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(tlsCertificate).Native()))
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_TlsDatabaseClass_lookup_certificate_issuer
func _gotk4_gio2_TlsDatabaseClass_lookup_certificate_issuer(arg0 *C.GTlsDatabase, arg1 *C.GTlsCertificate, arg2 *C.GTlsInteraction, arg3 C.GTlsDatabaseLookupFlags, arg4 *C.GCancellable, _cerr **C.GError) (cret *C.GTlsCertificate) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSDatabaseOverrides](instance0)
	if overrides.LookupCertificateIssuer == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSDatabaseOverrides.LookupCertificateIssuer, got none")
	}

	var _cancellable context.Context  // out
	var _certificate TLSCertificater  // out
	var _interaction *TLSInteraction  // out
	var _flags TLSDatabaseLookupFlags // out

	if arg4 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg4))
	}
	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.TLSCertificater is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TLSCertificater)
			return ok
		})
		rv, ok := casted.(TLSCertificater)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.TLSCertificater")
		}
		_certificate = rv
	}
	if arg2 != nil {
		_interaction = wrapTLSInteraction(coreglib.Take(unsafe.Pointer(arg2)))
	}
	_flags = TLSDatabaseLookupFlags(arg3)

	tlsCertificate, _goerr := overrides.LookupCertificateIssuer(_cancellable, _certificate, _interaction, _flags)

	var _ TLSCertificater
	var _ error

	cret = (*C.GTlsCertificate)(unsafe.Pointer(coreglib.InternObject(tlsCertificate).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(tlsCertificate).Native()))
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_TlsDatabaseClass_lookup_certificate_issuer_finish
func _gotk4_gio2_TlsDatabaseClass_lookup_certificate_issuer_finish(arg0 *C.GTlsDatabase, arg1 *C.GAsyncResult, _cerr **C.GError) (cret *C.GTlsCertificate) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSDatabaseOverrides](instance0)
	if overrides.LookupCertificateIssuerFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSDatabaseOverrides.LookupCertificateIssuerFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	tlsCertificate, _goerr := overrides.LookupCertificateIssuerFinish(_result)

	var _ TLSCertificater
	var _ error

	cret = (*C.GTlsCertificate)(unsafe.Pointer(coreglib.InternObject(tlsCertificate).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(tlsCertificate).Native()))
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_TlsDatabaseClass_lookup_certificates_issued_by
func _gotk4_gio2_TlsDatabaseClass_lookup_certificates_issued_by(arg0 *C.GTlsDatabase, arg1 *C.GByteArray, arg2 *C.GTlsInteraction, arg3 C.GTlsDatabaseLookupFlags, arg4 *C.GCancellable, _cerr **C.GError) (cret *C.GList) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSDatabaseOverrides](instance0)
	if overrides.LookupCertificatesIssuedBy == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSDatabaseOverrides.LookupCertificatesIssuedBy, got none")
	}

	var _cancellable context.Context  // out
	var _issuerRawDn []byte           // out
	var _interaction *TLSInteraction  // out
	var _flags TLSDatabaseLookupFlags // out

	if arg4 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg4))
	}
	_issuerRawDn = make([]byte, arg1.len)
	copy(_issuerRawDn, unsafe.Slice((*byte)(arg1.data), arg1.len))
	if arg2 != nil {
		_interaction = wrapTLSInteraction(coreglib.Take(unsafe.Pointer(arg2)))
	}
	_flags = TLSDatabaseLookupFlags(arg3)

	list, _goerr := overrides.LookupCertificatesIssuedBy(_cancellable, _issuerRawDn, _interaction, _flags)

	var _ []TLSCertificater
	var _ error

	for i := len(list) - 1; i >= 0; i-- {
		src := list[i]
		var dst *C.GTlsCertificate // out
		dst = (*C.GTlsCertificate)(unsafe.Pointer(coreglib.InternObject(src).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(src).Native()))
		cret = C.g_list_prepend(cret, C.gpointer(unsafe.Pointer(dst)))
	}
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_TlsDatabaseClass_lookup_certificates_issued_by_finish
func _gotk4_gio2_TlsDatabaseClass_lookup_certificates_issued_by_finish(arg0 *C.GTlsDatabase, arg1 *C.GAsyncResult, _cerr **C.GError) (cret *C.GList) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSDatabaseOverrides](instance0)
	if overrides.LookupCertificatesIssuedByFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSDatabaseOverrides.LookupCertificatesIssuedByFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	list, _goerr := overrides.LookupCertificatesIssuedByFinish(_result)

	var _ []TLSCertificater
	var _ error

	for i := len(list) - 1; i >= 0; i-- {
		src := list[i]
		var dst *C.GTlsCertificate // out
		dst = (*C.GTlsCertificate)(unsafe.Pointer(coreglib.InternObject(src).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(src).Native()))
		cret = C.g_list_prepend(cret, C.gpointer(unsafe.Pointer(dst)))
	}
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_TlsDatabaseClass_verify_chain
func _gotk4_gio2_TlsDatabaseClass_verify_chain(arg0 *C.GTlsDatabase, arg1 *C.GTlsCertificate, arg2 *C.gchar, arg3 *C.GSocketConnectable, arg4 *C.GTlsInteraction, arg5 C.GTlsDatabaseVerifyFlags, arg6 *C.GCancellable, _cerr **C.GError) (cret C.GTlsCertificateFlags) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSDatabaseOverrides](instance0)
	if overrides.VerifyChain == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSDatabaseOverrides.VerifyChain, got none")
	}

	var _cancellable context.Context  // out
	var _chain TLSCertificater        // out
	var _purpose string               // out
	var _identity SocketConnectabler  // out
	var _interaction *TLSInteraction  // out
	var _flags TLSDatabaseVerifyFlags // out

	if arg6 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg6))
	}
	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.TLSCertificater is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TLSCertificater)
			return ok
		})
		rv, ok := casted.(TLSCertificater)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.TLSCertificater")
		}
		_chain = rv
	}
	_purpose = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	if arg3 != nil {
		{
			objptr := unsafe.Pointer(arg3)

			object := coreglib.Take(objptr)
			casted := object.WalkCast(func(obj coreglib.Objector) bool {
				_, ok := obj.(SocketConnectabler)
				return ok
			})
			rv, ok := casted.(SocketConnectabler)
			if !ok {
				panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.SocketConnectabler")
			}
			_identity = rv
		}
	}
	if arg4 != nil {
		_interaction = wrapTLSInteraction(coreglib.Take(unsafe.Pointer(arg4)))
	}
	_flags = TLSDatabaseVerifyFlags(arg5)

	tlsCertificateFlags, _goerr := overrides.VerifyChain(_cancellable, _chain, _purpose, _identity, _interaction, _flags)

	var _ TLSCertificateFlags
	var _ error

	cret = C.GTlsCertificateFlags(tlsCertificateFlags)
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_TlsDatabaseClass_verify_chain_finish
func _gotk4_gio2_TlsDatabaseClass_verify_chain_finish(arg0 *C.GTlsDatabase, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.GTlsCertificateFlags) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSDatabaseOverrides](instance0)
	if overrides.VerifyChainFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSDatabaseOverrides.VerifyChainFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	tlsCertificateFlags, _goerr := overrides.VerifyChainFinish(_result)

	var _ TLSCertificateFlags
	var _ error

	cret = C.GTlsCertificateFlags(tlsCertificateFlags)
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_TlsInteractionClass_ask_password
func _gotk4_gio2_TlsInteractionClass_ask_password(arg0 *C.GTlsInteraction, arg1 *C.GTlsPassword, arg2 *C.GCancellable, _cerr **C.GError) (cret C.GTlsInteractionResult) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSInteractionOverrides](instance0)
	if overrides.AskPassword == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSInteractionOverrides.AskPassword, got none")
	}

	var _cancellable context.Context // out
	var _password *TLSPassword       // out

	if arg2 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg2))
	}
	_password = wrapTLSPassword(coreglib.Take(unsafe.Pointer(arg1)))

	tlsInteractionResult, _goerr := overrides.AskPassword(_cancellable, _password)

	var _ TLSInteractionResult
	var _ error

	cret = C.GTlsInteractionResult(tlsInteractionResult)
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_TlsInteractionClass_ask_password_finish
func _gotk4_gio2_TlsInteractionClass_ask_password_finish(arg0 *C.GTlsInteraction, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.GTlsInteractionResult) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSInteractionOverrides](instance0)
	if overrides.AskPasswordFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSInteractionOverrides.AskPasswordFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	tlsInteractionResult, _goerr := overrides.AskPasswordFinish(_result)

	var _ TLSInteractionResult
	var _ error

	cret = C.GTlsInteractionResult(tlsInteractionResult)
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_TlsInteractionClass_request_certificate
func _gotk4_gio2_TlsInteractionClass_request_certificate(arg0 *C.GTlsInteraction, arg1 *C.GTlsConnection, arg2 C.GTlsCertificateRequestFlags, arg3 *C.GCancellable, _cerr **C.GError) (cret C.GTlsInteractionResult) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSInteractionOverrides](instance0)
	if overrides.RequestCertificate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSInteractionOverrides.RequestCertificate, got none")
	}

	var _cancellable context.Context      // out
	var _connection TLSConnectioner       // out
	var _flags TLSCertificateRequestFlags // out

	if arg3 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg3))
	}
	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.TLSConnectioner is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TLSConnectioner)
			return ok
		})
		rv, ok := casted.(TLSConnectioner)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.TLSConnectioner")
		}
		_connection = rv
	}
	_flags = TLSCertificateRequestFlags(arg2)

	tlsInteractionResult, _goerr := overrides.RequestCertificate(_cancellable, _connection, _flags)

	var _ TLSInteractionResult
	var _ error

	cret = C.GTlsInteractionResult(tlsInteractionResult)
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_TlsInteractionClass_request_certificate_finish
func _gotk4_gio2_TlsInteractionClass_request_certificate_finish(arg0 *C.GTlsInteraction, arg1 *C.GAsyncResult, _cerr **C.GError) (cret C.GTlsInteractionResult) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSInteractionOverrides](instance0)
	if overrides.RequestCertificateFinish == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSInteractionOverrides.RequestCertificateFinish, got none")
	}

	var _result AsyncResulter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AsyncResulter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(AsyncResulter)
			return ok
		})
		rv, ok := casted.(AsyncResulter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AsyncResulter")
		}
		_result = rv
	}

	tlsInteractionResult, _goerr := overrides.RequestCertificateFinish(_result)

	var _ TLSInteractionResult
	var _ error

	cret = C.GTlsInteractionResult(tlsInteractionResult)
	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_TlsPasswordClass_get_default_warning
func _gotk4_gio2_TlsPasswordClass_get_default_warning(arg0 *C.GTlsPassword) (cret *C.gchar) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSPasswordOverrides](instance0)
	if overrides.DefaultWarning == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSPasswordOverrides.DefaultWarning, got none")
	}

	utf8 := overrides.DefaultWarning()

	var _ string

	cret = (*C.gchar)(unsafe.Pointer(C.CString(utf8)))
	defer C.free(unsafe.Pointer(cret))

	return cret
}

//export _gotk4_gio2_TlsPasswordClass_get_value
func _gotk4_gio2_TlsPasswordClass_get_value(arg0 *C.GTlsPassword, arg1 *C.gsize) (cret *C.guchar) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TLSPasswordOverrides](instance0)
	if overrides.Value == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TLSPasswordOverrides.Value, got none")
	}

	guint8s := overrides.Value()

	var _ []byte

	*arg1 = (C.gsize)(len(guint8s))
	if len(guint8s) > 0 {
		cret = (*C.guchar)(unsafe.Pointer(&guint8s[0]))
	}

	return cret
}

//export _gotk4_gio2_VfsClass_add_writable_namespaces
func _gotk4_gio2_VfsClass_add_writable_namespaces(arg0 *C.GVfs, arg1 *C.GFileAttributeInfoList) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VFSOverrides](instance0)
	if overrides.AddWritableNamespaces == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VFSOverrides.AddWritableNamespaces, got none")
	}

	var _list *FileAttributeInfoList // out

	_list = (*FileAttributeInfoList)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	C.g_file_attribute_info_list_ref(arg1)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_list)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_file_attribute_info_list_unref((*C.GFileAttributeInfoList)(intern.C))
		},
	)

	overrides.AddWritableNamespaces(_list)
}

//export _gotk4_gio2_VfsClass_get_file_for_path
func _gotk4_gio2_VfsClass_get_file_for_path(arg0 *C.GVfs, arg1 *C.char) (cret *C.GFile) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VFSOverrides](instance0)
	if overrides.FileForPath == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VFSOverrides.FileForPath, got none")
	}

	var _path string // out

	_path = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	file := overrides.FileForPath(_path)

	var _ *File

	cret = (*C.GFile)(unsafe.Pointer(coreglib.InternObject(file).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(file).Native()))

	return cret
}

//export _gotk4_gio2_VfsClass_get_file_for_uri
func _gotk4_gio2_VfsClass_get_file_for_uri(arg0 *C.GVfs, arg1 *C.char) (cret *C.GFile) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VFSOverrides](instance0)
	if overrides.FileForURI == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VFSOverrides.FileForURI, got none")
	}

	var _uri string // out

	_uri = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	file := overrides.FileForURI(_uri)

	var _ *File

	cret = (*C.GFile)(unsafe.Pointer(coreglib.InternObject(file).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(file).Native()))

	return cret
}

//export _gotk4_gio2_VfsClass_get_supported_uri_schemes
func _gotk4_gio2_VfsClass_get_supported_uri_schemes(arg0 *C.GVfs) (cret **C.gchar) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VFSOverrides](instance0)
	if overrides.SupportedURISchemes == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VFSOverrides.SupportedURISchemes, got none")
	}

	utf8s := overrides.SupportedURISchemes()

	var _ []string

	{
		cret = (**C.gchar)(C.calloc(C.size_t((len(utf8s) + 1)), C.size_t(unsafe.Sizeof(uint(0)))))
		defer C.free(unsafe.Pointer(cret))
		{
			out := unsafe.Slice(cret, len(utf8s)+1)
			var zero *C.gchar
			out[len(utf8s)] = zero
			for i := range utf8s {
				out[i] = (*C.gchar)(unsafe.Pointer(C.CString(utf8s[i])))
				defer C.free(unsafe.Pointer(out[i]))
			}
		}
	}

	return cret
}

//export _gotk4_gio2_VfsClass_is_active
func _gotk4_gio2_VfsClass_is_active(arg0 *C.GVfs) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VFSOverrides](instance0)
	if overrides.IsActive == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VFSOverrides.IsActive, got none")
	}

	ok := overrides.IsActive()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gio2_VfsClass_local_file_moved
func _gotk4_gio2_VfsClass_local_file_moved(arg0 *C.GVfs, arg1 *C.char, arg2 *C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VFSOverrides](instance0)
	if overrides.LocalFileMoved == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VFSOverrides.LocalFileMoved, got none")
	}

	var _source string // out
	var _dest string   // out

	_source = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	_dest = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	overrides.LocalFileMoved(_source, _dest)
}

//export _gotk4_gio2_VfsClass_local_file_removed
func _gotk4_gio2_VfsClass_local_file_removed(arg0 *C.GVfs, arg1 *C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VFSOverrides](instance0)
	if overrides.LocalFileRemoved == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VFSOverrides.LocalFileRemoved, got none")
	}

	var _filename string // out

	_filename = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	overrides.LocalFileRemoved(_filename)
}

//export _gotk4_gio2_VfsClass_local_file_set_attributes
func _gotk4_gio2_VfsClass_local_file_set_attributes(arg0 *C.GVfs, arg1 *C.char, arg2 *C.GFileInfo, arg3 C.GFileQueryInfoFlags, arg4 *C.GCancellable, _cerr **C.GError) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VFSOverrides](instance0)
	if overrides.LocalFileSetAttributes == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VFSOverrides.LocalFileSetAttributes, got none")
	}

	var _cancellable context.Context // out
	var _filename string             // out
	var _info *FileInfo              // out
	var _flags FileQueryInfoFlags    // out

	if arg4 != nil {
		_cancellable = gcancel.NewCancellableContext(unsafe.Pointer(arg4))
	}
	_filename = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	_info = wrapFileInfo(coreglib.Take(unsafe.Pointer(arg2)))
	_flags = FileQueryInfoFlags(arg3)

	_goerr := overrides.LocalFileSetAttributes(_cancellable, _filename, _info, _flags)

	var _ error

	if _goerr != nil && _cerr != nil {
		*_cerr = (*C.GError)(gerror.New(_goerr))
	}

	return cret
}

//export _gotk4_gio2_VfsClass_parse_name
func _gotk4_gio2_VfsClass_parse_name(arg0 *C.GVfs, arg1 *C.char) (cret *C.GFile) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VFSOverrides](instance0)
	if overrides.ParseName == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VFSOverrides.ParseName, got none")
	}

	var _parseName string // out

	_parseName = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	file := overrides.ParseName(_parseName)

	var _ *File

	cret = (*C.GFile)(unsafe.Pointer(coreglib.InternObject(file).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(file).Native()))

	return cret
}

//export _gotk4_gio2_VolumeMonitorClass_drive_changed
func _gotk4_gio2_VolumeMonitorClass_drive_changed(arg0 *C.GVolumeMonitor, arg1 *C.GDrive) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.DriveChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.DriveChanged, got none")
	}

	var _drive Driver // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Driver is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Driver)
			return ok
		})
		rv, ok := casted.(Driver)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Driver")
		}
		_drive = rv
	}

	overrides.DriveChanged(_drive)
}

//export _gotk4_gio2_VolumeMonitorClass_drive_connected
func _gotk4_gio2_VolumeMonitorClass_drive_connected(arg0 *C.GVolumeMonitor, arg1 *C.GDrive) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.DriveConnected == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.DriveConnected, got none")
	}

	var _drive Driver // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Driver is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Driver)
			return ok
		})
		rv, ok := casted.(Driver)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Driver")
		}
		_drive = rv
	}

	overrides.DriveConnected(_drive)
}

//export _gotk4_gio2_VolumeMonitorClass_drive_disconnected
func _gotk4_gio2_VolumeMonitorClass_drive_disconnected(arg0 *C.GVolumeMonitor, arg1 *C.GDrive) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.DriveDisconnected == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.DriveDisconnected, got none")
	}

	var _drive Driver // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Driver is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Driver)
			return ok
		})
		rv, ok := casted.(Driver)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Driver")
		}
		_drive = rv
	}

	overrides.DriveDisconnected(_drive)
}

//export _gotk4_gio2_VolumeMonitorClass_drive_eject_button
func _gotk4_gio2_VolumeMonitorClass_drive_eject_button(arg0 *C.GVolumeMonitor, arg1 *C.GDrive) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.DriveEjectButton == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.DriveEjectButton, got none")
	}

	var _drive Driver // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Driver is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Driver)
			return ok
		})
		rv, ok := casted.(Driver)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Driver")
		}
		_drive = rv
	}

	overrides.DriveEjectButton(_drive)
}

//export _gotk4_gio2_VolumeMonitorClass_drive_stop_button
func _gotk4_gio2_VolumeMonitorClass_drive_stop_button(arg0 *C.GVolumeMonitor, arg1 *C.GDrive) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.DriveStopButton == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.DriveStopButton, got none")
	}

	var _drive Driver // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Driver is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Driver)
			return ok
		})
		rv, ok := casted.(Driver)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Driver")
		}
		_drive = rv
	}

	overrides.DriveStopButton(_drive)
}

//export _gotk4_gio2_VolumeMonitorClass_get_connected_drives
func _gotk4_gio2_VolumeMonitorClass_get_connected_drives(arg0 *C.GVolumeMonitor) (cret *C.GList) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.ConnectedDrives == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.ConnectedDrives, got none")
	}

	list := overrides.ConnectedDrives()

	var _ []*Drive

	for i := len(list) - 1; i >= 0; i-- {
		src := list[i]
		var dst *C.GDrive // out
		dst = (*C.GDrive)(unsafe.Pointer(coreglib.InternObject(src).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(src).Native()))
		cret = C.g_list_prepend(cret, C.gpointer(unsafe.Pointer(dst)))
	}

	return cret
}

//export _gotk4_gio2_VolumeMonitorClass_get_mount_for_uuid
func _gotk4_gio2_VolumeMonitorClass_get_mount_for_uuid(arg0 *C.GVolumeMonitor, arg1 *C.char) (cret *C.GMount) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.MountForUUID == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.MountForUUID, got none")
	}

	var _uuid string // out

	_uuid = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	mount := overrides.MountForUUID(_uuid)

	var _ *Mount

	if mount != nil {
		cret = (*C.GMount)(unsafe.Pointer(coreglib.InternObject(mount).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(mount).Native()))
	}

	return cret
}

//export _gotk4_gio2_VolumeMonitorClass_get_mounts
func _gotk4_gio2_VolumeMonitorClass_get_mounts(arg0 *C.GVolumeMonitor) (cret *C.GList) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.Mounts == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.Mounts, got none")
	}

	list := overrides.Mounts()

	var _ []*Mount

	for i := len(list) - 1; i >= 0; i-- {
		src := list[i]
		var dst *C.GMount // out
		dst = (*C.GMount)(unsafe.Pointer(coreglib.InternObject(src).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(src).Native()))
		cret = C.g_list_prepend(cret, C.gpointer(unsafe.Pointer(dst)))
	}

	return cret
}

//export _gotk4_gio2_VolumeMonitorClass_get_volume_for_uuid
func _gotk4_gio2_VolumeMonitorClass_get_volume_for_uuid(arg0 *C.GVolumeMonitor, arg1 *C.char) (cret *C.GVolume) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.VolumeForUUID == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.VolumeForUUID, got none")
	}

	var _uuid string // out

	_uuid = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	volume := overrides.VolumeForUUID(_uuid)

	var _ *Volume

	if volume != nil {
		cret = (*C.GVolume)(unsafe.Pointer(coreglib.InternObject(volume).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(volume).Native()))
	}

	return cret
}

//export _gotk4_gio2_VolumeMonitorClass_get_volumes
func _gotk4_gio2_VolumeMonitorClass_get_volumes(arg0 *C.GVolumeMonitor) (cret *C.GList) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.Volumes == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.Volumes, got none")
	}

	list := overrides.Volumes()

	var _ []*Volume

	for i := len(list) - 1; i >= 0; i-- {
		src := list[i]
		var dst *C.GVolume // out
		dst = (*C.GVolume)(unsafe.Pointer(coreglib.InternObject(src).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(src).Native()))
		cret = C.g_list_prepend(cret, C.gpointer(unsafe.Pointer(dst)))
	}

	return cret
}

//export _gotk4_gio2_VolumeMonitorClass_mount_added
func _gotk4_gio2_VolumeMonitorClass_mount_added(arg0 *C.GVolumeMonitor, arg1 *C.GMount) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.MountAdded == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.MountAdded, got none")
	}

	var _mount Mounter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Mounter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Mounter)
			return ok
		})
		rv, ok := casted.(Mounter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Mounter")
		}
		_mount = rv
	}

	overrides.MountAdded(_mount)
}

//export _gotk4_gio2_VolumeMonitorClass_mount_changed
func _gotk4_gio2_VolumeMonitorClass_mount_changed(arg0 *C.GVolumeMonitor, arg1 *C.GMount) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.MountChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.MountChanged, got none")
	}

	var _mount Mounter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Mounter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Mounter)
			return ok
		})
		rv, ok := casted.(Mounter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Mounter")
		}
		_mount = rv
	}

	overrides.MountChanged(_mount)
}

//export _gotk4_gio2_VolumeMonitorClass_mount_pre_unmount
func _gotk4_gio2_VolumeMonitorClass_mount_pre_unmount(arg0 *C.GVolumeMonitor, arg1 *C.GMount) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.MountPreUnmount == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.MountPreUnmount, got none")
	}

	var _mount Mounter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Mounter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Mounter)
			return ok
		})
		rv, ok := casted.(Mounter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Mounter")
		}
		_mount = rv
	}

	overrides.MountPreUnmount(_mount)
}

//export _gotk4_gio2_VolumeMonitorClass_mount_removed
func _gotk4_gio2_VolumeMonitorClass_mount_removed(arg0 *C.GVolumeMonitor, arg1 *C.GMount) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.MountRemoved == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.MountRemoved, got none")
	}

	var _mount Mounter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Mounter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Mounter)
			return ok
		})
		rv, ok := casted.(Mounter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Mounter")
		}
		_mount = rv
	}

	overrides.MountRemoved(_mount)
}

//export _gotk4_gio2_VolumeMonitorClass_volume_added
func _gotk4_gio2_VolumeMonitorClass_volume_added(arg0 *C.GVolumeMonitor, arg1 *C.GVolume) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.VolumeAdded == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.VolumeAdded, got none")
	}

	var _volume Volumer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Volumer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Volumer)
			return ok
		})
		rv, ok := casted.(Volumer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Volumer")
		}
		_volume = rv
	}

	overrides.VolumeAdded(_volume)
}

//export _gotk4_gio2_VolumeMonitorClass_volume_changed
func _gotk4_gio2_VolumeMonitorClass_volume_changed(arg0 *C.GVolumeMonitor, arg1 *C.GVolume) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.VolumeChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.VolumeChanged, got none")
	}

	var _volume Volumer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Volumer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Volumer)
			return ok
		})
		rv, ok := casted.(Volumer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Volumer")
		}
		_volume = rv
	}

	overrides.VolumeChanged(_volume)
}

//export _gotk4_gio2_VolumeMonitorClass_volume_removed
func _gotk4_gio2_VolumeMonitorClass_volume_removed(arg0 *C.GVolumeMonitor, arg1 *C.GVolume) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[VolumeMonitorOverrides](instance0)
	if overrides.VolumeRemoved == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected VolumeMonitorOverrides.VolumeRemoved, got none")
	}

	var _volume Volumer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Volumer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Volumer)
			return ok
		})
		rv, ok := casted.(Volumer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Volumer")
		}
		_volume = rv
	}

	overrides.VolumeRemoved(_volume)
}

//export _gotk4_gio2_VolumeMonitor_ConnectDriveChanged
func _gotk4_gio2_VolumeMonitor_ConnectDriveChanged(arg0 C.gpointer, arg1 *C.GDrive, arg2 C.guintptr) {
	var f func(drive Driver)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(drive Driver))
	}

	var _drive Driver // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Driver is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Driver)
			return ok
		})
		rv, ok := casted.(Driver)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Driver")
		}
		_drive = rv
	}

	f(_drive)
}

//export _gotk4_gio2_VolumeMonitor_ConnectDriveConnected
func _gotk4_gio2_VolumeMonitor_ConnectDriveConnected(arg0 C.gpointer, arg1 *C.GDrive, arg2 C.guintptr) {
	var f func(drive Driver)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(drive Driver))
	}

	var _drive Driver // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Driver is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Driver)
			return ok
		})
		rv, ok := casted.(Driver)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Driver")
		}
		_drive = rv
	}

	f(_drive)
}

//export _gotk4_gio2_VolumeMonitor_ConnectDriveDisconnected
func _gotk4_gio2_VolumeMonitor_ConnectDriveDisconnected(arg0 C.gpointer, arg1 *C.GDrive, arg2 C.guintptr) {
	var f func(drive Driver)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(drive Driver))
	}

	var _drive Driver // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Driver is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Driver)
			return ok
		})
		rv, ok := casted.(Driver)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Driver")
		}
		_drive = rv
	}

	f(_drive)
}

//export _gotk4_gio2_VolumeMonitor_ConnectDriveEjectButton
func _gotk4_gio2_VolumeMonitor_ConnectDriveEjectButton(arg0 C.gpointer, arg1 *C.GDrive, arg2 C.guintptr) {
	var f func(drive Driver)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(drive Driver))
	}

	var _drive Driver // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Driver is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Driver)
			return ok
		})
		rv, ok := casted.(Driver)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Driver")
		}
		_drive = rv
	}

	f(_drive)
}

//export _gotk4_gio2_VolumeMonitor_ConnectDriveStopButton
func _gotk4_gio2_VolumeMonitor_ConnectDriveStopButton(arg0 C.gpointer, arg1 *C.GDrive, arg2 C.guintptr) {
	var f func(drive Driver)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(drive Driver))
	}

	var _drive Driver // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Driver is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Driver)
			return ok
		})
		rv, ok := casted.(Driver)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Driver")
		}
		_drive = rv
	}

	f(_drive)
}

//export _gotk4_gio2_VolumeMonitor_ConnectMountAdded
func _gotk4_gio2_VolumeMonitor_ConnectMountAdded(arg0 C.gpointer, arg1 *C.GMount, arg2 C.guintptr) {
	var f func(mount Mounter)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(mount Mounter))
	}

	var _mount Mounter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Mounter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Mounter)
			return ok
		})
		rv, ok := casted.(Mounter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Mounter")
		}
		_mount = rv
	}

	f(_mount)
}

//export _gotk4_gio2_VolumeMonitor_ConnectMountChanged
func _gotk4_gio2_VolumeMonitor_ConnectMountChanged(arg0 C.gpointer, arg1 *C.GMount, arg2 C.guintptr) {
	var f func(mount Mounter)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(mount Mounter))
	}

	var _mount Mounter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Mounter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Mounter)
			return ok
		})
		rv, ok := casted.(Mounter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Mounter")
		}
		_mount = rv
	}

	f(_mount)
}

//export _gotk4_gio2_VolumeMonitor_ConnectMountPreUnmount
func _gotk4_gio2_VolumeMonitor_ConnectMountPreUnmount(arg0 C.gpointer, arg1 *C.GMount, arg2 C.guintptr) {
	var f func(mount Mounter)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(mount Mounter))
	}

	var _mount Mounter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Mounter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Mounter)
			return ok
		})
		rv, ok := casted.(Mounter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Mounter")
		}
		_mount = rv
	}

	f(_mount)
}

//export _gotk4_gio2_VolumeMonitor_ConnectMountRemoved
func _gotk4_gio2_VolumeMonitor_ConnectMountRemoved(arg0 C.gpointer, arg1 *C.GMount, arg2 C.guintptr) {
	var f func(mount Mounter)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(mount Mounter))
	}

	var _mount Mounter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Mounter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Mounter)
			return ok
		})
		rv, ok := casted.(Mounter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Mounter")
		}
		_mount = rv
	}

	f(_mount)
}

//export _gotk4_gio2_VolumeMonitor_ConnectVolumeAdded
func _gotk4_gio2_VolumeMonitor_ConnectVolumeAdded(arg0 C.gpointer, arg1 *C.GVolume, arg2 C.guintptr) {
	var f func(volume Volumer)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(volume Volumer))
	}

	var _volume Volumer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Volumer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Volumer)
			return ok
		})
		rv, ok := casted.(Volumer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Volumer")
		}
		_volume = rv
	}

	f(_volume)
}

//export _gotk4_gio2_VolumeMonitor_ConnectVolumeChanged
func _gotk4_gio2_VolumeMonitor_ConnectVolumeChanged(arg0 C.gpointer, arg1 *C.GVolume, arg2 C.guintptr) {
	var f func(volume Volumer)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(volume Volumer))
	}

	var _volume Volumer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Volumer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Volumer)
			return ok
		})
		rv, ok := casted.(Volumer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Volumer")
		}
		_volume = rv
	}

	f(_volume)
}

//export _gotk4_gio2_VolumeMonitor_ConnectVolumeRemoved
func _gotk4_gio2_VolumeMonitor_ConnectVolumeRemoved(arg0 C.gpointer, arg1 *C.GVolume, arg2 C.guintptr) {
	var f func(volume Volumer)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(volume Volumer))
	}

	var _volume Volumer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.Volumer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Volumer)
			return ok
		})
		rv, ok := casted.(Volumer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.Volumer")
		}
		_volume = rv
	}

	f(_volume)
}
