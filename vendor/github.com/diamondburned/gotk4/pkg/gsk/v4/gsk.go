// Code generated by girgen. DO NOT EDIT.

package gsk

import (
	"fmt"
	"runtime"
	_ "runtime/cgo"
	"strings"
	"unsafe"

	"github.com/diamondburned/gotk4/pkg/cairo"
	"github.com/diamondburned/gotk4/pkg/core/gbox"
	"github.com/diamondburned/gotk4/pkg/core/gerror"
	"github.com/diamondburned/gotk4/pkg/core/gextras"
	coreglib "github.com/diamondburned/gotk4/pkg/core/glib"
	"github.com/diamondburned/gotk4/pkg/gdk/v4"
	"github.com/diamondburned/gotk4/pkg/glib/v2"
	"github.com/diamondburned/gotk4/pkg/graphene"
	"github.com/diamondburned/gotk4/pkg/pango"
)

// #cgo pkg-config: gtk4
// #cgo CFLAGS: -Wno-deprecated-declarations
// #include <stdlib.h>
// #include <glib-object.h>
// #include <gsk/gsk.h>
// extern void _gotk4_gsk4_ParseErrorFunc(GskParseLocation*, GskParseLocation*, GError*, gpointer);
// extern gboolean _gotk4_gsk4_PathForEachFunc(GskPathOperation, graphene_point_t*, gsize, float, gpointer);
import "C"

// GType values.
var (
	GTypeBlendMode                   = coreglib.Type(C.gsk_blend_mode_get_type())
	GTypeCorner                      = coreglib.Type(C.gsk_corner_get_type())
	GTypeFillRule                    = coreglib.Type(C.gsk_fill_rule_get_type())
	GTypeGLUniformType               = coreglib.Type(C.gsk_gl_uniform_type_get_type())
	GTypeLineCap                     = coreglib.Type(C.gsk_line_cap_get_type())
	GTypeLineJoin                    = coreglib.Type(C.gsk_line_join_get_type())
	GTypeMaskMode                    = coreglib.Type(C.gsk_mask_mode_get_type())
	GTypePathDirection               = coreglib.Type(C.gsk_path_direction_get_type())
	GTypePathOperation               = coreglib.Type(C.gsk_path_operation_get_type())
	GTypeRenderNodeType              = coreglib.Type(C.gsk_render_node_type_get_type())
	GTypeScalingFilter               = coreglib.Type(C.gsk_scaling_filter_get_type())
	GTypeSerializationError          = coreglib.Type(C.gsk_serialization_error_get_type())
	GTypeTransformCategory           = coreglib.Type(C.gsk_transform_category_get_type())
	GTypePathForEachFlags            = coreglib.Type(C.gsk_path_foreach_flags_get_type())
	GTypeBlendNode                   = coreglib.Type(C.gsk_blend_node_get_type())
	GTypeBlurNode                    = coreglib.Type(C.gsk_blur_node_get_type())
	GTypeBorderNode                  = coreglib.Type(C.gsk_border_node_get_type())
	GTypeCairoNode                   = coreglib.Type(C.gsk_cairo_node_get_type())
	GTypeCairoRenderer               = coreglib.Type(C.gsk_cairo_renderer_get_type())
	GTypeClipNode                    = coreglib.Type(C.gsk_clip_node_get_type())
	GTypeColorMatrixNode             = coreglib.Type(C.gsk_color_matrix_node_get_type())
	GTypeColorNode                   = coreglib.Type(C.gsk_color_node_get_type())
	GTypeConicGradientNode           = coreglib.Type(C.gsk_conic_gradient_node_get_type())
	GTypeContainerNode               = coreglib.Type(C.gsk_container_node_get_type())
	GTypeCrossFadeNode               = coreglib.Type(C.gsk_cross_fade_node_get_type())
	GTypeDebugNode                   = coreglib.Type(C.gsk_debug_node_get_type())
	GTypeFillNode                    = coreglib.Type(C.gsk_fill_node_get_type())
	GTypeGLShader                    = coreglib.Type(C.gsk_gl_shader_get_type())
	GTypeGLShaderNode                = coreglib.Type(C.gsk_gl_shader_node_get_type())
	GTypeInsetShadowNode             = coreglib.Type(C.gsk_inset_shadow_node_get_type())
	GTypeLinearGradientNode          = coreglib.Type(C.gsk_linear_gradient_node_get_type())
	GTypeMaskNode                    = coreglib.Type(C.gsk_mask_node_get_type())
	GTypeNGLRenderer                 = coreglib.Type(C.gsk_ngl_renderer_get_type())
	GTypeOpacityNode                 = coreglib.Type(C.gsk_opacity_node_get_type())
	GTypeOutsetShadowNode            = coreglib.Type(C.gsk_outset_shadow_node_get_type())
	GTypeRadialGradientNode          = coreglib.Type(C.gsk_radial_gradient_node_get_type())
	GTypeRenderNode                  = coreglib.Type(C.gsk_render_node_get_type())
	GTypeRenderer                    = coreglib.Type(C.gsk_renderer_get_type())
	GTypeRepeatNode                  = coreglib.Type(C.gsk_repeat_node_get_type())
	GTypeRepeatingLinearGradientNode = coreglib.Type(C.gsk_repeating_linear_gradient_node_get_type())
	GTypeRepeatingRadialGradientNode = coreglib.Type(C.gsk_repeating_radial_gradient_node_get_type())
	GTypeRoundedClipNode             = coreglib.Type(C.gsk_rounded_clip_node_get_type())
	GTypeShadowNode                  = coreglib.Type(C.gsk_shadow_node_get_type())
	GTypeStrokeNode                  = coreglib.Type(C.gsk_stroke_node_get_type())
	GTypeSubsurfaceNode              = coreglib.Type(C.gsk_subsurface_node_get_type())
	GTypeTextNode                    = coreglib.Type(C.gsk_text_node_get_type())
	GTypeTextureNode                 = coreglib.Type(C.gsk_texture_node_get_type())
	GTypeTextureScaleNode            = coreglib.Type(C.gsk_texture_scale_node_get_type())
	GTypeTransformNode               = coreglib.Type(C.gsk_transform_node_get_type())
	GTypePath                        = coreglib.Type(C.gsk_path_get_type())
	GTypePathBuilder                 = coreglib.Type(C.gsk_path_builder_get_type())
	GTypePathMeasure                 = coreglib.Type(C.gsk_path_measure_get_type())
	GTypePathPoint                   = coreglib.Type(C.gsk_path_point_get_type())
	GTypeShaderArgsBuilder           = coreglib.Type(C.gsk_shader_args_builder_get_type())
	GTypeStroke                      = coreglib.Type(C.gsk_stroke_get_type())
	GTypeTransform                   = coreglib.Type(C.gsk_transform_get_type())
)

func init() {
	coreglib.RegisterGValueMarshalers([]coreglib.TypeMarshaler{
		coreglib.TypeMarshaler{T: GTypeBlendMode, F: marshalBlendMode},
		coreglib.TypeMarshaler{T: GTypeCorner, F: marshalCorner},
		coreglib.TypeMarshaler{T: GTypeFillRule, F: marshalFillRule},
		coreglib.TypeMarshaler{T: GTypeGLUniformType, F: marshalGLUniformType},
		coreglib.TypeMarshaler{T: GTypeLineCap, F: marshalLineCap},
		coreglib.TypeMarshaler{T: GTypeLineJoin, F: marshalLineJoin},
		coreglib.TypeMarshaler{T: GTypeMaskMode, F: marshalMaskMode},
		coreglib.TypeMarshaler{T: GTypePathDirection, F: marshalPathDirection},
		coreglib.TypeMarshaler{T: GTypePathOperation, F: marshalPathOperation},
		coreglib.TypeMarshaler{T: GTypeRenderNodeType, F: marshalRenderNodeType},
		coreglib.TypeMarshaler{T: GTypeScalingFilter, F: marshalScalingFilter},
		coreglib.TypeMarshaler{T: GTypeSerializationError, F: marshalSerializationError},
		coreglib.TypeMarshaler{T: GTypeTransformCategory, F: marshalTransformCategory},
		coreglib.TypeMarshaler{T: GTypePathForEachFlags, F: marshalPathForEachFlags},
		coreglib.TypeMarshaler{T: GTypeBlendNode, F: marshalBlendNode},
		coreglib.TypeMarshaler{T: GTypeBlurNode, F: marshalBlurNode},
		coreglib.TypeMarshaler{T: GTypeBorderNode, F: marshalBorderNode},
		coreglib.TypeMarshaler{T: GTypeCairoNode, F: marshalCairoNode},
		coreglib.TypeMarshaler{T: GTypeCairoRenderer, F: marshalCairoRenderer},
		coreglib.TypeMarshaler{T: GTypeClipNode, F: marshalClipNode},
		coreglib.TypeMarshaler{T: GTypeColorMatrixNode, F: marshalColorMatrixNode},
		coreglib.TypeMarshaler{T: GTypeColorNode, F: marshalColorNode},
		coreglib.TypeMarshaler{T: GTypeConicGradientNode, F: marshalConicGradientNode},
		coreglib.TypeMarshaler{T: GTypeContainerNode, F: marshalContainerNode},
		coreglib.TypeMarshaler{T: GTypeCrossFadeNode, F: marshalCrossFadeNode},
		coreglib.TypeMarshaler{T: GTypeDebugNode, F: marshalDebugNode},
		coreglib.TypeMarshaler{T: GTypeFillNode, F: marshalFillNode},
		coreglib.TypeMarshaler{T: GTypeGLShader, F: marshalGLShader},
		coreglib.TypeMarshaler{T: GTypeGLShaderNode, F: marshalGLShaderNode},
		coreglib.TypeMarshaler{T: GTypeInsetShadowNode, F: marshalInsetShadowNode},
		coreglib.TypeMarshaler{T: GTypeLinearGradientNode, F: marshalLinearGradientNode},
		coreglib.TypeMarshaler{T: GTypeMaskNode, F: marshalMaskNode},
		coreglib.TypeMarshaler{T: GTypeNGLRenderer, F: marshalNGLRenderer},
		coreglib.TypeMarshaler{T: GTypeOpacityNode, F: marshalOpacityNode},
		coreglib.TypeMarshaler{T: GTypeOutsetShadowNode, F: marshalOutsetShadowNode},
		coreglib.TypeMarshaler{T: GTypeRadialGradientNode, F: marshalRadialGradientNode},
		coreglib.TypeMarshaler{T: GTypeRenderNode, F: marshalRenderNode},
		coreglib.TypeMarshaler{T: GTypeRenderer, F: marshalRenderer},
		coreglib.TypeMarshaler{T: GTypeRepeatNode, F: marshalRepeatNode},
		coreglib.TypeMarshaler{T: GTypeRepeatingLinearGradientNode, F: marshalRepeatingLinearGradientNode},
		coreglib.TypeMarshaler{T: GTypeRepeatingRadialGradientNode, F: marshalRepeatingRadialGradientNode},
		coreglib.TypeMarshaler{T: GTypeRoundedClipNode, F: marshalRoundedClipNode},
		coreglib.TypeMarshaler{T: GTypeShadowNode, F: marshalShadowNode},
		coreglib.TypeMarshaler{T: GTypeStrokeNode, F: marshalStrokeNode},
		coreglib.TypeMarshaler{T: GTypeSubsurfaceNode, F: marshalSubsurfaceNode},
		coreglib.TypeMarshaler{T: GTypeTextNode, F: marshalTextNode},
		coreglib.TypeMarshaler{T: GTypeTextureNode, F: marshalTextureNode},
		coreglib.TypeMarshaler{T: GTypeTextureScaleNode, F: marshalTextureScaleNode},
		coreglib.TypeMarshaler{T: GTypeTransformNode, F: marshalTransformNode},
		coreglib.TypeMarshaler{T: GTypePath, F: marshalPath},
		coreglib.TypeMarshaler{T: GTypePathBuilder, F: marshalPathBuilder},
		coreglib.TypeMarshaler{T: GTypePathMeasure, F: marshalPathMeasure},
		coreglib.TypeMarshaler{T: GTypePathPoint, F: marshalPathPoint},
		coreglib.TypeMarshaler{T: GTypeShaderArgsBuilder, F: marshalShaderArgsBuilder},
		coreglib.TypeMarshaler{T: GTypeStroke, F: marshalStroke},
		coreglib.TypeMarshaler{T: GTypeTransform, F: marshalTransform},
	})
}

// BlendMode: blend modes available for render nodes.
//
// The implementation of each blend mode is deferred to the rendering pipeline.
//
// See <https://www.w3.org/TR/compositing-1/#blending> for more information on
// blending and blend modes.
type BlendMode C.gint

const (
	// BlendModeDefault: default blend mode, which specifies no blending.
	BlendModeDefault BlendMode = iota
	// BlendModeMultiply: source color is multiplied by the destination and
	// replaces the destination.
	BlendModeMultiply
	// BlendModeScreen multiplies the complements of the destination and source
	// color values, then complements the result.
	BlendModeScreen
	// BlendModeOverlay multiplies or screens the colors, depending on the
	// destination color value. This is the inverse of hard-list.
	BlendModeOverlay
	// BlendModeDarken selects the darker of the destination and source colors.
	BlendModeDarken
	// BlendModeLighten selects the lighter of the destination and source
	// colors.
	BlendModeLighten
	// BlendModeColorDodge brightens the destination color to reflect the source
	// color.
	BlendModeColorDodge
	// BlendModeColorBurn darkens the destination color to reflect the source
	// color.
	BlendModeColorBurn
	// BlendModeHardLight multiplies or screens the colors, depending on the
	// source color value.
	BlendModeHardLight
	// BlendModeSoftLight darkens or lightens the colors, depending on the
	// source color value.
	BlendModeSoftLight
	// BlendModeDifference subtracts the darker of the two constituent colors
	// from the lighter color.
	BlendModeDifference
	// BlendModeExclusion produces an effect similar to that of the difference
	// mode but lower in contrast.
	BlendModeExclusion
	// BlendModeColor creates a color with the hue and saturation of the source
	// color and the luminosity of the destination color.
	BlendModeColor
	// BlendModeHue creates a color with the hue of the source color and the
	// saturation and luminosity of the destination color.
	BlendModeHue
	// BlendModeSaturation creates a color with the saturation of the source
	// color and the hue and luminosity of the destination color.
	BlendModeSaturation
	// BlendModeLuminosity creates a color with the luminosity of the source
	// color and the hue and saturation of the destination color.
	BlendModeLuminosity
)

func marshalBlendMode(p uintptr) (interface{}, error) {
	return BlendMode(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for BlendMode.
func (b BlendMode) String() string {
	switch b {
	case BlendModeDefault:
		return "Default"
	case BlendModeMultiply:
		return "Multiply"
	case BlendModeScreen:
		return "Screen"
	case BlendModeOverlay:
		return "Overlay"
	case BlendModeDarken:
		return "Darken"
	case BlendModeLighten:
		return "Lighten"
	case BlendModeColorDodge:
		return "ColorDodge"
	case BlendModeColorBurn:
		return "ColorBurn"
	case BlendModeHardLight:
		return "HardLight"
	case BlendModeSoftLight:
		return "SoftLight"
	case BlendModeDifference:
		return "Difference"
	case BlendModeExclusion:
		return "Exclusion"
	case BlendModeColor:
		return "Color"
	case BlendModeHue:
		return "Hue"
	case BlendModeSaturation:
		return "Saturation"
	case BlendModeLuminosity:
		return "Luminosity"
	default:
		return fmt.Sprintf("BlendMode(%d)", b)
	}
}

// Corner: corner indices used by GskRoundedRect.
type Corner C.gint

const (
	// CornerTopLeft: top left corner.
	CornerTopLeft Corner = iota
	// CornerTopRight: top right corner.
	CornerTopRight
	// CornerBottomRight: bottom right corner.
	CornerBottomRight
	// CornerBottomLeft: bottom left corner.
	CornerBottomLeft
)

func marshalCorner(p uintptr) (interface{}, error) {
	return Corner(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for Corner.
func (c Corner) String() string {
	switch c {
	case CornerTopLeft:
		return "TopLeft"
	case CornerTopRight:
		return "TopRight"
	case CornerBottomRight:
		return "BottomRight"
	case CornerBottomLeft:
		return "BottomLeft"
	default:
		return fmt.Sprintf("Corner(%d)", c)
	}
}

// FillRule: GskFillRule is used to select how paths are filled.
//
// Whether or not a point is included in the fill is determined by taking a
// ray from that point to infinity and looking at intersections with the path.
// The ray can be in any direction, as long as it doesn't pass through the end
// point of a segment or have a tricky intersection such as intersecting tangent
// to the path.
//
// (Note that filling is not actually implemented in this way. This is just a
// description of the rule that is applied.)
//
// New entries may be added in future versions.
type FillRule C.gint

const (
	// FillRuleWinding: if the path crosses the ray from left-to-right,
	// counts +1. If the path crosses the ray from right to left, counts -1.
	// (Left and right are determined from the perspective of looking along the
	// ray from the starting point.) If the total count is non-zero, the point
	// will be filled.
	FillRuleWinding FillRule = iota
	// FillRuleEvenOdd counts the total number of intersections, without regard
	// to the orientation of the contour. If the total number of intersections
	// is odd, the point will be filled.
	FillRuleEvenOdd
)

func marshalFillRule(p uintptr) (interface{}, error) {
	return FillRule(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for FillRule.
func (f FillRule) String() string {
	switch f {
	case FillRuleWinding:
		return "Winding"
	case FillRuleEvenOdd:
		return "EvenOdd"
	default:
		return fmt.Sprintf("FillRule(%d)", f)
	}
}

// GLUniformType: this defines the types of the uniforms that GskGLShaders
// declare.
//
// It defines both what the type is called in the GLSL shader code, and what the
// corresponding C type is on the Gtk side.
type GLUniformType C.gint

const (
	// GLUniformTypeNone: no type, used for uninitialized or unspecified values.
	GLUniformTypeNone GLUniformType = iota
	// GLUniformTypeFloat: float uniform.
	GLUniformTypeFloat
	// GLUniformTypeInt: GLSL int / gint32 uniform.
	GLUniformTypeInt
	// GLUniformTypeUint: GLSL uint / guint32 uniform.
	GLUniformTypeUint
	// GLUniformTypeBool: GLSL bool / gboolean uniform.
	GLUniformTypeBool
	// GLUniformTypeVec2: GLSL vec2 / graphene_vec2_t uniform.
	GLUniformTypeVec2
	// GLUniformTypeVec3: GLSL vec3 / graphene_vec3_t uniform.
	GLUniformTypeVec3
	// GLUniformTypeVec4: GLSL vec4 / graphene_vec4_t uniform.
	GLUniformTypeVec4
)

func marshalGLUniformType(p uintptr) (interface{}, error) {
	return GLUniformType(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for GLUniformType.
func (g GLUniformType) String() string {
	switch g {
	case GLUniformTypeNone:
		return "None"
	case GLUniformTypeFloat:
		return "Float"
	case GLUniformTypeInt:
		return "Int"
	case GLUniformTypeUint:
		return "Uint"
	case GLUniformTypeBool:
		return "Bool"
	case GLUniformTypeVec2:
		return "Vec2"
	case GLUniformTypeVec3:
		return "Vec3"
	case GLUniformTypeVec4:
		return "Vec4"
	default:
		return fmt.Sprintf("GLUniformType(%d)", g)
	}
}

// LineCap specifies how to render the start and end points of contours or
// dashes when stroking.
//
// The default line cap style is GSK_LINE_CAP_BUTT.
//
// New entries may be added in future versions.
//
// <figure> <picture> <source srcset="caps-dark.png"
// media="(prefers-color-scheme: dark)"> <img alt="Line Cap Styles"
// src="caps-light.png"> </picture> <figcaption>GSK_LINE_CAP_BUTT,
// GSK_LINE_CAP_ROUND, GSK_LINE_CAP_SQUARE</figcaption> </figure>.
type LineCap C.gint

const (
	// LineCapButt: start and stop the line exactly at the start and end point.
	LineCapButt LineCap = iota
	// LineCapRound: use a round ending, the center of the circle is the start
	// or end point.
	LineCapRound
	// LineCapSquare: use squared ending, the center of the square is the start
	// or end point.
	LineCapSquare
)

func marshalLineCap(p uintptr) (interface{}, error) {
	return LineCap(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for LineCap.
func (l LineCap) String() string {
	switch l {
	case LineCapButt:
		return "Butt"
	case LineCapRound:
		return "Round"
	case LineCapSquare:
		return "Square"
	default:
		return fmt.Sprintf("LineCap(%d)", l)
	}
}

// LineJoin specifies how to render the junction of two lines when stroking.
//
// The default line join style is GSK_LINE_JOIN_MITER.
//
// New entries may be added in future versions.
//
// <figure> <picture> <source srcset="join-dark.png"
// media="(prefers-color-scheme: dark)"> <img alt="Line Join Styles"
// src="join-light.png"> </picture> <figcaption>GSK_LINE_JOINT_MITER,
// GSK_LINE_JOINT_ROUND, GSK_LINE_JOIN_BEVEL</figcaption> </figure>.
type LineJoin C.gint

const (
	// LineJoinMiter: use a sharp angled corner.
	LineJoinMiter LineJoin = iota
	// LineJoinRound: use a round join, the center of the circle is the join
	// point.
	LineJoinRound
	// LineJoinBevel: use a cut-off join, the join is cut off at half the line
	// width from the joint point.
	LineJoinBevel
)

func marshalLineJoin(p uintptr) (interface{}, error) {
	return LineJoin(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for LineJoin.
func (l LineJoin) String() string {
	switch l {
	case LineJoinMiter:
		return "Miter"
	case LineJoinRound:
		return "Round"
	case LineJoinBevel:
		return "Bevel"
	default:
		return fmt.Sprintf("LineJoin(%d)", l)
	}
}

// MaskMode: mask modes available for mask nodes.
type MaskMode C.gint

const (
	// MaskModeAlpha: use the alpha channel of the mask.
	MaskModeAlpha MaskMode = iota
	// MaskModeInvertedAlpha: use the inverted alpha channel of the mask.
	MaskModeInvertedAlpha
	// MaskModeLuminance: use the luminance of the mask, multiplied by mask
	// alpha.
	MaskModeLuminance
	// MaskModeInvertedLuminance: use the inverted luminance of the mask,
	// multiplied by mask alpha.
	MaskModeInvertedLuminance
)

func marshalMaskMode(p uintptr) (interface{}, error) {
	return MaskMode(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for MaskMode.
func (m MaskMode) String() string {
	switch m {
	case MaskModeAlpha:
		return "Alpha"
	case MaskModeInvertedAlpha:
		return "InvertedAlpha"
	case MaskModeLuminance:
		return "Luminance"
	case MaskModeInvertedLuminance:
		return "InvertedLuminance"
	default:
		return fmt.Sprintf("MaskMode(%d)", m)
	}
}

// PathDirection values of the GskPathDirection enum are used to pick one of the
// four tangents at a given point on the path.
//
// Note that the directions for GSK_PATH_FROM_START/GSK_PATH_TO_END and
// GSK_PATH_TO_START/GSK_PATH_FROM_END will coincide for smooth points. Only
// sharp turns will exhibit four different directions.
//
// <picture> <source srcset="directions-dark.png" media="(prefers-color-scheme:
// dark)"> <img alt="Path Tangents" src="directions-light.png"> </picture>.
type PathDirection C.gint

const (
	// PathFromStart: tangent in path direction of the incoming side of the
	// path.
	PathFromStart PathDirection = iota
	// PathToStart: tangent against path direction of the incoming side of the
	// path.
	PathToStart
	// PathToEnd: tangent in path direction of the outgoing side of the path.
	PathToEnd
	// PathFromEnd: tangent against path direction of the outgoing side of the
	// path.
	PathFromEnd
)

func marshalPathDirection(p uintptr) (interface{}, error) {
	return PathDirection(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for PathDirection.
func (p PathDirection) String() string {
	switch p {
	case PathFromStart:
		return "FromStart"
	case PathToStart:
		return "ToStart"
	case PathToEnd:
		return "ToEnd"
	case PathFromEnd:
		return "FromEnd"
	default:
		return fmt.Sprintf("PathDirection(%d)", p)
	}
}

// PathOperation: path operations are used to describe the segments of a
// GskPath.
//
// More values may be added in the future.
type PathOperation C.gint

const (
	// PathMove: move-to operation, with 1 point describing the target point.
	PathMove PathOperation = iota
	// PathClose: close operation ending the current contour with a line back to
	// the starting point. Two points describe the start and end of the line.
	PathClose
	// PathLine: line-to operation, with 2 points describing the start and end
	// point of a straight line.
	PathLine
	// PathQuad: curve-to operation describing a quadratic Bézier curve with 3
	// points describing the start point, the control point and the end point of
	// the curve.
	PathQuad
	// PathCubic: curve-to operation describing a cubic Bézier curve with 4
	// points describing the start point, the two control points and the end
	// point of the curve.
	PathCubic
	// PathConic: rational quadratic Bézier curve with 3 points describing the
	// start point, control point and end point of the curve. A weight for the
	// curve will be passed, too.
	PathConic
)

func marshalPathOperation(p uintptr) (interface{}, error) {
	return PathOperation(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for PathOperation.
func (p PathOperation) String() string {
	switch p {
	case PathMove:
		return "Move"
	case PathClose:
		return "Close"
	case PathLine:
		return "Line"
	case PathQuad:
		return "Quad"
	case PathCubic:
		return "Cubic"
	case PathConic:
		return "Conic"
	default:
		return fmt.Sprintf("PathOperation(%d)", p)
	}
}

// RenderNodeType: type of a node determines what the node is rendering.
type RenderNodeType C.gint

const (
	// NotARenderNodeType: error type. No node will ever have this type.
	NotARenderNodeType RenderNodeType = iota
	// ContainerNodeType: node containing a stack of children.
	ContainerNodeType
	// CairoNodeType: node drawing a cairo_surface_t.
	CairoNodeType
	// ColorNodeType: node drawing a single color rectangle.
	ColorNodeType
	// LinearGradientNodeType: node drawing a linear gradient.
	LinearGradientNodeType
	// RepeatingLinearGradientNodeType: node drawing a repeating linear
	// gradient.
	RepeatingLinearGradientNodeType
	// RadialGradientNodeType: node drawing a radial gradient.
	RadialGradientNodeType
	// RepeatingRadialGradientNodeType: node drawing a repeating radial
	// gradient.
	RepeatingRadialGradientNodeType
	// ConicGradientNodeType: node drawing a conic gradient.
	ConicGradientNodeType
	// BorderNodeType: node stroking a border around an area.
	BorderNodeType
	// TextureNodeType: node drawing a GdkTexture.
	TextureNodeType
	// InsetShadowNodeType: node drawing an inset shadow.
	InsetShadowNodeType
	// OutsetShadowNodeType: node drawing an outset shadow.
	OutsetShadowNodeType
	// TransformNodeType: node that renders its child after applying a matrix
	// transform.
	TransformNodeType
	// OpacityNodeType: node that changes the opacity of its child.
	OpacityNodeType
	// ColorMatrixNodeType: node that applies a color matrix to every pixel.
	ColorMatrixNodeType
	// RepeatNodeType: node that repeats the child's contents.
	RepeatNodeType
	// ClipNodeType: node that clips its child to a rectangular area.
	ClipNodeType
	// RoundedClipNodeType: node that clips its child to a rounded rectangle.
	RoundedClipNodeType
	// ShadowNodeType: node that draws a shadow below its child.
	ShadowNodeType
	// BlendNodeType: node that blends two children together.
	BlendNodeType
	// CrossFadeNodeType: node that cross-fades between two children.
	CrossFadeNodeType
	// TextNodeType: node containing a glyph string.
	TextNodeType
	// BlurNodeType: node that applies a blur.
	BlurNodeType
	// DebugNodeType: debug information that does not affect the rendering.
	DebugNodeType
	// GLShaderNodeType: node that uses OpenGL fragment shaders to render.
	GLShaderNodeType
	// TextureScaleNodeType: node drawing a GdkTexture scaled and filtered.
	TextureScaleNodeType
	// MaskNodeType: node that masks one child with another.
	MaskNodeType
	// FillNodeType: node that fills a path.
	FillNodeType
	// StrokeNodeType: node that strokes a path.
	StrokeNodeType
	// SubsurfaceNodeType: node that possibly redirects part of the scene graph
	// to a subsurface.
	SubsurfaceNodeType
)

func marshalRenderNodeType(p uintptr) (interface{}, error) {
	return RenderNodeType(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for RenderNodeType.
func (r RenderNodeType) String() string {
	switch r {
	case NotARenderNodeType:
		return "NotARenderNode"
	case ContainerNodeType:
		return "ContainerNode"
	case CairoNodeType:
		return "CairoNode"
	case ColorNodeType:
		return "ColorNode"
	case LinearGradientNodeType:
		return "LinearGradientNode"
	case RepeatingLinearGradientNodeType:
		return "RepeatingLinearGradientNode"
	case RadialGradientNodeType:
		return "RadialGradientNode"
	case RepeatingRadialGradientNodeType:
		return "RepeatingRadialGradientNode"
	case ConicGradientNodeType:
		return "ConicGradientNode"
	case BorderNodeType:
		return "BorderNode"
	case TextureNodeType:
		return "TextureNode"
	case InsetShadowNodeType:
		return "InsetShadowNode"
	case OutsetShadowNodeType:
		return "OutsetShadowNode"
	case TransformNodeType:
		return "TransformNode"
	case OpacityNodeType:
		return "OpacityNode"
	case ColorMatrixNodeType:
		return "ColorMatrixNode"
	case RepeatNodeType:
		return "RepeatNode"
	case ClipNodeType:
		return "ClipNode"
	case RoundedClipNodeType:
		return "RoundedClipNode"
	case ShadowNodeType:
		return "ShadowNode"
	case BlendNodeType:
		return "BlendNode"
	case CrossFadeNodeType:
		return "CrossFadeNode"
	case TextNodeType:
		return "TextNode"
	case BlurNodeType:
		return "BlurNode"
	case DebugNodeType:
		return "DebugNode"
	case GLShaderNodeType:
		return "GLShaderNode"
	case TextureScaleNodeType:
		return "TextureScaleNode"
	case MaskNodeType:
		return "MaskNode"
	case FillNodeType:
		return "FillNode"
	case StrokeNodeType:
		return "StrokeNode"
	case SubsurfaceNodeType:
		return "SubsurfaceNode"
	default:
		return fmt.Sprintf("RenderNodeType(%d)", r)
	}
}

// ScalingFilter filters used when scaling texture data.
//
// The actual implementation of each filter is deferred to the rendering
// pipeline.
type ScalingFilter C.gint

const (
	// ScalingFilterLinear: linear interpolation filter.
	ScalingFilterLinear ScalingFilter = iota
	// ScalingFilterNearest: nearest neighbor interpolation filter.
	ScalingFilterNearest
	// ScalingFilterTrilinear: linear interpolation along each axis, plus mipmap
	// generation, with linear interpolation along the mipmap levels.
	ScalingFilterTrilinear
)

func marshalScalingFilter(p uintptr) (interface{}, error) {
	return ScalingFilter(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for ScalingFilter.
func (s ScalingFilter) String() string {
	switch s {
	case ScalingFilterLinear:
		return "Linear"
	case ScalingFilterNearest:
		return "Nearest"
	case ScalingFilterTrilinear:
		return "Trilinear"
	default:
		return fmt.Sprintf("ScalingFilter(%d)", s)
	}
}

// SerializationError errors that can happen during (de)serialization.
type SerializationError C.gint

const (
	// SerializationUnsupportedFormat: format can not be identified.
	SerializationUnsupportedFormat SerializationError = iota
	// SerializationUnsupportedVersion: version of the data is not understood.
	SerializationUnsupportedVersion
	// SerializationInvalidData: given data may not exist in a proper
	// serialization.
	SerializationInvalidData
)

func marshalSerializationError(p uintptr) (interface{}, error) {
	return SerializationError(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for SerializationError.
func (s SerializationError) String() string {
	switch s {
	case SerializationUnsupportedFormat:
		return "UnsupportedFormat"
	case SerializationUnsupportedVersion:
		return "UnsupportedVersion"
	case SerializationInvalidData:
		return "InvalidData"
	default:
		return fmt.Sprintf("SerializationError(%d)", s)
	}
}

func SerializationErrorQuark() glib.Quark {
	var _cret C.GQuark // in

	_cret = C.gsk_serialization_error_quark()

	var _quark glib.Quark // out

	_quark = glib.Quark(_cret)

	return _quark
}

// TransformCategory categories of matrices relevant for GSK and GTK.
//
// Note that any category includes matrices of all later categories.
// So if you want to for example check if a matrix is a 2D matrix, category >=
// GSK_TRANSFORM_CATEGORY_2D is the way to do this.
//
// Also keep in mind that rounding errors may cause matrices to not conform
// to their categories. Otherwise, matrix operations done via multiplication
// will not worsen categories. So for the matrix multiplication C = A * B,
// category(C) = MIN (category(A), category(B)).
type TransformCategory C.gint

const (
	// TransformCategoryUnknown: category of the matrix has not been determined.
	TransformCategoryUnknown TransformCategory = iota
	// TransformCategoryAny: analyzing the matrix concluded that it does not fit
	// in any other category.
	TransformCategoryAny
	// TransformCategory3D: matrix is a 3D matrix. This means that the w column
	// (the last column) has the values (0, 0, 0, 1).
	TransformCategory3D
	// TransformCategory2D: matrix is a 2D matrix. This is equivalent to
	// graphene_matrix_is_2d() returning TRUE. In particular, this means that
	// Cairo can deal with the matrix.
	TransformCategory2D
	// TransformCategory2DAffine: matrix is a combination of 2D scale and 2D
	// translation operations. In particular, this means that any rectangle can
	// be transformed exactly using this matrix.
	TransformCategory2DAffine
	// TransformCategory2DTranslate: matrix is a 2D translation.
	TransformCategory2DTranslate
	// TransformCategoryIdentity: matrix is the identity matrix.
	TransformCategoryIdentity
)

func marshalTransformCategory(p uintptr) (interface{}, error) {
	return TransformCategory(coreglib.ValueFromNative(unsafe.Pointer(p)).Enum()), nil
}

// String returns the name in string for TransformCategory.
func (t TransformCategory) String() string {
	switch t {
	case TransformCategoryUnknown:
		return "Unknown"
	case TransformCategoryAny:
		return "Any"
	case TransformCategory3D:
		return "3D"
	case TransformCategory2D:
		return "2D"
	case TransformCategory2DAffine:
		return "2DAffine"
	case TransformCategory2DTranslate:
		return "2DTranslate"
	case TransformCategoryIdentity:
		return "Identity"
	default:
		return fmt.Sprintf("TransformCategory(%d)", t)
	}
}

// PathForEachFlags flags that can be passed to gsk_path_foreach() to influence
// what kinds of operations the path is decomposed into.
//
// By default, gsk.Path.ForEach() will only emit a path with all operations
// flattened to straight lines to allow for maximum compatibility. The only
// operations emitted will be GSK_PATH_MOVE, GSK_PATH_LINE and GSK_PATH_CLOSE.
type PathForEachFlags C.guint

const (
	// PathForEachAllowOnlyLines: default behavior, only allow lines.
	PathForEachAllowOnlyLines PathForEachFlags = 0b0
	// PathForEachAllowQuad: allow emission of GSK_PATH_QUAD operations.
	PathForEachAllowQuad PathForEachFlags = 0b1
	// PathForEachAllowCubic: allow emission of GSK_PATH_CUBIC operations.
	PathForEachAllowCubic PathForEachFlags = 0b10
	// PathForEachAllowConic: allow emission of GSK_PATH_CONIC operations.
	PathForEachAllowConic PathForEachFlags = 0b100
)

func marshalPathForEachFlags(p uintptr) (interface{}, error) {
	return PathForEachFlags(coreglib.ValueFromNative(unsafe.Pointer(p)).Flags()), nil
}

// String returns the names in string for PathForEachFlags.
func (p PathForEachFlags) String() string {
	if p == 0 {
		return "PathForEachFlags(0)"
	}

	var builder strings.Builder
	builder.Grow(90)

	for p != 0 {
		next := p & (p - 1)
		bit := p - next

		switch bit {
		case PathForEachAllowOnlyLines:
			builder.WriteString("OnlyLines|")
		case PathForEachAllowQuad:
			builder.WriteString("Quad|")
		case PathForEachAllowCubic:
			builder.WriteString("Cubic|")
		case PathForEachAllowConic:
			builder.WriteString("Conic|")
		default:
			builder.WriteString(fmt.Sprintf("PathForEachFlags(0b%b)|", bit))
		}

		p = next
	}

	return strings.TrimSuffix(builder.String(), "|")
}

// Has returns true if p contains other.
func (p PathForEachFlags) Has(other PathForEachFlags) bool {
	return (p & other) == other
}

// ParseErrorFunc: type of callback that is called when an error occurs during
// node deserialization.
type ParseErrorFunc func(start, end *ParseLocation, err error)

// PathForEachFunc: prototype of the callback to iterate through the operations
// of a path.
//
// For each operation, the callback is given the op itself, the points that
// the operation is applied to in pts, and a weight for conic curves. The n_pts
// argument is somewhat redundant, since the number of points can be inferred
// from the operation.
//
// Each contour of the path starts with a GSK_PATH_MOVE operation. Closed
// contours end with a GSK_PATH_CLOSE operation.
type PathForEachFunc func(op PathOperation, pts *graphene.Point, nPts uint, weight float32) (ok bool)

// ValueDupRenderNode retrieves the GskRenderNode stored inside the given value,
// and acquires a reference to it.
//
// The function takes the following parameters:
//
//   - value: gobject.Value initialized with type GSK_TYPE_RENDER_NODE.
//
// The function returns the following values:
//
//   - renderNode (optional): GskRenderNode.
func ValueDupRenderNode(value *coreglib.Value) RenderNoder {
	var _arg1 *C.GValue        // out
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.GValue)(unsafe.Pointer(value.Native()))

	_cret = C.gsk_value_dup_render_node(_arg1)
	runtime.KeepAlive(value)

	var _renderNode RenderNoder // out

	if _cret != nil {
		{
			objptr := unsafe.Pointer(_cret)

			object := coreglib.AssumeOwnership(objptr)
			casted := object.WalkCast(func(obj coreglib.Objector) bool {
				_, ok := obj.(RenderNoder)
				return ok
			})
			rv, ok := casted.(RenderNoder)
			if !ok {
				panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
			}
			_renderNode = rv
		}
	}

	return _renderNode
}

// ValueGetRenderNode retrieves the GskRenderNode stored inside the given value.
//
// The function takes the following parameters:
//
//   - value: GValue initialized with type GSK_TYPE_RENDER_NODE.
//
// The function returns the following values:
//
//   - renderNode (optional): GskRenderNode.
func ValueGetRenderNode(value *coreglib.Value) RenderNoder {
	var _arg1 *C.GValue        // out
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.GValue)(unsafe.Pointer(value.Native()))

	_cret = C.gsk_value_get_render_node(_arg1)
	runtime.KeepAlive(value)

	var _renderNode RenderNoder // out

	if _cret != nil {
		{
			objptr := unsafe.Pointer(_cret)

			object := coreglib.Take(objptr)
			casted := object.WalkCast(func(obj coreglib.Objector) bool {
				_, ok := obj.(RenderNoder)
				return ok
			})
			rv, ok := casted.(RenderNoder)
			if !ok {
				panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
			}
			_renderNode = rv
		}
	}

	return _renderNode
}

// ValueSetRenderNode stores the given GskRenderNode inside value.
//
// The gobject.Value will acquire a reference to the node.
//
// The function takes the following parameters:
//
//   - value: gobject.Value initialized with type GSK_TYPE_RENDER_NODE.
//   - node: GskRenderNode.
func ValueSetRenderNode(value *coreglib.Value, node RenderNoder) {
	var _arg1 *C.GValue        // out
	var _arg2 *C.GskRenderNode // out

	_arg1 = (*C.GValue)(unsafe.Pointer(value.Native()))
	_arg2 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	C.gsk_value_set_render_node(_arg1, _arg2)
	runtime.KeepAlive(value)
	runtime.KeepAlive(node)
}

// ValueTakeRenderNode stores the given GskRenderNode inside value.
//
// This function transfers the ownership of the node to the GValue.
//
// The function takes the following parameters:
//
//   - value: gobject.Value initialized with type GSK_TYPE_RENDER_NODE.
//   - node (optional): GskRenderNode.
func ValueTakeRenderNode(value *coreglib.Value, node RenderNoder) {
	var _arg1 *C.GValue        // out
	var _arg2 *C.GskRenderNode // out

	_arg1 = (*C.GValue)(unsafe.Pointer(value.Native()))
	if node != nil {
		_arg2 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(node).Native()))
	}

	C.gsk_value_take_render_node(_arg1, _arg2)
	runtime.KeepAlive(value)
	runtime.KeepAlive(node)
}

// BlendNode: render node applying a blending function between its two child
// nodes.
type BlendNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*BlendNode)(nil)
)

func wrapBlendNode(obj *coreglib.Object) *BlendNode {
	return &BlendNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalBlendNode(p uintptr) (interface{}, error) {
	return wrapBlendNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewBlendNode creates a GskRenderNode that will use blend_mode to blend the
// top node onto the bottom node.
//
// The function takes the following parameters:
//
//   - bottom node to be drawn.
//   - top: node to be blended onto the bottom node.
//   - blendMode: blend mode to use.
//
// The function returns the following values:
//
//   - blendNode: new GskRenderNode.
func NewBlendNode(bottom, top RenderNoder, blendMode BlendMode) *BlendNode {
	var _arg1 *C.GskRenderNode // out
	var _arg2 *C.GskRenderNode // out
	var _arg3 C.GskBlendMode   // out
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(bottom).Native()))
	_arg2 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(top).Native()))
	_arg3 = C.GskBlendMode(blendMode)

	_cret = C.gsk_blend_node_new(_arg1, _arg2, _arg3)
	runtime.KeepAlive(bottom)
	runtime.KeepAlive(top)
	runtime.KeepAlive(blendMode)

	var _blendNode *BlendNode // out

	_blendNode = wrapBlendNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _blendNode
}

// BlendMode retrieves the blend mode used by node.
//
// The function returns the following values:
//
//   - blendMode: blend mode.
func (node *BlendNode) BlendMode() BlendMode {
	var _arg0 *C.GskRenderNode // out
	var _cret C.GskBlendMode   // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_blend_node_get_blend_mode(_arg0)
	runtime.KeepAlive(node)

	var _blendMode BlendMode // out

	_blendMode = BlendMode(_cret)

	return _blendMode
}

// BottomChild retrieves the bottom GskRenderNode child of the node.
//
// The function returns the following values:
//
//   - renderNode: bottom child node.
func (node *BlendNode) BottomChild() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_blend_node_get_bottom_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// TopChild retrieves the top GskRenderNode child of the node.
//
// The function returns the following values:
//
//   - renderNode: top child node.
func (node *BlendNode) TopChild() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_blend_node_get_top_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// BlurNode: render node applying a blur effect to its single child.
type BlurNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*BlurNode)(nil)
)

func wrapBlurNode(obj *coreglib.Object) *BlurNode {
	return &BlurNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalBlurNode(p uintptr) (interface{}, error) {
	return wrapBlurNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewBlurNode creates a render node that blurs the child.
//
// The function takes the following parameters:
//
//   - child node to blur.
//   - radius: blur radius. Must be positive.
//
// The function returns the following values:
//
//   - blurNode: new GskRenderNode.
func NewBlurNode(child RenderNoder, radius float32) *BlurNode {
	var _arg1 *C.GskRenderNode // out
	var _arg2 C.float          // out
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(child).Native()))
	_arg2 = C.float(radius)

	_cret = C.gsk_blur_node_new(_arg1, _arg2)
	runtime.KeepAlive(child)
	runtime.KeepAlive(radius)

	var _blurNode *BlurNode // out

	_blurNode = wrapBlurNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _blurNode
}

// Child retrieves the child GskRenderNode of the blur node.
//
// The function returns the following values:
//
//   - renderNode: blurred child node.
func (node *BlurNode) Child() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_blur_node_get_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// Radius retrieves the blur radius of the node.
//
// The function returns the following values:
//
//   - gfloat: blur radius.
func (node *BlurNode) Radius() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_blur_node_get_radius(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// BorderNode: render node for a border.
type BorderNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*BorderNode)(nil)
)

func wrapBorderNode(obj *coreglib.Object) *BorderNode {
	return &BorderNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalBorderNode(p uintptr) (interface{}, error) {
	return wrapBorderNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewBorderNode creates a GskRenderNode that will stroke a border rectangle
// inside the given outline.
//
// The 4 sides of the border can have different widths and colors.
//
// The function takes the following parameters:
//
//   - outline: GskRoundedRect describing the outline of the border.
//   - borderWidth: stroke width of the border on the top, right, bottom and
//     left side respectively.
//   - borderColor: color used on the top, right, bottom and left side.
//
// The function returns the following values:
//
//   - borderNode: new GskRenderNode.
func NewBorderNode(outline *RoundedRect, borderWidth [4]float32, borderColor [4]gdk.RGBA) *BorderNode {
	var _arg1 *C.GskRoundedRect // out
	var _arg2 *C.float          // out
	var _arg3 *C.GdkRGBA        // out
	var _cret *C.GskRenderNode  // in

	_arg1 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(outline)))
	_arg2 = (*C.float)(unsafe.Pointer(&borderWidth))
	{
		var out [4]C.GdkRGBA
		_arg3 = &out[0]
		for i := 0; i < 4; i++ {
			out[i] = *(*C.GdkRGBA)(gextras.StructNative(unsafe.Pointer((&borderColor[i]))))
		}
	}

	_cret = C.gsk_border_node_new(_arg1, _arg2, _arg3)
	runtime.KeepAlive(outline)
	runtime.KeepAlive(borderWidth)
	runtime.KeepAlive(borderColor)

	var _borderNode *BorderNode // out

	_borderNode = wrapBorderNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _borderNode
}

// Colors retrieves the colors of the border.
//
// The function returns the following values:
//
//   - rgbA: array of 4 GdkRGBA structs for the top, right, bottom and left
//     color of the border.
func (node *BorderNode) Colors() *gdk.RGBA {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GdkRGBA       // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_border_node_get_colors(_arg0)
	runtime.KeepAlive(node)

	var _rgbA *gdk.RGBA // out

	_rgbA = (*gdk.RGBA)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _rgbA
}

// Outline retrieves the outline of the border.
//
// The function returns the following values:
//
//   - roundedRect: outline of the border.
func (node *BorderNode) Outline() *RoundedRect {
	var _arg0 *C.GskRenderNode  // out
	var _cret *C.GskRoundedRect // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_border_node_get_outline(_arg0)
	runtime.KeepAlive(node)

	var _roundedRect *RoundedRect // out

	_roundedRect = (*RoundedRect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _roundedRect
}

// Widths retrieves the stroke widths of the border.
//
// The function returns the following values:
//
//   - gfloats: array of 4 floats for the top, right, bottom and left stroke
//     width of the border, respectively.
func (node *BorderNode) Widths() [4]float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.float         // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_border_node_get_widths(_arg0)
	runtime.KeepAlive(node)

	var _gfloats [4]float32 // out

	_gfloats = *(*[4]float32)(unsafe.Pointer(&_cret))

	return _gfloats
}

// CairoNode: render node for a Cairo surface.
type CairoNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*CairoNode)(nil)
)

func wrapCairoNode(obj *coreglib.Object) *CairoNode {
	return &CairoNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalCairoNode(p uintptr) (interface{}, error) {
	return wrapCairoNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewCairoNode creates a GskRenderNode that will render a cairo surface into
// the area given by bounds.
//
// You can draw to the cairo surface using gsk.CairoNode.GetDrawContext().
//
// The function takes the following parameters:
//
//   - bounds: rectangle to render to.
//
// The function returns the following values:
//
//   - cairoNode: new GskRenderNode.
func NewCairoNode(bounds *graphene.Rect) *CairoNode {
	var _arg1 *C.graphene_rect_t // out
	var _cret *C.GskRenderNode   // in

	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(bounds)))

	_cret = C.gsk_cairo_node_new(_arg1)
	runtime.KeepAlive(bounds)

	var _cairoNode *CairoNode // out

	_cairoNode = wrapCairoNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _cairoNode
}

// DrawContext creates a Cairo context for drawing using the surface associated
// to the render node.
//
// If no surface exists yet, a surface will be created optimized for rendering
// to renderer.
//
// The function returns the following values:
//
//   - context: cairo context used for drawing; use cairo_destroy() when done
//     drawing.
func (node *CairoNode) DrawContext() *cairo.Context {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.cairo_t       // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_cairo_node_get_draw_context(_arg0)
	runtime.KeepAlive(node)

	var _context *cairo.Context // out

	_context = cairo.WrapContext(uintptr(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(_context, func(v *cairo.Context) {
		C.cairo_destroy((*C.cairo_t)(unsafe.Pointer(v.Native())))
	})

	return _context
}

// Surface retrieves the Cairo surface used by the render node.
//
// The function returns the following values:
//
//   - surface: cairo surface.
func (node *CairoNode) Surface() *cairo.Surface {
	var _arg0 *C.GskRenderNode   // out
	var _cret *C.cairo_surface_t // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_cairo_node_get_surface(_arg0)
	runtime.KeepAlive(node)

	var _surface *cairo.Surface // out

	_surface = cairo.WrapSurface(uintptr(unsafe.Pointer(_cret)))
	C.cairo_surface_reference(_cret)
	runtime.SetFinalizer(_surface, func(v *cairo.Surface) {
		C.cairo_surface_destroy((*C.cairo_surface_t)(unsafe.Pointer(v.Native())))
	})

	return _surface
}

// CairoRenderer: GSK renderer that is using cairo.
//
// Since it is using cairo, this renderer cannot support 3D transformations.
type CairoRenderer struct {
	_ [0]func() // equal guard
	Renderer
}

var (
	_ Rendererer = (*CairoRenderer)(nil)
)

func wrapCairoRenderer(obj *coreglib.Object) *CairoRenderer {
	return &CairoRenderer{
		Renderer: Renderer{
			Object: obj,
		},
	}
}

func marshalCairoRenderer(p uintptr) (interface{}, error) {
	return wrapCairoRenderer(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewCairoRenderer creates a new Cairo renderer.
//
// The Cairo renderer is the fallback renderer drawing in ways similar to how
// GTK 3 drew its content. Its primary use is as comparison tool.
//
// The Cairo renderer is incomplete. It cannot render 3D transformed content and
// will instead render an error marker. Its usage should be avoided.
//
// The function returns the following values:
//
//   - cairoRenderer: new Cairo renderer.
func NewCairoRenderer() *CairoRenderer {
	var _cret *C.GskRenderer // in

	_cret = C.gsk_cairo_renderer_new()

	var _cairoRenderer *CairoRenderer // out

	_cairoRenderer = wrapCairoRenderer(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _cairoRenderer
}

// ClipNode: render node applying a rectangular clip to its single child node.
type ClipNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*ClipNode)(nil)
)

func wrapClipNode(obj *coreglib.Object) *ClipNode {
	return &ClipNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalClipNode(p uintptr) (interface{}, error) {
	return wrapClipNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewClipNode creates a GskRenderNode that will clip the child to the area
// given by clip.
//
// The function takes the following parameters:
//
//   - child: node to draw.
//   - clip to apply.
//
// The function returns the following values:
//
//   - clipNode: new GskRenderNode.
func NewClipNode(child RenderNoder, clip *graphene.Rect) *ClipNode {
	var _arg1 *C.GskRenderNode   // out
	var _arg2 *C.graphene_rect_t // out
	var _cret *C.GskRenderNode   // in

	_arg1 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(child).Native()))
	_arg2 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(clip)))

	_cret = C.gsk_clip_node_new(_arg1, _arg2)
	runtime.KeepAlive(child)
	runtime.KeepAlive(clip)

	var _clipNode *ClipNode // out

	_clipNode = wrapClipNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _clipNode
}

// Child gets the child node that is getting clipped by the given node.
//
// The function returns the following values:
//
//   - renderNode: child that is getting clipped.
func (node *ClipNode) Child() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_clip_node_get_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// Clip retrieves the clip rectangle for node.
//
// The function returns the following values:
//
//   - rect: clip rectangle.
func (node *ClipNode) Clip() *graphene.Rect {
	var _arg0 *C.GskRenderNode   // out
	var _cret *C.graphene_rect_t // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_clip_node_get_clip(_arg0)
	runtime.KeepAlive(node)

	var _rect *graphene.Rect // out

	_rect = (*graphene.Rect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _rect
}

// ColorMatrixNode: render node controlling the color matrix of its single child
// node.
type ColorMatrixNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*ColorMatrixNode)(nil)
)

func wrapColorMatrixNode(obj *coreglib.Object) *ColorMatrixNode {
	return &ColorMatrixNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalColorMatrixNode(p uintptr) (interface{}, error) {
	return wrapColorMatrixNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewColorMatrixNode creates a GskRenderNode that will drawn the child with
// color_matrix.
//
// In particular, the node will transform colors by applying
//
//	pixel = transpose(color_matrix) * pixel + color_offset
//
// for every pixel. The transformation operates on unpremultiplied colors,
// with color components ordered R, G, B, A.
//
// The function takes the following parameters:
//
//   - child: node to draw.
//   - colorMatrix: matrix to apply.
//   - colorOffset values to add to the color.
//
// The function returns the following values:
//
//   - colorMatrixNode: new GskRenderNode.
func NewColorMatrixNode(child RenderNoder, colorMatrix *graphene.Matrix, colorOffset *graphene.Vec4) *ColorMatrixNode {
	var _arg1 *C.GskRenderNode     // out
	var _arg2 *C.graphene_matrix_t // out
	var _arg3 *C.graphene_vec4_t   // out
	var _cret *C.GskRenderNode     // in

	_arg1 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(child).Native()))
	_arg2 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(colorMatrix)))
	_arg3 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(colorOffset)))

	_cret = C.gsk_color_matrix_node_new(_arg1, _arg2, _arg3)
	runtime.KeepAlive(child)
	runtime.KeepAlive(colorMatrix)
	runtime.KeepAlive(colorOffset)

	var _colorMatrixNode *ColorMatrixNode // out

	_colorMatrixNode = wrapColorMatrixNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _colorMatrixNode
}

// Child gets the child node that is getting its colors modified by the given
// node.
//
// The function returns the following values:
//
//   - renderNode: child that is getting its colors modified.
func (node *ColorMatrixNode) Child() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_color_matrix_node_get_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// ColorMatrix retrieves the color matrix used by the node.
//
// The function returns the following values:
//
//   - matrix: 4x4 color matrix.
func (node *ColorMatrixNode) ColorMatrix() *graphene.Matrix {
	var _arg0 *C.GskRenderNode     // out
	var _cret *C.graphene_matrix_t // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_color_matrix_node_get_color_matrix(_arg0)
	runtime.KeepAlive(node)

	var _matrix *graphene.Matrix // out

	_matrix = (*graphene.Matrix)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _matrix
}

// ColorOffset retrieves the color offset used by the node.
//
// The function returns the following values:
//
//   - vec4: color vector.
func (node *ColorMatrixNode) ColorOffset() *graphene.Vec4 {
	var _arg0 *C.GskRenderNode   // out
	var _cret *C.graphene_vec4_t // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_color_matrix_node_get_color_offset(_arg0)
	runtime.KeepAlive(node)

	var _vec4 *graphene.Vec4 // out

	_vec4 = (*graphene.Vec4)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec4
}

// ColorNode: render node for a solid color.
type ColorNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*ColorNode)(nil)
)

func wrapColorNode(obj *coreglib.Object) *ColorNode {
	return &ColorNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalColorNode(p uintptr) (interface{}, error) {
	return wrapColorNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewColorNode creates a GskRenderNode that will render the color specified by
// rgba into the area given by bounds.
//
// The function takes the following parameters:
//
//   - rgba: GdkRGBA specifying a color.
//   - bounds: rectangle to render the color into.
//
// The function returns the following values:
//
//   - colorNode: new GskRenderNode.
func NewColorNode(rgba *gdk.RGBA, bounds *graphene.Rect) *ColorNode {
	var _arg1 *C.GdkRGBA         // out
	var _arg2 *C.graphene_rect_t // out
	var _cret *C.GskRenderNode   // in

	_arg1 = (*C.GdkRGBA)(gextras.StructNative(unsafe.Pointer(rgba)))
	_arg2 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(bounds)))

	_cret = C.gsk_color_node_new(_arg1, _arg2)
	runtime.KeepAlive(rgba)
	runtime.KeepAlive(bounds)

	var _colorNode *ColorNode // out

	_colorNode = wrapColorNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _colorNode
}

// Color retrieves the color of the given node.
//
// The function returns the following values:
//
//   - rgbA: color of the node.
func (node *ColorNode) Color() *gdk.RGBA {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GdkRGBA       // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_color_node_get_color(_arg0)
	runtime.KeepAlive(node)

	var _rgbA *gdk.RGBA // out

	_rgbA = (*gdk.RGBA)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _rgbA
}

// ConicGradientNode: render node for a conic gradient.
type ConicGradientNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*ConicGradientNode)(nil)
)

func wrapConicGradientNode(obj *coreglib.Object) *ConicGradientNode {
	return &ConicGradientNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalConicGradientNode(p uintptr) (interface{}, error) {
	return wrapConicGradientNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewConicGradientNode creates a GskRenderNode that draws a conic gradient.
//
// The conic gradient starts around center in the direction of rotation.
// A rotation of 0 means that the gradient points up. Color stops are then added
// clockwise.
//
// The function takes the following parameters:
//
//   - bounds of the node.
//   - center of the gradient.
//   - rotation of the gradient in degrees.
//   - colorStops: pointer to an array of GskColorStop defining the gradient.
//     The offsets of all color stops must be increasing. The first stop's
//     offset must be >= 0 and the last stop's offset must be <= 1.
//
// The function returns the following values:
//
//   - conicGradientNode: new GskRenderNode.
func NewConicGradientNode(bounds *graphene.Rect, center *graphene.Point, rotation float32, colorStops []ColorStop) *ConicGradientNode {
	var _arg1 *C.graphene_rect_t  // out
	var _arg2 *C.graphene_point_t // out
	var _arg3 C.float             // out
	var _arg4 *C.GskColorStop     // out
	var _arg5 C.gsize
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(bounds)))
	_arg2 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(center)))
	_arg3 = C.float(rotation)
	_arg5 = (C.gsize)(len(colorStops))
	_arg4 = (*C.GskColorStop)(C.calloc(C.size_t(len(colorStops)), C.size_t(C.sizeof_GskColorStop)))
	defer C.free(unsafe.Pointer(_arg4))
	{
		out := unsafe.Slice((*C.GskColorStop)(_arg4), len(colorStops))
		for i := range colorStops {
			out[i] = *(*C.GskColorStop)(gextras.StructNative(unsafe.Pointer((&colorStops[i]))))
		}
	}

	_cret = C.gsk_conic_gradient_node_new(_arg1, _arg2, _arg3, _arg4, _arg5)
	runtime.KeepAlive(bounds)
	runtime.KeepAlive(center)
	runtime.KeepAlive(rotation)
	runtime.KeepAlive(colorStops)

	var _conicGradientNode *ConicGradientNode // out

	_conicGradientNode = wrapConicGradientNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _conicGradientNode
}

// Angle retrieves the angle for the gradient in radians, normalized in [0,
// 2 * PI].
//
// The angle is starting at the top and going clockwise, as expressed in the css
// specification:
//
//	angle = 90 - gsk_conic_gradient_node_get_rotation().
//
// The function returns the following values:
//
//   - gfloat: angle for the gradient.
func (node *ConicGradientNode) Angle() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_conic_gradient_node_get_angle(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Center retrieves the center pointer for the gradient.
//
// The function returns the following values:
//
//   - point: center point for the gradient.
func (node *ConicGradientNode) Center() *graphene.Point {
	var _arg0 *C.GskRenderNode    // out
	var _cret *C.graphene_point_t // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_conic_gradient_node_get_center(_arg0)
	runtime.KeepAlive(node)

	var _point *graphene.Point // out

	_point = (*graphene.Point)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _point
}

// ColorStops retrieves the color stops in the gradient.
//
// The function returns the following values:
//
//   - colorStops: color stops in the gradient.
func (node *ConicGradientNode) ColorStops() []ColorStop {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskColorStop  // in
	var _arg1 C.gsize          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_conic_gradient_node_get_color_stops(_arg0, &_arg1)
	runtime.KeepAlive(node)

	var _colorStops []ColorStop // out

	{
		src := unsafe.Slice((*C.GskColorStop)(_cret), _arg1)
		_colorStops = make([]ColorStop, _arg1)
		for i := 0; i < int(_arg1); i++ {
			_colorStops[i] = *(*ColorStop)(gextras.NewStructNative(unsafe.Pointer((&src[i]))))
		}
	}

	return _colorStops
}

// NColorStops retrieves the number of color stops in the gradient.
//
// The function returns the following values:
//
//   - gsize: number of color stops.
func (node *ConicGradientNode) NColorStops() uint {
	var _arg0 *C.GskRenderNode // out
	var _cret C.gsize          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_conic_gradient_node_get_n_color_stops(_arg0)
	runtime.KeepAlive(node)

	var _gsize uint // out

	_gsize = uint(_cret)

	return _gsize
}

// Rotation retrieves the rotation for the gradient in degrees.
//
// The function returns the following values:
//
//   - gfloat: rotation for the gradient.
func (node *ConicGradientNode) Rotation() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_conic_gradient_node_get_rotation(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// ContainerNode: render node that can contain other render nodes.
type ContainerNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*ContainerNode)(nil)
)

func wrapContainerNode(obj *coreglib.Object) *ContainerNode {
	return &ContainerNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalContainerNode(p uintptr) (interface{}, error) {
	return wrapContainerNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewContainerNode creates a new GskRenderNode instance for holding the given
// children.
//
// The new node will acquire a reference to each of the children.
//
// The function takes the following parameters:
//
//   - children of the node.
//
// The function returns the following values:
//
//   - containerNode: new GskRenderNode.
func NewContainerNode(children []RenderNoder) *ContainerNode {
	var _arg1 **C.GskRenderNode // out
	var _arg2 C.guint
	var _cret *C.GskRenderNode // in

	_arg2 = (C.guint)(len(children))
	_arg1 = (**C.GskRenderNode)(C.calloc(C.size_t(len(children)), C.size_t(unsafe.Sizeof(uint(0)))))
	defer C.free(unsafe.Pointer(_arg1))
	{
		out := unsafe.Slice((**C.GskRenderNode)(_arg1), len(children))
		for i := range children {
			out[i] = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(children[i]).Native()))
		}
	}

	_cret = C.gsk_container_node_new(_arg1, _arg2)
	runtime.KeepAlive(children)

	var _containerNode *ContainerNode // out

	_containerNode = wrapContainerNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _containerNode
}

// Child gets one of the children of container.
//
// The function takes the following parameters:
//
//   - idx: position of the child to get.
//
// The function returns the following values:
//
//   - renderNode: idx'th child of container.
func (node *ContainerNode) Child(idx uint) RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _arg1 C.guint          // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))
	_arg1 = C.guint(idx)

	_cret = C.gsk_container_node_get_child(_arg0, _arg1)
	runtime.KeepAlive(node)
	runtime.KeepAlive(idx)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// NChildren retrieves the number of direct children of node.
//
// The function returns the following values:
//
//   - guint: number of children of the GskRenderNode.
func (node *ContainerNode) NChildren() uint {
	var _arg0 *C.GskRenderNode // out
	var _cret C.guint          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_container_node_get_n_children(_arg0)
	runtime.KeepAlive(node)

	var _guint uint // out

	_guint = uint(_cret)

	return _guint
}

// CrossFadeNode: render node cross fading between two child nodes.
type CrossFadeNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*CrossFadeNode)(nil)
)

func wrapCrossFadeNode(obj *coreglib.Object) *CrossFadeNode {
	return &CrossFadeNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalCrossFadeNode(p uintptr) (interface{}, error) {
	return wrapCrossFadeNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewCrossFadeNode creates a GskRenderNode that will do a cross-fade between
// start and end.
//
// The function takes the following parameters:
//
//   - start node to be drawn.
//   - end: node to be cross_fadeed onto the start node.
//   - progress: how far the fade has progressed from start to end. The value
//     will be clamped to the range [0 ... 1].
//
// The function returns the following values:
//
//   - crossFadeNode: new GskRenderNode.
func NewCrossFadeNode(start, end RenderNoder, progress float32) *CrossFadeNode {
	var _arg1 *C.GskRenderNode // out
	var _arg2 *C.GskRenderNode // out
	var _arg3 C.float          // out
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(start).Native()))
	_arg2 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(end).Native()))
	_arg3 = C.float(progress)

	_cret = C.gsk_cross_fade_node_new(_arg1, _arg2, _arg3)
	runtime.KeepAlive(start)
	runtime.KeepAlive(end)
	runtime.KeepAlive(progress)

	var _crossFadeNode *CrossFadeNode // out

	_crossFadeNode = wrapCrossFadeNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _crossFadeNode
}

// EndChild retrieves the child GskRenderNode at the end of the cross-fade.
//
// The function returns the following values:
//
//   - renderNode: GskRenderNode.
func (node *CrossFadeNode) EndChild() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_cross_fade_node_get_end_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// Progress retrieves the progress value of the cross fade.
//
// The function returns the following values:
//
//   - gfloat progress value, between 0 and 1.
func (node *CrossFadeNode) Progress() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_cross_fade_node_get_progress(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// StartChild retrieves the child GskRenderNode at the beginning of the
// cross-fade.
//
// The function returns the following values:
//
//   - renderNode: GskRenderNode.
func (node *CrossFadeNode) StartChild() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_cross_fade_node_get_start_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// DebugNode: render node that emits a debugging message when drawing its child
// node.
type DebugNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*DebugNode)(nil)
)

func wrapDebugNode(obj *coreglib.Object) *DebugNode {
	return &DebugNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalDebugNode(p uintptr) (interface{}, error) {
	return wrapDebugNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewDebugNode creates a GskRenderNode that will add debug information about
// the given child.
//
// Adding this node has no visual effect.
//
// The function takes the following parameters:
//
//   - child to add debug info for.
//   - message: debug message.
//
// The function returns the following values:
//
//   - debugNode: new GskRenderNode.
func NewDebugNode(child RenderNoder, message string) *DebugNode {
	var _arg1 *C.GskRenderNode // out
	var _arg2 *C.char          // out
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(child).Native()))
	_arg2 = (*C.char)(unsafe.Pointer(C.CString(message)))

	_cret = C.gsk_debug_node_new(_arg1, _arg2)
	runtime.KeepAlive(child)
	runtime.KeepAlive(message)

	var _debugNode *DebugNode // out

	_debugNode = wrapDebugNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _debugNode
}

// Child gets the child node that is getting drawn by the given node.
//
// The function returns the following values:
//
//   - renderNode: child GskRenderNode.
func (node *DebugNode) Child() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_debug_node_get_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// Message gets the debug message that was set on this node.
//
// The function returns the following values:
//
//   - utf8: debug message.
func (node *DebugNode) Message() string {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.char          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_debug_node_get_message(_arg0)
	runtime.KeepAlive(node)

	var _utf8 string // out

	_utf8 = C.GoString((*C.gchar)(unsafe.Pointer(_cret)))

	return _utf8
}

// FillNode: render node filling the area given by gsk.Path and gsk.FillRule
// with the child node.
type FillNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*FillNode)(nil)
)

func wrapFillNode(obj *coreglib.Object) *FillNode {
	return &FillNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalFillNode(p uintptr) (interface{}, error) {
	return wrapFillNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewFillNode creates a GskRenderNode that will fill the child in the area
// given by path and fill_rule.
//
// The function takes the following parameters:
//
//   - child: node to fill the area with.
//   - path describing the area to fill.
//   - fillRule: fill rule to use.
//
// The function returns the following values:
//
//   - fillNode: new GskRenderNode.
func NewFillNode(child RenderNoder, path *Path, fillRule FillRule) *FillNode {
	var _arg1 *C.GskRenderNode // out
	var _arg2 *C.GskPath       // out
	var _arg3 C.GskFillRule    // out
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(child).Native()))
	_arg2 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(path)))
	_arg3 = C.GskFillRule(fillRule)

	_cret = C.gsk_fill_node_new(_arg1, _arg2, _arg3)
	runtime.KeepAlive(child)
	runtime.KeepAlive(path)
	runtime.KeepAlive(fillRule)

	var _fillNode *FillNode // out

	_fillNode = wrapFillNode(coreglib.Take(unsafe.Pointer(_cret)))

	return _fillNode
}

// Child gets the child node that is getting drawn by the given node.
//
// The function returns the following values:
//
//   - renderNode: child that is getting drawn.
func (node *FillNode) Child() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_fill_node_get_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// FillRule retrieves the fill rule used to determine how the path is filled.
//
// The function returns the following values:
//
//   - fillRule: GskFillRule.
func (node *FillNode) FillRule() FillRule {
	var _arg0 *C.GskRenderNode // out
	var _cret C.GskFillRule    // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_fill_node_get_fill_rule(_arg0)
	runtime.KeepAlive(node)

	var _fillRule FillRule // out

	_fillRule = FillRule(_cret)

	return _fillRule
}

// Path retrieves the path used to describe the area filled with the contents of
// the node.
//
// The function returns the following values:
//
//   - path: GskPath.
func (node *FillNode) Path() *Path {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskPath       // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_fill_node_get_path(_arg0)
	runtime.KeepAlive(node)

	var _path *Path // out

	_path = (*Path)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	C.gsk_path_ref(_cret)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_path)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_path_unref((*C.GskPath)(intern.C))
		},
	)

	return _path
}

// GLShaderOverrides contains methods that are overridable.
type GLShaderOverrides struct {
}

func defaultGLShaderOverrides(v *GLShader) GLShaderOverrides {
	return GLShaderOverrides{}
}

// GLShader: GskGLShader is a snippet of GLSL that is meant to run in the
// fragment shader of the rendering pipeline.
//
// A fragment shader gets the coordinates being rendered as input and produces
// the pixel values for that particular pixel. Additionally, the shader can
// declare a set of other input arguments, called uniforms (as they are uniform
// over all the calls to your shader in each instance of use). A shader can also
// receive up to 4 textures that it can use as input when producing the pixel
// data.
//
// GskGLShader is usually used with gtk_snapshot_push_gl_shader() to produce a
// gsk.GLShaderNode in the rendering hierarchy, and then its input textures are
// constructed by rendering the child nodes to textures before rendering the
// shader node itself. (You can pass texture nodes as children if you want to
// directly use a texture as input).
//
// The actual shader code is GLSL code that gets combined with some other code
// into the fragment shader. Since the exact capabilities of the GPU driver
// differs between different OpenGL drivers and hardware, GTK adds some defines
// that you can use to ensure your GLSL code runs on as many drivers as it can.
//
// If the OpenGL driver is GLES, then the shader language version is set to 100,
// and GSK_GLES will be defined in the shader.
//
// Otherwise, if the OpenGL driver does not support the 3.2 core profile,
// then the shader will run with language version 110 for GL2 and 130 for GL3,
// and GSK_LEGACY will be defined in the shader.
//
// If the OpenGL driver supports the 3.2 code profile, it will be used,
// the shader language version is set to 150, and GSK_GL3 will be defined in the
// shader.
//
// The main function the shader must implement is:
//
//	void mainImage(out vec4 fragColor,
//	               in vec2 fragCoord,
//	               in vec2 resolution,
//	               in vec2 uv)
//
// Where the input fragCoord is the coordinate of the pixel we're currently
// rendering, relative to the boundary rectangle that was specified in the
// GskGLShaderNode, and resolution is the width and height of that rectangle.
// This is in the typical GTK coordinate system with the origin in the top left.
// uv contains the u and v coordinates that can be used to index a texture at
// the corresponding point. These coordinates are in the [0..1]x[0..1] region,
// with 0, 0 being in the lower left corder (which is typical for OpenGL).
//
// The output fragColor should be a RGBA color (with premultiplied alpha) that
// will be used as the output for the specified pixel location. Note that this
// output will be automatically clipped to the clip region of the glshader node.
//
// In addition to the function arguments the shader can define up to 4 uniforms
// for textures which must be called u_textureN (i.e. u_texture1 to u_texture4)
// as well as any custom uniforms you want of types int, uint, bool, float,
// vec2, vec3 or vec4.
//
// All textures sources contain premultiplied alpha colors, but if some there
// are outer sources of colors there is a gsk_premultiply() helper to compute
// premultiplication when needed.
//
// Note that GTK parses the uniform declarations, so each uniform has to be on a
// line by itself with no other code, like so:
//
//	uniform float u_time;
//	uniform vec3 u_color;
//	uniform sampler2D u_texture1;
//	uniform sampler2D u_texture2;
//
// GTK uses the "gsk" namespace in the symbols it uses in the shader, so your
// code should not use any symbols with the prefix gsk or GSK. There are some
// helper functions declared that you can use:
//
//	vec4 GskTexture(sampler2D sampler, vec2 texCoords);
//
// This samples a texture (e.g. u_texture1) at the specified coordinates, and
// contains some helper ifdefs to ensure that it works on all OpenGL versions.
//
// You can compile the shader yourself using gsk.GLShader.Compile(), otherwise
// the GSK renderer will do it when it handling the glshader node. If errors
// occurs, the returned error will include the glsl sources, so you can see
// what GSK was passing to the compiler. You can also set GSK_DEBUG=shaders in
// the environment to see the sources and other relevant information about all
// shaders that GSK is handling.
//
// An example shader
//
//	uniform float position;
//	uniform sampler2D u_texture1;
//	uniform sampler2D u_texture2;
//
//	void mainImage(out vec4 fragColor,
//	               in vec2 fragCoord,
//	               in vec2 resolution,
//	               in vec2 uv) {
//	  vec4 source1 = GskTexture(u_texture1, uv);
//	  vec4 source2 = GskTexture(u_texture2, uv);
//
//	  fragColor = position * source1 + (1.0 - position) * source2;
//	}.
type GLShader struct {
	_ [0]func() // equal guard
	*coreglib.Object
}

var (
	_ coreglib.Objector = (*GLShader)(nil)
)

func init() {
	coreglib.RegisterClassInfo[*GLShader, *GLShaderClass, GLShaderOverrides](
		GTypeGLShader,
		initGLShaderClass,
		wrapGLShader,
		defaultGLShaderOverrides,
	)
}

func initGLShaderClass(gclass unsafe.Pointer, overrides GLShaderOverrides, classInitFunc func(*GLShaderClass)) {
	if classInitFunc != nil {
		class := (*GLShaderClass)(gextras.NewStructNative(gclass))
		classInitFunc(class)
	}
}

func wrapGLShader(obj *coreglib.Object) *GLShader {
	return &GLShader{
		Object: obj,
	}
}

func marshalGLShader(p uintptr) (interface{}, error) {
	return wrapGLShader(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewGLShaderFromBytes creates a GskGLShader that will render pixels using the
// specified code.
//
// The function takes the following parameters:
//
//   - sourcecode: GLSL sourcecode for the shader, as a GBytes.
//
// The function returns the following values:
//
//   - glShader: new GskGLShader.
func NewGLShaderFromBytes(sourcecode *glib.Bytes) *GLShader {
	var _arg1 *C.GBytes      // out
	var _cret *C.GskGLShader // in

	_arg1 = (*C.GBytes)(gextras.StructNative(unsafe.Pointer(sourcecode)))

	_cret = C.gsk_gl_shader_new_from_bytes(_arg1)
	runtime.KeepAlive(sourcecode)

	var _glShader *GLShader // out

	_glShader = wrapGLShader(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _glShader
}

// NewGLShaderFromResource creates a GskGLShader that will render pixels using
// the specified code.
//
// The function takes the following parameters:
//
//   - resourcePath: path to a resource that contains the GLSL sourcecode for
//     the shader.
//
// The function returns the following values:
//
//   - glShader: new GskGLShader.
func NewGLShaderFromResource(resourcePath string) *GLShader {
	var _arg1 *C.char        // out
	var _cret *C.GskGLShader // in

	_arg1 = (*C.char)(unsafe.Pointer(C.CString(resourcePath)))
	defer C.free(unsafe.Pointer(_arg1))

	_cret = C.gsk_gl_shader_new_from_resource(_arg1)
	runtime.KeepAlive(resourcePath)

	var _glShader *GLShader // out

	_glShader = wrapGLShader(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _glShader
}

// Compile tries to compile the shader for the given renderer.
//
// If there is a problem, this function returns FALSE and reports an error.
// You should use this function before relying on the shader for rendering and
// use a fallback with a simpler shader or without shaders if it fails.
//
// Note that this will modify the rendering state (for example change the
// current GL context) and requires the renderer to be set up. This means
// that the widget has to be realized. Commonly you want to call this from the
// realize signal of a widget, or during widget snapshot.
//
// The function takes the following parameters:
//
//   - renderer: GskRenderer.
func (shader *GLShader) Compile(renderer Rendererer) error {
	var _arg0 *C.GskGLShader // out
	var _arg1 *C.GskRenderer // out
	var _cerr *C.GError      // in

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))
	_arg1 = (*C.GskRenderer)(unsafe.Pointer(coreglib.InternObject(renderer).Native()))

	C.gsk_gl_shader_compile(_arg0, _arg1, &_cerr)
	runtime.KeepAlive(shader)
	runtime.KeepAlive(renderer)

	var _goerr error // out

	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _goerr
}

// FindUniformByName looks for a uniform by the name name, and returns the index
// of the uniform, or -1 if it was not found.
//
// The function takes the following parameters:
//
//   - name: uniform name.
//
// The function returns the following values:
//
//   - gint: index of the uniform, or -1.
func (shader *GLShader) FindUniformByName(name string) int {
	var _arg0 *C.GskGLShader // out
	var _arg1 *C.char        // out
	var _cret C.int          // in

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))
	_arg1 = (*C.char)(unsafe.Pointer(C.CString(name)))
	defer C.free(unsafe.Pointer(_arg1))

	_cret = C.gsk_gl_shader_find_uniform_by_name(_arg0, _arg1)
	runtime.KeepAlive(shader)
	runtime.KeepAlive(name)

	var _gint int // out

	_gint = int(_cret)

	return _gint
}

// ArgBool gets the value of the uniform idx in the args block.
//
// The uniform must be of bool type.
//
// The function takes the following parameters:
//
//   - args: uniform arguments.
//   - idx: index of the uniform.
//
// The function returns the following values:
//
//   - ok: value.
func (shader *GLShader) ArgBool(args *glib.Bytes, idx int) bool {
	var _arg0 *C.GskGLShader // out
	var _arg1 *C.GBytes      // out
	var _arg2 C.int          // out
	var _cret C.gboolean     // in

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))
	_arg1 = (*C.GBytes)(gextras.StructNative(unsafe.Pointer(args)))
	_arg2 = C.int(idx)

	_cret = C.gsk_gl_shader_get_arg_bool(_arg0, _arg1, _arg2)
	runtime.KeepAlive(shader)
	runtime.KeepAlive(args)
	runtime.KeepAlive(idx)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// ArgFloat gets the value of the uniform idx in the args block.
//
// The uniform must be of float type.
//
// The function takes the following parameters:
//
//   - args: uniform arguments.
//   - idx: index of the uniform.
//
// The function returns the following values:
//
//   - gfloat: value.
func (shader *GLShader) ArgFloat(args *glib.Bytes, idx int) float32 {
	var _arg0 *C.GskGLShader // out
	var _arg1 *C.GBytes      // out
	var _arg2 C.int          // out
	var _cret C.float        // in

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))
	_arg1 = (*C.GBytes)(gextras.StructNative(unsafe.Pointer(args)))
	_arg2 = C.int(idx)

	_cret = C.gsk_gl_shader_get_arg_float(_arg0, _arg1, _arg2)
	runtime.KeepAlive(shader)
	runtime.KeepAlive(args)
	runtime.KeepAlive(idx)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// ArgInt gets the value of the uniform idx in the args block.
//
// The uniform must be of int type.
//
// The function takes the following parameters:
//
//   - args: uniform arguments.
//   - idx: index of the uniform.
//
// The function returns the following values:
//
//   - gint32: value.
func (shader *GLShader) ArgInt(args *glib.Bytes, idx int) int32 {
	var _arg0 *C.GskGLShader // out
	var _arg1 *C.GBytes      // out
	var _arg2 C.int          // out
	var _cret C.gint32       // in

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))
	_arg1 = (*C.GBytes)(gextras.StructNative(unsafe.Pointer(args)))
	_arg2 = C.int(idx)

	_cret = C.gsk_gl_shader_get_arg_int(_arg0, _arg1, _arg2)
	runtime.KeepAlive(shader)
	runtime.KeepAlive(args)
	runtime.KeepAlive(idx)

	var _gint32 int32 // out

	_gint32 = int32(_cret)

	return _gint32
}

// ArgUint gets the value of the uniform idx in the args block.
//
// The uniform must be of uint type.
//
// The function takes the following parameters:
//
//   - args: uniform arguments.
//   - idx: index of the uniform.
//
// The function returns the following values:
//
//   - guint32: value.
func (shader *GLShader) ArgUint(args *glib.Bytes, idx int) uint32 {
	var _arg0 *C.GskGLShader // out
	var _arg1 *C.GBytes      // out
	var _arg2 C.int          // out
	var _cret C.guint32      // in

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))
	_arg1 = (*C.GBytes)(gextras.StructNative(unsafe.Pointer(args)))
	_arg2 = C.int(idx)

	_cret = C.gsk_gl_shader_get_arg_uint(_arg0, _arg1, _arg2)
	runtime.KeepAlive(shader)
	runtime.KeepAlive(args)
	runtime.KeepAlive(idx)

	var _guint32 uint32 // out

	_guint32 = uint32(_cret)

	return _guint32
}

// ArgVec2 gets the value of the uniform idx in the args block.
//
// The uniform must be of vec2 type.
//
// The function takes the following parameters:
//
//   - args: uniform arguments.
//   - idx: index of the uniform.
//   - outValue: location to store the uniform value in.
func (shader *GLShader) ArgVec2(args *glib.Bytes, idx int, outValue *graphene.Vec2) {
	var _arg0 *C.GskGLShader     // out
	var _arg1 *C.GBytes          // out
	var _arg2 C.int              // out
	var _arg3 *C.graphene_vec2_t // out

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))
	_arg1 = (*C.GBytes)(gextras.StructNative(unsafe.Pointer(args)))
	_arg2 = C.int(idx)
	_arg3 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(outValue)))

	C.gsk_gl_shader_get_arg_vec2(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(shader)
	runtime.KeepAlive(args)
	runtime.KeepAlive(idx)
	runtime.KeepAlive(outValue)
}

// ArgVec3 gets the value of the uniform idx in the args block.
//
// The uniform must be of vec3 type.
//
// The function takes the following parameters:
//
//   - args: uniform arguments.
//   - idx: index of the uniform.
//   - outValue: location to store the uniform value in.
func (shader *GLShader) ArgVec3(args *glib.Bytes, idx int, outValue *graphene.Vec3) {
	var _arg0 *C.GskGLShader     // out
	var _arg1 *C.GBytes          // out
	var _arg2 C.int              // out
	var _arg3 *C.graphene_vec3_t // out

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))
	_arg1 = (*C.GBytes)(gextras.StructNative(unsafe.Pointer(args)))
	_arg2 = C.int(idx)
	_arg3 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(outValue)))

	C.gsk_gl_shader_get_arg_vec3(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(shader)
	runtime.KeepAlive(args)
	runtime.KeepAlive(idx)
	runtime.KeepAlive(outValue)
}

// ArgVec4 gets the value of the uniform idx in the args block.
//
// The uniform must be of vec4 type.
//
// The function takes the following parameters:
//
//   - args: uniform arguments.
//   - idx: index of the uniform.
//   - outValue: location to store set the uniform value in.
func (shader *GLShader) ArgVec4(args *glib.Bytes, idx int, outValue *graphene.Vec4) {
	var _arg0 *C.GskGLShader     // out
	var _arg1 *C.GBytes          // out
	var _arg2 C.int              // out
	var _arg3 *C.graphene_vec4_t // out

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))
	_arg1 = (*C.GBytes)(gextras.StructNative(unsafe.Pointer(args)))
	_arg2 = C.int(idx)
	_arg3 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(outValue)))

	C.gsk_gl_shader_get_arg_vec4(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(shader)
	runtime.KeepAlive(args)
	runtime.KeepAlive(idx)
	runtime.KeepAlive(outValue)
}

// ArgsSize: get the size of the data block used to specify arguments for this
// shader.
//
// The function returns the following values:
//
//   - gsize: size of the data block.
func (shader *GLShader) ArgsSize() uint {
	var _arg0 *C.GskGLShader // out
	var _cret C.gsize        // in

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))

	_cret = C.gsk_gl_shader_get_args_size(_arg0)
	runtime.KeepAlive(shader)

	var _gsize uint // out

	_gsize = uint(_cret)

	return _gsize
}

// NTextures returns the number of textures that the shader requires.
//
// This can be used to check that the a passed shader works in your usecase.
// It is determined by looking at the highest u_textureN value that the shader
// defines.
//
// The function returns the following values:
//
//   - gint: number of texture inputs required by shader.
func (shader *GLShader) NTextures() int {
	var _arg0 *C.GskGLShader // out
	var _cret C.int          // in

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))

	_cret = C.gsk_gl_shader_get_n_textures(_arg0)
	runtime.KeepAlive(shader)

	var _gint int // out

	_gint = int(_cret)

	return _gint
}

// NUniforms: get the number of declared uniforms for this shader.
//
// The function returns the following values:
//
//   - gint: number of declared uniforms.
func (shader *GLShader) NUniforms() int {
	var _arg0 *C.GskGLShader // out
	var _cret C.int          // in

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))

	_cret = C.gsk_gl_shader_get_n_uniforms(_arg0)
	runtime.KeepAlive(shader)

	var _gint int // out

	_gint = int(_cret)

	return _gint
}

// Resource gets the resource path for the GLSL sourcecode being used to render
// this shader.
//
// The function returns the following values:
//
//   - utf8 (optional): resource path for the shader.
func (shader *GLShader) Resource() string {
	var _arg0 *C.GskGLShader // out
	var _cret *C.char        // in

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))

	_cret = C.gsk_gl_shader_get_resource(_arg0)
	runtime.KeepAlive(shader)

	var _utf8 string // out

	if _cret != nil {
		_utf8 = C.GoString((*C.gchar)(unsafe.Pointer(_cret)))
	}

	return _utf8
}

// Source gets the GLSL sourcecode being used to render this shader.
//
// The function returns the following values:
//
//   - bytes: source code for the shader.
func (shader *GLShader) Source() *glib.Bytes {
	var _arg0 *C.GskGLShader // out
	var _cret *C.GBytes      // in

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))

	_cret = C.gsk_gl_shader_get_source(_arg0)
	runtime.KeepAlive(shader)

	var _bytes *glib.Bytes // out

	_bytes = (*glib.Bytes)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	C.g_bytes_ref(_cret)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_bytes)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_bytes_unref((*C.GBytes)(intern.C))
		},
	)

	return _bytes
}

// UniformName: get the name of the declared uniform for this shader at index
// idx.
//
// The function takes the following parameters:
//
//   - idx: index of the uniform.
//
// The function returns the following values:
//
//   - utf8: name of the declared uniform.
func (shader *GLShader) UniformName(idx int) string {
	var _arg0 *C.GskGLShader // out
	var _arg1 C.int          // out
	var _cret *C.char        // in

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))
	_arg1 = C.int(idx)

	_cret = C.gsk_gl_shader_get_uniform_name(_arg0, _arg1)
	runtime.KeepAlive(shader)
	runtime.KeepAlive(idx)

	var _utf8 string // out

	_utf8 = C.GoString((*C.gchar)(unsafe.Pointer(_cret)))

	return _utf8
}

// UniformOffset: get the offset into the data block where data for this
// uniforms is stored.
//
// The function takes the following parameters:
//
//   - idx: index of the uniform.
//
// The function returns the following values:
//
//   - gint: data offset.
func (shader *GLShader) UniformOffset(idx int) int {
	var _arg0 *C.GskGLShader // out
	var _arg1 C.int          // out
	var _cret C.int          // in

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))
	_arg1 = C.int(idx)

	_cret = C.gsk_gl_shader_get_uniform_offset(_arg0, _arg1)
	runtime.KeepAlive(shader)
	runtime.KeepAlive(idx)

	var _gint int // out

	_gint = int(_cret)

	return _gint
}

// UniformType: get the type of the declared uniform for this shader at index
// idx.
//
// The function takes the following parameters:
//
//   - idx: index of the uniform.
//
// The function returns the following values:
//
//   - glUniformType: type of the declared uniform.
func (shader *GLShader) UniformType(idx int) GLUniformType {
	var _arg0 *C.GskGLShader     // out
	var _arg1 C.int              // out
	var _cret C.GskGLUniformType // in

	_arg0 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))
	_arg1 = C.int(idx)

	_cret = C.gsk_gl_shader_get_uniform_type(_arg0, _arg1)
	runtime.KeepAlive(shader)
	runtime.KeepAlive(idx)

	var _glUniformType GLUniformType // out

	_glUniformType = GLUniformType(_cret)

	return _glUniformType
}

// GLShaderNode: render node using a GL shader when drawing its children nodes.
type GLShaderNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*GLShaderNode)(nil)
)

func wrapGLShaderNode(obj *coreglib.Object) *GLShaderNode {
	return &GLShaderNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalGLShaderNode(p uintptr) (interface{}, error) {
	return wrapGLShaderNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewGLShaderNode creates a GskRenderNode that will render the given shader
// into the area given by bounds.
//
// The args is a block of data to use for uniform input, as per types
// and offsets defined by the shader. Normally this is generated by
// gsk.GLShader.FormatArgs() or gsk.ShaderArgsBuilder.
//
// See gsk.GLShader for details about how the shader should be written.
//
// All the children will be rendered into textures (if they aren't already
// GskTextureNodes, which will be used directly). These textures will be sent as
// input to the shader.
//
// If the renderer doesn't support GL shaders, or if there is any problem
// when compiling the shader, then the node will draw pink. You should use
// gsk.GLShader.Compile() to ensure the shader will work for the renderer before
// using it.
//
// The function takes the following parameters:
//
//   - shader: GskGLShader.
//   - bounds: rectangle to render the shader into.
//   - args arguments for the uniforms.
//   - children (optional): array of child nodes, these will be rendered to
//     textures and used as input.
//
// The function returns the following values:
//
//   - glShaderNode: new GskRenderNode.
func NewGLShaderNode(shader *GLShader, bounds *graphene.Rect, args *glib.Bytes, children []RenderNoder) *GLShaderNode {
	var _arg1 *C.GskGLShader     // out
	var _arg2 *C.graphene_rect_t // out
	var _arg3 *C.GBytes          // out
	var _arg4 **C.GskRenderNode  // out
	var _arg5 C.guint
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))
	_arg2 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(bounds)))
	_arg3 = (*C.GBytes)(gextras.StructNative(unsafe.Pointer(args)))
	if children != nil {
		_arg5 = (C.guint)(len(children))
		_arg4 = (**C.GskRenderNode)(C.calloc(C.size_t(len(children)), C.size_t(unsafe.Sizeof(uint(0)))))
		defer C.free(unsafe.Pointer(_arg4))
		{
			out := unsafe.Slice((**C.GskRenderNode)(_arg4), len(children))
			for i := range children {
				out[i] = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(children[i]).Native()))
			}
		}
	}

	_cret = C.gsk_gl_shader_node_new(_arg1, _arg2, _arg3, _arg4, _arg5)
	runtime.KeepAlive(shader)
	runtime.KeepAlive(bounds)
	runtime.KeepAlive(args)
	runtime.KeepAlive(children)

	var _glShaderNode *GLShaderNode // out

	_glShaderNode = wrapGLShaderNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _glShaderNode
}

// Args gets args for the node.
//
// The function returns the following values:
//
//   - bytes: GBytes with the uniform arguments.
func (node *GLShaderNode) Args() *glib.Bytes {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GBytes        // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_gl_shader_node_get_args(_arg0)
	runtime.KeepAlive(node)

	var _bytes *glib.Bytes // out

	_bytes = (*glib.Bytes)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	C.g_bytes_ref(_cret)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_bytes)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_bytes_unref((*C.GBytes)(intern.C))
		},
	)

	return _bytes
}

// Child gets one of the children.
//
// The function takes the following parameters:
//
//   - idx: position of the child to get.
//
// The function returns the following values:
//
//   - renderNode: idx'th child of node.
func (node *GLShaderNode) Child(idx uint) RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _arg1 C.guint          // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))
	_arg1 = C.guint(idx)

	_cret = C.gsk_gl_shader_node_get_child(_arg0, _arg1)
	runtime.KeepAlive(node)
	runtime.KeepAlive(idx)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// NChildren returns the number of children.
//
// The function returns the following values:
//
//   - guint: number of children.
func (node *GLShaderNode) NChildren() uint {
	var _arg0 *C.GskRenderNode // out
	var _cret C.guint          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_gl_shader_node_get_n_children(_arg0)
	runtime.KeepAlive(node)

	var _guint uint // out

	_guint = uint(_cret)

	return _guint
}

// Shader gets shader code for the node.
//
// The function returns the following values:
//
//   - glShader: GskGLShader shader.
func (node *GLShaderNode) Shader() *GLShader {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskGLShader   // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_gl_shader_node_get_shader(_arg0)
	runtime.KeepAlive(node)

	var _glShader *GLShader // out

	_glShader = wrapGLShader(coreglib.Take(unsafe.Pointer(_cret)))

	return _glShader
}

// InsetShadowNode: render node for an inset shadow.
type InsetShadowNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*InsetShadowNode)(nil)
)

func wrapInsetShadowNode(obj *coreglib.Object) *InsetShadowNode {
	return &InsetShadowNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalInsetShadowNode(p uintptr) (interface{}, error) {
	return wrapInsetShadowNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewInsetShadowNode creates a GskRenderNode that will render an inset shadow
// into the box given by outline.
//
// The function takes the following parameters:
//
//   - outline of the region containing the shadow.
//   - color of the shadow.
//   - dx: horizontal offset of shadow.
//   - dy: vertical offset of shadow.
//   - spread: how far the shadow spreads towards the inside.
//   - blurRadius: how much blur to apply to the shadow.
//
// The function returns the following values:
//
//   - insetShadowNode: new GskRenderNode.
func NewInsetShadowNode(outline *RoundedRect, color *gdk.RGBA, dx, dy, spread, blurRadius float32) *InsetShadowNode {
	var _arg1 *C.GskRoundedRect // out
	var _arg2 *C.GdkRGBA        // out
	var _arg3 C.float           // out
	var _arg4 C.float           // out
	var _arg5 C.float           // out
	var _arg6 C.float           // out
	var _cret *C.GskRenderNode  // in

	_arg1 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(outline)))
	_arg2 = (*C.GdkRGBA)(gextras.StructNative(unsafe.Pointer(color)))
	_arg3 = C.float(dx)
	_arg4 = C.float(dy)
	_arg5 = C.float(spread)
	_arg6 = C.float(blurRadius)

	_cret = C.gsk_inset_shadow_node_new(_arg1, _arg2, _arg3, _arg4, _arg5, _arg6)
	runtime.KeepAlive(outline)
	runtime.KeepAlive(color)
	runtime.KeepAlive(dx)
	runtime.KeepAlive(dy)
	runtime.KeepAlive(spread)
	runtime.KeepAlive(blurRadius)

	var _insetShadowNode *InsetShadowNode // out

	_insetShadowNode = wrapInsetShadowNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _insetShadowNode
}

// BlurRadius retrieves the blur radius to apply to the shadow.
//
// The function returns the following values:
//
//   - gfloat: blur radius, in pixels.
func (node *InsetShadowNode) BlurRadius() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_inset_shadow_node_get_blur_radius(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Color retrieves the color of the inset shadow.
//
// The function returns the following values:
//
//   - rgbA: color of the shadow.
func (node *InsetShadowNode) Color() *gdk.RGBA {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GdkRGBA       // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_inset_shadow_node_get_color(_arg0)
	runtime.KeepAlive(node)

	var _rgbA *gdk.RGBA // out

	_rgbA = (*gdk.RGBA)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _rgbA
}

// Dx retrieves the horizontal offset of the inset shadow.
//
// The function returns the following values:
//
//   - gfloat: offset, in pixels.
func (node *InsetShadowNode) Dx() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_inset_shadow_node_get_dx(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Dy retrieves the vertical offset of the inset shadow.
//
// The function returns the following values:
//
//   - gfloat: offset, in pixels.
func (node *InsetShadowNode) Dy() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_inset_shadow_node_get_dy(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Outline retrieves the outline rectangle of the inset shadow.
//
// The function returns the following values:
//
//   - roundedRect: rounded rectangle.
func (node *InsetShadowNode) Outline() *RoundedRect {
	var _arg0 *C.GskRenderNode  // out
	var _cret *C.GskRoundedRect // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_inset_shadow_node_get_outline(_arg0)
	runtime.KeepAlive(node)

	var _roundedRect *RoundedRect // out

	_roundedRect = (*RoundedRect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _roundedRect
}

// Spread retrieves how much the shadow spreads inwards.
//
// The function returns the following values:
//
//   - gfloat: size of the shadow, in pixels.
func (node *InsetShadowNode) Spread() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_inset_shadow_node_get_spread(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// LinearGradientNode: render node for a linear gradient.
type LinearGradientNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*LinearGradientNode)(nil)
)

func wrapLinearGradientNode(obj *coreglib.Object) *LinearGradientNode {
	return &LinearGradientNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalLinearGradientNode(p uintptr) (interface{}, error) {
	return wrapLinearGradientNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewLinearGradientNode creates a GskRenderNode that will create a linear
// gradient from the given points and color stops, and render that into the area
// given by bounds.
//
// The function takes the following parameters:
//
//   - bounds: rectangle to render the linear gradient into.
//   - start: point at which the linear gradient will begin.
//   - end: point at which the linear gradient will finish.
//   - colorStops: pointer to an array of GskColorStop defining the gradient.
//     The offsets of all color stops must be increasing. The first stop's
//     offset must be >= 0 and the last stop's offset must be <= 1.
//
// The function returns the following values:
//
//   - linearGradientNode: new GskRenderNode.
func NewLinearGradientNode(bounds *graphene.Rect, start, end *graphene.Point, colorStops []ColorStop) *LinearGradientNode {
	var _arg1 *C.graphene_rect_t  // out
	var _arg2 *C.graphene_point_t // out
	var _arg3 *C.graphene_point_t // out
	var _arg4 *C.GskColorStop     // out
	var _arg5 C.gsize
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(bounds)))
	_arg2 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(start)))
	_arg3 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(end)))
	_arg5 = (C.gsize)(len(colorStops))
	_arg4 = (*C.GskColorStop)(C.calloc(C.size_t(len(colorStops)), C.size_t(C.sizeof_GskColorStop)))
	defer C.free(unsafe.Pointer(_arg4))
	{
		out := unsafe.Slice((*C.GskColorStop)(_arg4), len(colorStops))
		for i := range colorStops {
			out[i] = *(*C.GskColorStop)(gextras.StructNative(unsafe.Pointer((&colorStops[i]))))
		}
	}

	_cret = C.gsk_linear_gradient_node_new(_arg1, _arg2, _arg3, _arg4, _arg5)
	runtime.KeepAlive(bounds)
	runtime.KeepAlive(start)
	runtime.KeepAlive(end)
	runtime.KeepAlive(colorStops)

	var _linearGradientNode *LinearGradientNode // out

	_linearGradientNode = wrapLinearGradientNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _linearGradientNode
}

// ColorStops retrieves the color stops in the gradient.
//
// The function returns the following values:
//
//   - colorStops: color stops in the gradient.
func (node *LinearGradientNode) ColorStops() []ColorStop {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskColorStop  // in
	var _arg1 C.gsize          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_linear_gradient_node_get_color_stops(_arg0, &_arg1)
	runtime.KeepAlive(node)

	var _colorStops []ColorStop // out

	{
		src := unsafe.Slice((*C.GskColorStop)(_cret), _arg1)
		_colorStops = make([]ColorStop, _arg1)
		for i := 0; i < int(_arg1); i++ {
			_colorStops[i] = *(*ColorStop)(gextras.NewStructNative(unsafe.Pointer((&src[i]))))
		}
	}

	return _colorStops
}

// End retrieves the final point of the linear gradient.
//
// The function returns the following values:
//
//   - point: final point.
func (node *LinearGradientNode) End() *graphene.Point {
	var _arg0 *C.GskRenderNode    // out
	var _cret *C.graphene_point_t // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_linear_gradient_node_get_end(_arg0)
	runtime.KeepAlive(node)

	var _point *graphene.Point // out

	_point = (*graphene.Point)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _point
}

// NColorStops retrieves the number of color stops in the gradient.
//
// The function returns the following values:
//
//   - gsize: number of color stops.
func (node *LinearGradientNode) NColorStops() uint {
	var _arg0 *C.GskRenderNode // out
	var _cret C.gsize          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_linear_gradient_node_get_n_color_stops(_arg0)
	runtime.KeepAlive(node)

	var _gsize uint // out

	_gsize = uint(_cret)

	return _gsize
}

// Start retrieves the initial point of the linear gradient.
//
// The function returns the following values:
//
//   - point: initial point.
func (node *LinearGradientNode) Start() *graphene.Point {
	var _arg0 *C.GskRenderNode    // out
	var _cret *C.graphene_point_t // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_linear_gradient_node_get_start(_arg0)
	runtime.KeepAlive(node)

	var _point *graphene.Point // out

	_point = (*graphene.Point)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _point
}

// MaskNode: render node masking one child node with another.
type MaskNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*MaskNode)(nil)
)

func wrapMaskNode(obj *coreglib.Object) *MaskNode {
	return &MaskNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalMaskNode(p uintptr) (interface{}, error) {
	return wrapMaskNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewMaskNode creates a GskRenderNode that will mask a given node by another.
//
// The mask_mode determines how the 'mask values' are derived from the colors of
// the mask. Applying the mask consists of multiplying the 'mask value' with the
// alpha of the source.
//
// The function takes the following parameters:
//
//   - source node to be drawn.
//   - mask: node to be used as mask.
//   - maskMode: mask mode to use.
//
// The function returns the following values:
//
//   - maskNode: new GskRenderNode.
func NewMaskNode(source, mask RenderNoder, maskMode MaskMode) *MaskNode {
	var _arg1 *C.GskRenderNode // out
	var _arg2 *C.GskRenderNode // out
	var _arg3 C.GskMaskMode    // out
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(source).Native()))
	_arg2 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(mask).Native()))
	_arg3 = C.GskMaskMode(maskMode)

	_cret = C.gsk_mask_node_new(_arg1, _arg2, _arg3)
	runtime.KeepAlive(source)
	runtime.KeepAlive(mask)
	runtime.KeepAlive(maskMode)

	var _maskNode *MaskNode // out

	_maskNode = wrapMaskNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _maskNode
}

// Mask retrieves the mask GskRenderNode child of the node.
//
// The function returns the following values:
//
//   - renderNode: mask child node.
func (node *MaskNode) Mask() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_mask_node_get_mask(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// MaskMode retrieves the mask mode used by node.
//
// The function returns the following values:
//
//   - maskMode: mask mode.
func (node *MaskNode) MaskMode() MaskMode {
	var _arg0 *C.GskRenderNode // out
	var _cret C.GskMaskMode    // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_mask_node_get_mask_mode(_arg0)
	runtime.KeepAlive(node)

	var _maskMode MaskMode // out

	_maskMode = MaskMode(_cret)

	return _maskMode
}

// Source retrieves the source GskRenderNode child of the node.
//
// The function returns the following values:
//
//   - renderNode: source child node.
func (node *MaskNode) Source() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_mask_node_get_source(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

type NGLRenderer struct {
	_ [0]func() // equal guard
	Renderer
}

var (
	_ Rendererer = (*NGLRenderer)(nil)
)

func wrapNGLRenderer(obj *coreglib.Object) *NGLRenderer {
	return &NGLRenderer{
		Renderer: Renderer{
			Object: obj,
		},
	}
}

func marshalNGLRenderer(p uintptr) (interface{}, error) {
	return wrapNGLRenderer(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

func NewNGLRenderer() *NGLRenderer {
	var _cret *C.GskRenderer // in

	_cret = C.gsk_ngl_renderer_new()

	var _nglRenderer *NGLRenderer // out

	_nglRenderer = wrapNGLRenderer(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _nglRenderer
}

// OpacityNode: render node controlling the opacity of its single child node.
type OpacityNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*OpacityNode)(nil)
)

func wrapOpacityNode(obj *coreglib.Object) *OpacityNode {
	return &OpacityNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalOpacityNode(p uintptr) (interface{}, error) {
	return wrapOpacityNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewOpacityNode creates a GskRenderNode that will drawn the child with reduced
// opacity.
//
// The function takes the following parameters:
//
//   - child: node to draw.
//   - opacity to apply.
//
// The function returns the following values:
//
//   - opacityNode: new GskRenderNode.
func NewOpacityNode(child RenderNoder, opacity float32) *OpacityNode {
	var _arg1 *C.GskRenderNode // out
	var _arg2 C.float          // out
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(child).Native()))
	_arg2 = C.float(opacity)

	_cret = C.gsk_opacity_node_new(_arg1, _arg2)
	runtime.KeepAlive(child)
	runtime.KeepAlive(opacity)

	var _opacityNode *OpacityNode // out

	_opacityNode = wrapOpacityNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _opacityNode
}

// Child gets the child node that is getting opacityed by the given node.
//
// The function returns the following values:
//
//   - renderNode: child that is getting opacityed.
func (node *OpacityNode) Child() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_opacity_node_get_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// Opacity gets the transparency factor for an opacity node.
//
// The function returns the following values:
//
//   - gfloat: opacity factor.
func (node *OpacityNode) Opacity() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_opacity_node_get_opacity(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// OutsetShadowNode: render node for an outset shadow.
type OutsetShadowNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*OutsetShadowNode)(nil)
)

func wrapOutsetShadowNode(obj *coreglib.Object) *OutsetShadowNode {
	return &OutsetShadowNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalOutsetShadowNode(p uintptr) (interface{}, error) {
	return wrapOutsetShadowNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewOutsetShadowNode creates a GskRenderNode that will render an outset shadow
// around the box given by outline.
//
// The function takes the following parameters:
//
//   - outline of the region surrounded by shadow.
//   - color of the shadow.
//   - dx: horizontal offset of shadow.
//   - dy: vertical offset of shadow.
//   - spread: how far the shadow spreads towards the inside.
//   - blurRadius: how much blur to apply to the shadow.
//
// The function returns the following values:
//
//   - outsetShadowNode: new GskRenderNode.
func NewOutsetShadowNode(outline *RoundedRect, color *gdk.RGBA, dx, dy, spread, blurRadius float32) *OutsetShadowNode {
	var _arg1 *C.GskRoundedRect // out
	var _arg2 *C.GdkRGBA        // out
	var _arg3 C.float           // out
	var _arg4 C.float           // out
	var _arg5 C.float           // out
	var _arg6 C.float           // out
	var _cret *C.GskRenderNode  // in

	_arg1 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(outline)))
	_arg2 = (*C.GdkRGBA)(gextras.StructNative(unsafe.Pointer(color)))
	_arg3 = C.float(dx)
	_arg4 = C.float(dy)
	_arg5 = C.float(spread)
	_arg6 = C.float(blurRadius)

	_cret = C.gsk_outset_shadow_node_new(_arg1, _arg2, _arg3, _arg4, _arg5, _arg6)
	runtime.KeepAlive(outline)
	runtime.KeepAlive(color)
	runtime.KeepAlive(dx)
	runtime.KeepAlive(dy)
	runtime.KeepAlive(spread)
	runtime.KeepAlive(blurRadius)

	var _outsetShadowNode *OutsetShadowNode // out

	_outsetShadowNode = wrapOutsetShadowNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _outsetShadowNode
}

// BlurRadius retrieves the blur radius of the shadow.
//
// The function returns the following values:
//
//   - gfloat: blur radius, in pixels.
func (node *OutsetShadowNode) BlurRadius() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_outset_shadow_node_get_blur_radius(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Color retrieves the color of the outset shadow.
//
// The function returns the following values:
//
//   - rgbA: color.
func (node *OutsetShadowNode) Color() *gdk.RGBA {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GdkRGBA       // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_outset_shadow_node_get_color(_arg0)
	runtime.KeepAlive(node)

	var _rgbA *gdk.RGBA // out

	_rgbA = (*gdk.RGBA)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _rgbA
}

// Dx retrieves the horizontal offset of the outset shadow.
//
// The function returns the following values:
//
//   - gfloat: offset, in pixels.
func (node *OutsetShadowNode) Dx() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_outset_shadow_node_get_dx(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Dy retrieves the vertical offset of the outset shadow.
//
// The function returns the following values:
//
//   - gfloat: offset, in pixels.
func (node *OutsetShadowNode) Dy() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_outset_shadow_node_get_dy(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Outline retrieves the outline rectangle of the outset shadow.
//
// The function returns the following values:
//
//   - roundedRect: rounded rectangle.
func (node *OutsetShadowNode) Outline() *RoundedRect {
	var _arg0 *C.GskRenderNode  // out
	var _cret *C.GskRoundedRect // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_outset_shadow_node_get_outline(_arg0)
	runtime.KeepAlive(node)

	var _roundedRect *RoundedRect // out

	_roundedRect = (*RoundedRect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _roundedRect
}

// Spread retrieves how much the shadow spreads outwards.
//
// The function returns the following values:
//
//   - gfloat: size of the shadow, in pixels.
func (node *OutsetShadowNode) Spread() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_outset_shadow_node_get_spread(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// RadialGradientNode: render node for a radial gradient.
type RadialGradientNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*RadialGradientNode)(nil)
)

func wrapRadialGradientNode(obj *coreglib.Object) *RadialGradientNode {
	return &RadialGradientNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalRadialGradientNode(p uintptr) (interface{}, error) {
	return wrapRadialGradientNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewRadialGradientNode creates a GskRenderNode that draws a radial gradient.
//
// The radial gradient starts around center. The size of the gradient is
// dictated by hradius in horizontal orientation and by vradius in vertical
// orientation.
//
// The function takes the following parameters:
//
//   - bounds of the node.
//   - center of the gradient.
//   - hradius: horizontal radius.
//   - vradius: vertical radius.
//   - start: percentage >= 0 that defines the start of the gradient around
//     center.
//   - end: percentage >= 0 that defines the end of the gradient around center.
//   - colorStops: pointer to an array of GskColorStop defining the gradient.
//     The offsets of all color stops must be increasing. The first stop's
//     offset must be >= 0 and the last stop's offset must be <= 1.
//
// The function returns the following values:
//
//   - radialGradientNode: new GskRenderNode.
func NewRadialGradientNode(bounds *graphene.Rect, center *graphene.Point, hradius, vradius, start, end float32, colorStops []ColorStop) *RadialGradientNode {
	var _arg1 *C.graphene_rect_t  // out
	var _arg2 *C.graphene_point_t // out
	var _arg3 C.float             // out
	var _arg4 C.float             // out
	var _arg5 C.float             // out
	var _arg6 C.float             // out
	var _arg7 *C.GskColorStop     // out
	var _arg8 C.gsize
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(bounds)))
	_arg2 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(center)))
	_arg3 = C.float(hradius)
	_arg4 = C.float(vradius)
	_arg5 = C.float(start)
	_arg6 = C.float(end)
	_arg8 = (C.gsize)(len(colorStops))
	_arg7 = (*C.GskColorStop)(C.calloc(C.size_t(len(colorStops)), C.size_t(C.sizeof_GskColorStop)))
	defer C.free(unsafe.Pointer(_arg7))
	{
		out := unsafe.Slice((*C.GskColorStop)(_arg7), len(colorStops))
		for i := range colorStops {
			out[i] = *(*C.GskColorStop)(gextras.StructNative(unsafe.Pointer((&colorStops[i]))))
		}
	}

	_cret = C.gsk_radial_gradient_node_new(_arg1, _arg2, _arg3, _arg4, _arg5, _arg6, _arg7, _arg8)
	runtime.KeepAlive(bounds)
	runtime.KeepAlive(center)
	runtime.KeepAlive(hradius)
	runtime.KeepAlive(vradius)
	runtime.KeepAlive(start)
	runtime.KeepAlive(end)
	runtime.KeepAlive(colorStops)

	var _radialGradientNode *RadialGradientNode // out

	_radialGradientNode = wrapRadialGradientNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _radialGradientNode
}

// Center retrieves the center pointer for the gradient.
//
// The function returns the following values:
//
//   - point: center point for the gradient.
func (node *RadialGradientNode) Center() *graphene.Point {
	var _arg0 *C.GskRenderNode    // out
	var _cret *C.graphene_point_t // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_radial_gradient_node_get_center(_arg0)
	runtime.KeepAlive(node)

	var _point *graphene.Point // out

	_point = (*graphene.Point)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _point
}

// ColorStops retrieves the color stops in the gradient.
//
// The function returns the following values:
//
//   - colorStops: color stops in the gradient.
func (node *RadialGradientNode) ColorStops() []ColorStop {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskColorStop  // in
	var _arg1 C.gsize          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_radial_gradient_node_get_color_stops(_arg0, &_arg1)
	runtime.KeepAlive(node)

	var _colorStops []ColorStop // out

	{
		src := unsafe.Slice((*C.GskColorStop)(_cret), _arg1)
		_colorStops = make([]ColorStop, _arg1)
		for i := 0; i < int(_arg1); i++ {
			_colorStops[i] = *(*ColorStop)(gextras.NewStructNative(unsafe.Pointer((&src[i]))))
		}
	}

	return _colorStops
}

// End retrieves the end value for the gradient.
//
// The function returns the following values:
//
//   - gfloat: end value for the gradient.
func (node *RadialGradientNode) End() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_radial_gradient_node_get_end(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Hradius retrieves the horizontal radius for the gradient.
//
// The function returns the following values:
//
//   - gfloat: horizontal radius for the gradient.
func (node *RadialGradientNode) Hradius() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_radial_gradient_node_get_hradius(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// NColorStops retrieves the number of color stops in the gradient.
//
// The function returns the following values:
//
//   - gsize: number of color stops.
func (node *RadialGradientNode) NColorStops() uint {
	var _arg0 *C.GskRenderNode // out
	var _cret C.gsize          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_radial_gradient_node_get_n_color_stops(_arg0)
	runtime.KeepAlive(node)

	var _gsize uint // out

	_gsize = uint(_cret)

	return _gsize
}

// Start retrieves the start value for the gradient.
//
// The function returns the following values:
//
//   - gfloat: start value for the gradient.
func (node *RadialGradientNode) Start() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_radial_gradient_node_get_start(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Vradius retrieves the vertical radius for the gradient.
//
// The function returns the following values:
//
//   - gfloat: vertical radius for the gradient.
func (node *RadialGradientNode) Vradius() float32 {
	var _arg0 *C.GskRenderNode // out
	var _cret C.float          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_radial_gradient_node_get_vradius(_arg0)
	runtime.KeepAlive(node)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// RenderNode: GskRenderNode is the basic block in a scene graph to be rendered
// using gsk.Renderer.
//
// Each node has a parent, except the top-level node; each node may have
// children nodes.
//
// Each node has an associated drawing surface, which has the size of the
// rectangle set when creating it.
//
// Render nodes are meant to be transient; once they have been associated to
// a gsk.Renderer it's safe to release any reference you have on them. All
// gsk.RenderNodes are immutable, you can only specify their properties during
// construction.
type RenderNode struct {
	_ [0]func() // equal guard
	*coreglib.Object
}

var (
	_ coreglib.Objector = (*RenderNode)(nil)
)

// RenderNoder describes types inherited from class RenderNode.
//
// To get the original type, the caller must assert this to an interface or
// another type.
type RenderNoder interface {
	coreglib.Objector
	baseRenderNode() *RenderNode
}

var _ RenderNoder = (*RenderNode)(nil)

func wrapRenderNode(obj *coreglib.Object) *RenderNode {
	return &RenderNode{
		Object: obj,
	}
}

func marshalRenderNode(p uintptr) (interface{}, error) {
	return wrapRenderNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

func (node *RenderNode) baseRenderNode() *RenderNode {
	return node
}

// BaseRenderNode returns the underlying base object.
func BaseRenderNode(obj RenderNoder) *RenderNode {
	return obj.baseRenderNode()
}

// Draw the contents of node to the given cairo context.
//
// Typically, you'll use this function to implement fallback rendering of
// GskRenderNodes on an intermediate Cairo context, instead of using the drawing
// context associated to a gdk.Surface's rendering buffer.
//
// For advanced nodes that cannot be supported using Cairo, in particular for
// nodes doing 3D operations, this function may fail.
//
// The function takes the following parameters:
//
//   - cr: cairo context to draw to.
func (node *RenderNode) Draw(cr *cairo.Context) {
	var _arg0 *C.GskRenderNode // out
	var _arg1 *C.cairo_t       // out

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))
	_arg1 = (*C.cairo_t)(unsafe.Pointer(cr.Native()))

	C.gsk_render_node_draw(_arg0, _arg1)
	runtime.KeepAlive(node)
	runtime.KeepAlive(cr)
}

// Bounds retrieves the boundaries of the node.
//
// The node will not draw outside of its boundaries.
//
// The function returns the following values:
//
//   - bounds: return location for the boundaries.
func (node *RenderNode) Bounds() *graphene.Rect {
	var _arg0 *C.GskRenderNode  // out
	var _arg1 C.graphene_rect_t // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	C.gsk_render_node_get_bounds(_arg0, &_arg1)
	runtime.KeepAlive(node)

	var _bounds *graphene.Rect // out

	_bounds = (*graphene.Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _bounds
}

// NodeType returns the type of the node.
//
// The function returns the following values:
//
//   - renderNodeType: type of the GskRenderNode.
func (node *RenderNode) NodeType() RenderNodeType {
	var _arg0 *C.GskRenderNode    // out
	var _cret C.GskRenderNodeType // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_render_node_get_node_type(_arg0)
	runtime.KeepAlive(node)

	var _renderNodeType RenderNodeType // out

	_renderNodeType = RenderNodeType(_cret)

	return _renderNodeType
}

// Serialize serializes the node for later deserialization via
// gsk_render_node_deserialize(). No guarantees are made about the
// format used other than that the same version of GTK will be able to
// deserialize the result of a call to gsk_render_node_serialize() and
// gsk_render_node_deserialize() will correctly reject files it cannot open that
// were created with previous versions of GTK.
//
// The intended use of this functions is testing, benchmarking and debugging.
// The format is not meant as a permanent storage format.
//
// The function returns the following values:
//
//   - bytes: GBytes representing the node.
func (node *RenderNode) Serialize() *glib.Bytes {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GBytes        // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_render_node_serialize(_arg0)
	runtime.KeepAlive(node)

	var _bytes *glib.Bytes // out

	_bytes = (*glib.Bytes)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_bytes)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_bytes_unref((*C.GBytes)(intern.C))
		},
	)

	return _bytes
}

// WriteToFile: this function is equivalent to calling
// gsk.RenderNode.Serialize() followed by glib.FileSetContents().
//
// See those two functions for details on the arguments.
//
// It is mostly intended for use inside a debugger to quickly dump a render node
// to a file for later inspection.
//
// The function takes the following parameters:
//
//   - filename: file to save it to.
func (node *RenderNode) WriteToFile(filename string) error {
	var _arg0 *C.GskRenderNode // out
	var _arg1 *C.char          // out
	var _cerr *C.GError        // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))
	_arg1 = (*C.char)(unsafe.Pointer(C.CString(filename)))
	defer C.free(unsafe.Pointer(_arg1))

	C.gsk_render_node_write_to_file(_arg0, _arg1, &_cerr)
	runtime.KeepAlive(node)
	runtime.KeepAlive(filename)

	var _goerr error // out

	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _goerr
}

// RenderNodeDeserialize loads data previously created via
// gsk.RenderNode.Serialize().
//
// For a discussion of the supported format, see that function.
//
// The function takes the following parameters:
//
//   - bytes containing the data.
//   - errorFunc (optional): callback on parsing errors.
//
// The function returns the following values:
//
//   - renderNode (optional): new GskRenderNode.
func RenderNodeDeserialize(bytes *glib.Bytes, errorFunc ParseErrorFunc) RenderNoder {
	var _arg1 *C.GBytes           // out
	var _arg2 C.GskParseErrorFunc // out
	var _arg3 C.gpointer
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.GBytes)(gextras.StructNative(unsafe.Pointer(bytes)))
	if errorFunc != nil {
		_arg2 = (*[0]byte)(C._gotk4_gsk4_ParseErrorFunc)
		_arg3 = C.gpointer(gbox.Assign(errorFunc))
		defer gbox.Delete(uintptr(_arg3))
	}

	_cret = C.gsk_render_node_deserialize(_arg1, _arg2, _arg3)
	runtime.KeepAlive(bytes)
	runtime.KeepAlive(errorFunc)

	var _renderNode RenderNoder // out

	if _cret != nil {
		{
			objptr := unsafe.Pointer(_cret)

			object := coreglib.AssumeOwnership(objptr)
			casted := object.WalkCast(func(obj coreglib.Objector) bool {
				_, ok := obj.(RenderNoder)
				return ok
			})
			rv, ok := casted.(RenderNoder)
			if !ok {
				panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
			}
			_renderNode = rv
		}
	}

	return _renderNode
}

// Renderer: GskRenderer is a class that renders a scene graph defined via a
// tree of gsk.RenderNode instances.
//
// Typically you will use a GskRenderer instance to repeatedly call
// gsk.Renderer.Render() to update the contents of its associated gdk.Surface.
//
// It is necessary to realize a GskRenderer instance using
// gsk.Renderer.Realize() before calling gsk.Renderer.Render(), in order to
// create the appropriate windowing system resources needed to render the scene.
type Renderer struct {
	_ [0]func() // equal guard
	*coreglib.Object
}

var (
	_ coreglib.Objector = (*Renderer)(nil)
)

// Rendererer describes types inherited from class Renderer.
//
// To get the original type, the caller must assert this to an interface or
// another type.
type Rendererer interface {
	coreglib.Objector
	baseRenderer() *Renderer
}

var _ Rendererer = (*Renderer)(nil)

func wrapRenderer(obj *coreglib.Object) *Renderer {
	return &Renderer{
		Object: obj,
	}
}

func marshalRenderer(p uintptr) (interface{}, error) {
	return wrapRenderer(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

func (renderer *Renderer) baseRenderer() *Renderer {
	return renderer
}

// BaseRenderer returns the underlying base object.
func BaseRenderer(obj Rendererer) *Renderer {
	return obj.baseRenderer()
}

// NewRendererForSurface creates an appropriate GskRenderer instance for the
// given surface.
//
// If the GSK_RENDERER environment variable is set, GSK will try that renderer
// first, before trying the backend-specific default. The ultimate fallback is
// the cairo renderer.
//
// The renderer will be realized before it is returned.
//
// The function takes the following parameters:
//
//   - surface: GdkSurface.
//
// The function returns the following values:
//
//   - renderer (optional): GskRenderer.
func NewRendererForSurface(surface gdk.Surfacer) *Renderer {
	var _arg1 *C.GdkSurface  // out
	var _cret *C.GskRenderer // in

	_arg1 = (*C.GdkSurface)(unsafe.Pointer(coreglib.InternObject(surface).Native()))

	_cret = C.gsk_renderer_new_for_surface(_arg1)
	runtime.KeepAlive(surface)

	var _renderer *Renderer // out

	if _cret != nil {
		_renderer = wrapRenderer(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}

	return _renderer
}

// Surface retrieves the GdkSurface set using gsk_enderer_realize().
//
// If the renderer has not been realized yet, NULL will be returned.
//
// The function returns the following values:
//
//   - surface (optional): GdkSurface.
func (renderer *Renderer) Surface() gdk.Surfacer {
	var _arg0 *C.GskRenderer // out
	var _cret *C.GdkSurface  // in

	_arg0 = (*C.GskRenderer)(unsafe.Pointer(coreglib.InternObject(renderer).Native()))

	_cret = C.gsk_renderer_get_surface(_arg0)
	runtime.KeepAlive(renderer)

	var _surface gdk.Surfacer // out

	if _cret != nil {
		{
			objptr := unsafe.Pointer(_cret)

			object := coreglib.Take(objptr)
			casted := object.WalkCast(func(obj coreglib.Objector) bool {
				_, ok := obj.(gdk.Surfacer)
				return ok
			})
			rv, ok := casted.(gdk.Surfacer)
			if !ok {
				panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Surfacer")
			}
			_surface = rv
		}
	}

	return _surface
}

// IsRealized checks whether the renderer is realized or not.
//
// The function returns the following values:
//
//   - ok: TRUE if the GskRenderer was realized, and FALSE otherwise.
func (renderer *Renderer) IsRealized() bool {
	var _arg0 *C.GskRenderer // out
	var _cret C.gboolean     // in

	_arg0 = (*C.GskRenderer)(unsafe.Pointer(coreglib.InternObject(renderer).Native()))

	_cret = C.gsk_renderer_is_realized(_arg0)
	runtime.KeepAlive(renderer)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// Realize creates the resources needed by the renderer to render the scene
// graph.
//
// Since GTK 4.6, the surface may be NULL, which allows using renderers
// without having to create a surface. Since GTK 4.14, it is recommended to use
// gsk.Renderer.RealizeForDisplay() instead.
//
// Note that it is mandatory to call gsk.Renderer.Unrealize() before destroying
// the renderer.
//
// The function takes the following parameters:
//
//   - surface (optional): GdkSurface renderer will be used on.
func (renderer *Renderer) Realize(surface gdk.Surfacer) error {
	var _arg0 *C.GskRenderer // out
	var _arg1 *C.GdkSurface  // out
	var _cerr *C.GError      // in

	_arg0 = (*C.GskRenderer)(unsafe.Pointer(coreglib.InternObject(renderer).Native()))
	if surface != nil {
		_arg1 = (*C.GdkSurface)(unsafe.Pointer(coreglib.InternObject(surface).Native()))
	}

	C.gsk_renderer_realize(_arg0, _arg1, &_cerr)
	runtime.KeepAlive(renderer)
	runtime.KeepAlive(surface)

	var _goerr error // out

	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _goerr
}

// RealizeForDisplay creates the resources needed by the renderer to render the
// scene graph.
//
// Note that it is mandatory to call gsk.Renderer.Unrealize() before destroying
// the renderer.
//
// The function takes the following parameters:
//
//   - display: GdkDisplay renderer will be used on.
func (renderer *Renderer) RealizeForDisplay(display *gdk.Display) error {
	var _arg0 *C.GskRenderer // out
	var _arg1 *C.GdkDisplay  // out
	var _cerr *C.GError      // in

	_arg0 = (*C.GskRenderer)(unsafe.Pointer(coreglib.InternObject(renderer).Native()))
	_arg1 = (*C.GdkDisplay)(unsafe.Pointer(coreglib.InternObject(display).Native()))

	C.gsk_renderer_realize_for_display(_arg0, _arg1, &_cerr)
	runtime.KeepAlive(renderer)
	runtime.KeepAlive(display)

	var _goerr error // out

	if _cerr != nil {
		_goerr = gerror.Take(unsafe.Pointer(_cerr))
	}

	return _goerr
}

// Render renders the scene graph, described by a tree of GskRenderNode
// instances to the renderer's surface, ensuring that the given region gets
// redrawn.
//
// If the renderer has no associated surface, this function does nothing.
//
// Renderers must ensure that changes of the contents given by the root node
// as well as the area given by region are redrawn. They are however free to
// not redraw any pixel outside of region if they can guarantee that it didn't
// change.
//
// The renderer will acquire a reference on the GskRenderNode tree while the
// rendering is in progress.
//
// The function takes the following parameters:
//
//   - root: GskRenderNode.
//   - region (optional): cairo_region_t that must be redrawn or NULL for the
//     whole window.
func (renderer *Renderer) Render(root RenderNoder, region *cairo.Region) {
	var _arg0 *C.GskRenderer    // out
	var _arg1 *C.GskRenderNode  // out
	var _arg2 *C.cairo_region_t // out

	_arg0 = (*C.GskRenderer)(unsafe.Pointer(coreglib.InternObject(renderer).Native()))
	_arg1 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(root).Native()))
	if region != nil {
		_arg2 = (*C.cairo_region_t)(unsafe.Pointer(region.Native()))
	}

	C.gsk_renderer_render(_arg0, _arg1, _arg2)
	runtime.KeepAlive(renderer)
	runtime.KeepAlive(root)
	runtime.KeepAlive(region)
}

// RenderTexture renders the scene graph, described by a tree of GskRenderNode
// instances, to a GdkTexture.
//
// The renderer will acquire a reference on the GskRenderNode tree while the
// rendering is in progress.
//
// If you want to apply any transformations to root, you should put it into a
// transform node and pass that node instead.
//
// The function takes the following parameters:
//
//   - root: GskRenderNode.
//   - viewport (optional): section to draw or NULL to use root's bounds.
//
// The function returns the following values:
//
//   - texture: GdkTexture with the rendered contents of root.
func (renderer *Renderer) RenderTexture(root RenderNoder, viewport *graphene.Rect) gdk.Texturer {
	var _arg0 *C.GskRenderer     // out
	var _arg1 *C.GskRenderNode   // out
	var _arg2 *C.graphene_rect_t // out
	var _cret *C.GdkTexture      // in

	_arg0 = (*C.GskRenderer)(unsafe.Pointer(coreglib.InternObject(renderer).Native()))
	_arg1 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(root).Native()))
	if viewport != nil {
		_arg2 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(viewport)))
	}

	_cret = C.gsk_renderer_render_texture(_arg0, _arg1, _arg2)
	runtime.KeepAlive(renderer)
	runtime.KeepAlive(root)
	runtime.KeepAlive(viewport)

	var _texture gdk.Texturer // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gdk.Texturer is nil")
		}

		object := coreglib.AssumeOwnership(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Texturer)
			return ok
		})
		rv, ok := casted.(gdk.Texturer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Texturer")
		}
		_texture = rv
	}

	return _texture
}

// Unrealize releases all the resources created by gsk_renderer_realize().
func (renderer *Renderer) Unrealize() {
	var _arg0 *C.GskRenderer // out

	_arg0 = (*C.GskRenderer)(unsafe.Pointer(coreglib.InternObject(renderer).Native()))

	C.gsk_renderer_unrealize(_arg0)
	runtime.KeepAlive(renderer)
}

// RepeatNode: render node repeating its single child node.
type RepeatNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*RepeatNode)(nil)
)

func wrapRepeatNode(obj *coreglib.Object) *RepeatNode {
	return &RepeatNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalRepeatNode(p uintptr) (interface{}, error) {
	return wrapRepeatNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewRepeatNode creates a GskRenderNode that will repeat the drawing of child
// across the given bounds.
//
// The function takes the following parameters:
//
//   - bounds of the area to be painted.
//   - child to repeat.
//   - childBounds (optional): area of the child to repeat or NULL to use the
//     child's bounds.
//
// The function returns the following values:
//
//   - repeatNode: new GskRenderNode.
func NewRepeatNode(bounds *graphene.Rect, child RenderNoder, childBounds *graphene.Rect) *RepeatNode {
	var _arg1 *C.graphene_rect_t // out
	var _arg2 *C.GskRenderNode   // out
	var _arg3 *C.graphene_rect_t // out
	var _cret *C.GskRenderNode   // in

	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(bounds)))
	_arg2 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(child).Native()))
	if childBounds != nil {
		_arg3 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(childBounds)))
	}

	_cret = C.gsk_repeat_node_new(_arg1, _arg2, _arg3)
	runtime.KeepAlive(bounds)
	runtime.KeepAlive(child)
	runtime.KeepAlive(childBounds)

	var _repeatNode *RepeatNode // out

	_repeatNode = wrapRepeatNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _repeatNode
}

// Child retrieves the child of node.
//
// The function returns the following values:
//
//   - renderNode: GskRenderNode.
func (node *RepeatNode) Child() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_repeat_node_get_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// ChildBounds retrieves the bounding rectangle of the child of node.
//
// The function returns the following values:
//
//   - rect: bounding rectangle.
func (node *RepeatNode) ChildBounds() *graphene.Rect {
	var _arg0 *C.GskRenderNode   // out
	var _cret *C.graphene_rect_t // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_repeat_node_get_child_bounds(_arg0)
	runtime.KeepAlive(node)

	var _rect *graphene.Rect // out

	_rect = (*graphene.Rect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _rect
}

// RepeatingLinearGradientNode: render node for a repeating linear gradient.
type RepeatingLinearGradientNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*RepeatingLinearGradientNode)(nil)
)

func wrapRepeatingLinearGradientNode(obj *coreglib.Object) *RepeatingLinearGradientNode {
	return &RepeatingLinearGradientNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalRepeatingLinearGradientNode(p uintptr) (interface{}, error) {
	return wrapRepeatingLinearGradientNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewRepeatingLinearGradientNode creates a GskRenderNode that will create a
// repeating linear gradient from the given points and color stops, and render
// that into the area given by bounds.
//
// The function takes the following parameters:
//
//   - bounds: rectangle to render the linear gradient into.
//   - start: point at which the linear gradient will begin.
//   - end: point at which the linear gradient will finish.
//   - colorStops: pointer to an array of GskColorStop defining the gradient.
//     The offsets of all color stops must be increasing. The first stop's
//     offset must be >= 0 and the last stop's offset must be <= 1.
//
// The function returns the following values:
//
//   - repeatingLinearGradientNode: new GskRenderNode.
func NewRepeatingLinearGradientNode(bounds *graphene.Rect, start, end *graphene.Point, colorStops []ColorStop) *RepeatingLinearGradientNode {
	var _arg1 *C.graphene_rect_t  // out
	var _arg2 *C.graphene_point_t // out
	var _arg3 *C.graphene_point_t // out
	var _arg4 *C.GskColorStop     // out
	var _arg5 C.gsize
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(bounds)))
	_arg2 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(start)))
	_arg3 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(end)))
	_arg5 = (C.gsize)(len(colorStops))
	_arg4 = (*C.GskColorStop)(C.calloc(C.size_t(len(colorStops)), C.size_t(C.sizeof_GskColorStop)))
	defer C.free(unsafe.Pointer(_arg4))
	{
		out := unsafe.Slice((*C.GskColorStop)(_arg4), len(colorStops))
		for i := range colorStops {
			out[i] = *(*C.GskColorStop)(gextras.StructNative(unsafe.Pointer((&colorStops[i]))))
		}
	}

	_cret = C.gsk_repeating_linear_gradient_node_new(_arg1, _arg2, _arg3, _arg4, _arg5)
	runtime.KeepAlive(bounds)
	runtime.KeepAlive(start)
	runtime.KeepAlive(end)
	runtime.KeepAlive(colorStops)

	var _repeatingLinearGradientNode *RepeatingLinearGradientNode // out

	_repeatingLinearGradientNode = wrapRepeatingLinearGradientNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _repeatingLinearGradientNode
}

// RepeatingRadialGradientNode: render node for a repeating radial gradient.
type RepeatingRadialGradientNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*RepeatingRadialGradientNode)(nil)
)

func wrapRepeatingRadialGradientNode(obj *coreglib.Object) *RepeatingRadialGradientNode {
	return &RepeatingRadialGradientNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalRepeatingRadialGradientNode(p uintptr) (interface{}, error) {
	return wrapRepeatingRadialGradientNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewRepeatingRadialGradientNode creates a GskRenderNode that draws a repeating
// radial gradient.
//
// The radial gradient starts around center. The size of the gradient is
// dictated by hradius in horizontal orientation and by vradius in vertical
// orientation.
//
// The function takes the following parameters:
//
//   - bounds of the node.
//   - center of the gradient.
//   - hradius: horizontal radius.
//   - vradius: vertical radius.
//   - start: percentage >= 0 that defines the start of the gradient around
//     center.
//   - end: percentage >= 0 that defines the end of the gradient around center.
//   - colorStops: pointer to an array of GskColorStop defining the gradient.
//     The offsets of all color stops must be increasing. The first stop's
//     offset must be >= 0 and the last stop's offset must be <= 1.
//
// The function returns the following values:
//
//   - repeatingRadialGradientNode: new GskRenderNode.
func NewRepeatingRadialGradientNode(bounds *graphene.Rect, center *graphene.Point, hradius, vradius, start, end float32, colorStops []ColorStop) *RepeatingRadialGradientNode {
	var _arg1 *C.graphene_rect_t  // out
	var _arg2 *C.graphene_point_t // out
	var _arg3 C.float             // out
	var _arg4 C.float             // out
	var _arg5 C.float             // out
	var _arg6 C.float             // out
	var _arg7 *C.GskColorStop     // out
	var _arg8 C.gsize
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(bounds)))
	_arg2 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(center)))
	_arg3 = C.float(hradius)
	_arg4 = C.float(vradius)
	_arg5 = C.float(start)
	_arg6 = C.float(end)
	_arg8 = (C.gsize)(len(colorStops))
	_arg7 = (*C.GskColorStop)(C.calloc(C.size_t(len(colorStops)), C.size_t(C.sizeof_GskColorStop)))
	defer C.free(unsafe.Pointer(_arg7))
	{
		out := unsafe.Slice((*C.GskColorStop)(_arg7), len(colorStops))
		for i := range colorStops {
			out[i] = *(*C.GskColorStop)(gextras.StructNative(unsafe.Pointer((&colorStops[i]))))
		}
	}

	_cret = C.gsk_repeating_radial_gradient_node_new(_arg1, _arg2, _arg3, _arg4, _arg5, _arg6, _arg7, _arg8)
	runtime.KeepAlive(bounds)
	runtime.KeepAlive(center)
	runtime.KeepAlive(hradius)
	runtime.KeepAlive(vradius)
	runtime.KeepAlive(start)
	runtime.KeepAlive(end)
	runtime.KeepAlive(colorStops)

	var _repeatingRadialGradientNode *RepeatingRadialGradientNode // out

	_repeatingRadialGradientNode = wrapRepeatingRadialGradientNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _repeatingRadialGradientNode
}

// RoundedClipNode: render node applying a rounded rectangle clip to its single
// child.
type RoundedClipNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*RoundedClipNode)(nil)
)

func wrapRoundedClipNode(obj *coreglib.Object) *RoundedClipNode {
	return &RoundedClipNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalRoundedClipNode(p uintptr) (interface{}, error) {
	return wrapRoundedClipNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewRoundedClipNode creates a GskRenderNode that will clip the child to the
// area given by clip.
//
// The function takes the following parameters:
//
//   - child: node to draw.
//   - clip to apply.
//
// The function returns the following values:
//
//   - roundedClipNode: new GskRenderNode.
func NewRoundedClipNode(child RenderNoder, clip *RoundedRect) *RoundedClipNode {
	var _arg1 *C.GskRenderNode  // out
	var _arg2 *C.GskRoundedRect // out
	var _cret *C.GskRenderNode  // in

	_arg1 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(child).Native()))
	_arg2 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(clip)))

	_cret = C.gsk_rounded_clip_node_new(_arg1, _arg2)
	runtime.KeepAlive(child)
	runtime.KeepAlive(clip)

	var _roundedClipNode *RoundedClipNode // out

	_roundedClipNode = wrapRoundedClipNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _roundedClipNode
}

// Child gets the child node that is getting clipped by the given node.
//
// The function returns the following values:
//
//   - renderNode: child that is getting clipped.
func (node *RoundedClipNode) Child() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_rounded_clip_node_get_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// Clip retrieves the rounded rectangle used to clip the contents of the node.
//
// The function returns the following values:
//
//   - roundedRect: rounded rectangle.
func (node *RoundedClipNode) Clip() *RoundedRect {
	var _arg0 *C.GskRenderNode  // out
	var _cret *C.GskRoundedRect // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_rounded_clip_node_get_clip(_arg0)
	runtime.KeepAlive(node)

	var _roundedRect *RoundedRect // out

	_roundedRect = (*RoundedRect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _roundedRect
}

// ShadowNode: render node drawing one or more shadows behind its single child
// node.
type ShadowNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*ShadowNode)(nil)
)

func wrapShadowNode(obj *coreglib.Object) *ShadowNode {
	return &ShadowNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalShadowNode(p uintptr) (interface{}, error) {
	return wrapShadowNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewShadowNode creates a GskRenderNode that will draw a child with the given
// shadows below it.
//
// The function takes the following parameters:
//
//   - child: node to draw.
//   - shadows to apply.
//
// The function returns the following values:
//
//   - shadowNode: new GskRenderNode.
func NewShadowNode(child RenderNoder, shadows []Shadow) *ShadowNode {
	var _arg1 *C.GskRenderNode // out
	var _arg2 *C.GskShadow     // out
	var _arg3 C.gsize
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(child).Native()))
	_arg3 = (C.gsize)(len(shadows))
	_arg2 = (*C.GskShadow)(C.calloc(C.size_t(len(shadows)), C.size_t(C.sizeof_GskShadow)))
	defer C.free(unsafe.Pointer(_arg2))
	{
		out := unsafe.Slice((*C.GskShadow)(_arg2), len(shadows))
		for i := range shadows {
			out[i] = *(*C.GskShadow)(gextras.StructNative(unsafe.Pointer((&shadows[i]))))
		}
	}

	_cret = C.gsk_shadow_node_new(_arg1, _arg2, _arg3)
	runtime.KeepAlive(child)
	runtime.KeepAlive(shadows)

	var _shadowNode *ShadowNode // out

	_shadowNode = wrapShadowNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _shadowNode
}

// Child retrieves the child GskRenderNode of the shadow node.
//
// The function returns the following values:
//
//   - renderNode: child render node.
func (node *ShadowNode) Child() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_shadow_node_get_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// NShadows retrieves the number of shadows in the node.
//
// The function returns the following values:
//
//   - gsize: number of shadows.
func (node *ShadowNode) NShadows() uint {
	var _arg0 *C.GskRenderNode // out
	var _cret C.gsize          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_shadow_node_get_n_shadows(_arg0)
	runtime.KeepAlive(node)

	var _gsize uint // out

	_gsize = uint(_cret)

	return _gsize
}

// Shadow retrieves the shadow data at the given index i.
//
// The function takes the following parameters:
//
//   - i: given index.
//
// The function returns the following values:
//
//   - shadow data.
func (node *ShadowNode) Shadow(i uint) *Shadow {
	var _arg0 *C.GskRenderNode // out
	var _arg1 C.gsize          // out
	var _cret *C.GskShadow     // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))
	_arg1 = C.gsize(i)

	_cret = C.gsk_shadow_node_get_shadow(_arg0, _arg1)
	runtime.KeepAlive(node)
	runtime.KeepAlive(i)

	var _shadow *Shadow // out

	_shadow = (*Shadow)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _shadow
}

// StrokeNode: render node that will fill the area determined by stroking the
// the given gsk.Path using the gsk.Stroke attributes.
type StrokeNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*StrokeNode)(nil)
)

func wrapStrokeNode(obj *coreglib.Object) *StrokeNode {
	return &StrokeNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalStrokeNode(p uintptr) (interface{}, error) {
	return wrapStrokeNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewStrokeNode creates a RenderNode that will fill the outline generated by
// stroking the given path using the attributes defined in stroke.
//
// The area is filled with child.
//
// The function takes the following parameters:
//
//   - child: node to stroke the area with.
//   - path describing the area to stroke.
//   - stroke attributes to use.
//
// The function returns the following values:
//
//   - strokeNode: new RenderNode.
func NewStrokeNode(child RenderNoder, path *Path, stroke *Stroke) *StrokeNode {
	var _arg1 *C.GskRenderNode // out
	var _arg2 *C.GskPath       // out
	var _arg3 *C.GskStroke     // out
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(child).Native()))
	_arg2 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(path)))
	_arg3 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(stroke)))

	_cret = C.gsk_stroke_node_new(_arg1, _arg2, _arg3)
	runtime.KeepAlive(child)
	runtime.KeepAlive(path)
	runtime.KeepAlive(stroke)

	var _strokeNode *StrokeNode // out

	_strokeNode = wrapStrokeNode(coreglib.Take(unsafe.Pointer(_cret)))

	return _strokeNode
}

// Child gets the child node that is getting drawn by the given node.
//
// The function returns the following values:
//
//   - renderNode: child that is getting drawn.
func (node *StrokeNode) Child() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_stroke_node_get_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// Path retrieves the path that will be stroked with the contents of the node.
//
// The function returns the following values:
//
//   - path: Path.
func (node *StrokeNode) Path() *Path {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskPath       // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_stroke_node_get_path(_arg0)
	runtime.KeepAlive(node)

	var _path *Path // out

	_path = (*Path)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	C.gsk_path_ref(_cret)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_path)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_path_unref((*C.GskPath)(intern.C))
		},
	)

	return _path
}

// Stroke retrieves the stroke attributes used in this node.
//
// The function returns the following values:
//
//   - stroke: Stroke.
func (node *StrokeNode) Stroke() *Stroke {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskStroke     // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_stroke_node_get_stroke(_arg0)
	runtime.KeepAlive(node)

	var _stroke *Stroke // out

	_stroke = (*Stroke)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _stroke
}

// SubsurfaceNode: render node that potentially diverts a part of the scene
// graph to a subsurface.
type SubsurfaceNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*SubsurfaceNode)(nil)
)

func wrapSubsurfaceNode(obj *coreglib.Object) *SubsurfaceNode {
	return &SubsurfaceNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalSubsurfaceNode(p uintptr) (interface{}, error) {
	return wrapSubsurfaceNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// Child gets the child node that is getting drawn by the given node.
//
// The function returns the following values:
//
//   - renderNode: child GskRenderNode.
func (node *SubsurfaceNode) Child() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_subsurface_node_get_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// TextNode: render node drawing a set of glyphs.
type TextNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*TextNode)(nil)
)

func wrapTextNode(obj *coreglib.Object) *TextNode {
	return &TextNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalTextNode(p uintptr) (interface{}, error) {
	return wrapTextNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewTextNode creates a render node that renders the given glyphs.
//
// Note that color may not be used if the font contains color glyphs.
//
// The function takes the following parameters:
//
//   - font: PangoFont containing the glyphs.
//   - glyphs: PangoGlyphString to render.
//   - color: foreground color to render with.
//   - offset of the baseline.
//
// The function returns the following values:
//
//   - textNode (optional): new GskRenderNode.
func NewTextNode(font pango.Fonter, glyphs *pango.GlyphString, color *gdk.RGBA, offset *graphene.Point) *TextNode {
	var _arg1 *C.PangoFont        // out
	var _arg2 *C.PangoGlyphString // out
	var _arg3 *C.GdkRGBA          // out
	var _arg4 *C.graphene_point_t // out
	var _cret *C.GskRenderNode    // in

	_arg1 = (*C.PangoFont)(unsafe.Pointer(coreglib.InternObject(font).Native()))
	_arg2 = (*C.PangoGlyphString)(gextras.StructNative(unsafe.Pointer(glyphs)))
	_arg3 = (*C.GdkRGBA)(gextras.StructNative(unsafe.Pointer(color)))
	_arg4 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(offset)))

	_cret = C.gsk_text_node_new(_arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(font)
	runtime.KeepAlive(glyphs)
	runtime.KeepAlive(color)
	runtime.KeepAlive(offset)

	var _textNode *TextNode // out

	if _cret != nil {
		_textNode = wrapTextNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))
	}

	return _textNode
}

// Color retrieves the color used by the text node.
//
// The function returns the following values:
//
//   - rgbA: text color.
func (node *TextNode) Color() *gdk.RGBA {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GdkRGBA       // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_text_node_get_color(_arg0)
	runtime.KeepAlive(node)

	var _rgbA *gdk.RGBA // out

	_rgbA = (*gdk.RGBA)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _rgbA
}

// Font returns the font used by the text node.
//
// The function returns the following values:
//
//   - font: font.
func (node *TextNode) Font() pango.Fonter {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.PangoFont     // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_text_node_get_font(_arg0)
	runtime.KeepAlive(node)

	var _font pango.Fonter // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type pango.Fonter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(pango.Fonter)
			return ok
		})
		rv, ok := casted.(pango.Fonter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching pango.Fonter")
		}
		_font = rv
	}

	return _font
}

// Glyphs retrieves the glyph information in the node.
//
// The function returns the following values:
//
//   - glyphInfos: glyph information.
func (node *TextNode) Glyphs() []pango.GlyphInfo {
	var _arg0 *C.GskRenderNode  // out
	var _cret *C.PangoGlyphInfo // in
	var _arg1 C.guint           // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_text_node_get_glyphs(_arg0, &_arg1)
	runtime.KeepAlive(node)

	var _glyphInfos []pango.GlyphInfo // out

	{
		src := unsafe.Slice((*C.PangoGlyphInfo)(_cret), _arg1)
		_glyphInfos = make([]pango.GlyphInfo, _arg1)
		for i := 0; i < int(_arg1); i++ {
			_glyphInfos[i] = *(*pango.GlyphInfo)(gextras.NewStructNative(unsafe.Pointer((&src[i]))))
		}
	}

	return _glyphInfos
}

// NumGlyphs retrieves the number of glyphs in the text node.
//
// The function returns the following values:
//
//   - guint: number of glyphs.
func (node *TextNode) NumGlyphs() uint {
	var _arg0 *C.GskRenderNode // out
	var _cret C.guint          // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_text_node_get_num_glyphs(_arg0)
	runtime.KeepAlive(node)

	var _guint uint // out

	_guint = uint(_cret)

	return _guint
}

// Offset retrieves the offset applied to the text.
//
// The function returns the following values:
//
//   - point with the horizontal and vertical offsets.
func (node *TextNode) Offset() *graphene.Point {
	var _arg0 *C.GskRenderNode    // out
	var _cret *C.graphene_point_t // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_text_node_get_offset(_arg0)
	runtime.KeepAlive(node)

	var _point *graphene.Point // out

	_point = (*graphene.Point)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _point
}

// HasColorGlyphs checks whether the text node has color glyphs.
//
// The function returns the following values:
//
//   - ok: TRUE if the text node has color glyphs.
func (node *TextNode) HasColorGlyphs() bool {
	var _arg0 *C.GskRenderNode // out
	var _cret C.gboolean       // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_text_node_has_color_glyphs(_arg0)
	runtime.KeepAlive(node)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// TextureNode: render node for a GdkTexture.
type TextureNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*TextureNode)(nil)
)

func wrapTextureNode(obj *coreglib.Object) *TextureNode {
	return &TextureNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalTextureNode(p uintptr) (interface{}, error) {
	return wrapTextureNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewTextureNode creates a GskRenderNode that will render the given texture
// into the area given by bounds.
//
// Note that GSK applies linear filtering when textures are scaled and
// transformed. See gsk.TextureScaleNode for a way to influence filtering.
//
// The function takes the following parameters:
//
//   - texture: GdkTexture.
//   - bounds: rectangle to render the texture into.
//
// The function returns the following values:
//
//   - textureNode: new GskRenderNode.
func NewTextureNode(texture gdk.Texturer, bounds *graphene.Rect) *TextureNode {
	var _arg1 *C.GdkTexture      // out
	var _arg2 *C.graphene_rect_t // out
	var _cret *C.GskRenderNode   // in

	_arg1 = (*C.GdkTexture)(unsafe.Pointer(coreglib.InternObject(texture).Native()))
	_arg2 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(bounds)))

	_cret = C.gsk_texture_node_new(_arg1, _arg2)
	runtime.KeepAlive(texture)
	runtime.KeepAlive(bounds)

	var _textureNode *TextureNode // out

	_textureNode = wrapTextureNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _textureNode
}

// Texture retrieves the GdkTexture used when creating this GskRenderNode.
//
// The function returns the following values:
//
//   - texture: GdkTexture.
func (node *TextureNode) Texture() gdk.Texturer {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GdkTexture    // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_texture_node_get_texture(_arg0)
	runtime.KeepAlive(node)

	var _texture gdk.Texturer // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gdk.Texturer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Texturer)
			return ok
		})
		rv, ok := casted.(gdk.Texturer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Texturer")
		}
		_texture = rv
	}

	return _texture
}

// TextureScaleNode: render node for a GdkTexture.
type TextureScaleNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*TextureScaleNode)(nil)
)

func wrapTextureScaleNode(obj *coreglib.Object) *TextureScaleNode {
	return &TextureScaleNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalTextureScaleNode(p uintptr) (interface{}, error) {
	return wrapTextureScaleNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewTextureScaleNode creates a node that scales the texture to the size given
// by the bounds using the filter and then places it at the bounds' position.
//
// Note that further scaling and other transformations which are applied to the
// node will apply linear filtering to the resulting texture, as usual.
//
// This node is intended for tight control over scaling applied to a texture,
// such as in image editors and requires the application to be aware of the
// whole render tree as further transforms may be applied that conflict with the
// desired effect of this node.
//
// The function takes the following parameters:
//
//   - texture to scale.
//   - bounds: size of the texture to scale to.
//   - filter: how to scale the texture.
//
// The function returns the following values:
//
//   - textureScaleNode: new GskRenderNode.
func NewTextureScaleNode(texture gdk.Texturer, bounds *graphene.Rect, filter ScalingFilter) *TextureScaleNode {
	var _arg1 *C.GdkTexture      // out
	var _arg2 *C.graphene_rect_t // out
	var _arg3 C.GskScalingFilter // out
	var _cret *C.GskRenderNode   // in

	_arg1 = (*C.GdkTexture)(unsafe.Pointer(coreglib.InternObject(texture).Native()))
	_arg2 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(bounds)))
	_arg3 = C.GskScalingFilter(filter)

	_cret = C.gsk_texture_scale_node_new(_arg1, _arg2, _arg3)
	runtime.KeepAlive(texture)
	runtime.KeepAlive(bounds)
	runtime.KeepAlive(filter)

	var _textureScaleNode *TextureScaleNode // out

	_textureScaleNode = wrapTextureScaleNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _textureScaleNode
}

// Filter retrieves the GskScalingFilter used when creating this GskRenderNode.
//
// The function returns the following values:
//
//   - scalingFilter: GskScalingFilter.
func (node *TextureScaleNode) Filter() ScalingFilter {
	var _arg0 *C.GskRenderNode   // out
	var _cret C.GskScalingFilter // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_texture_scale_node_get_filter(_arg0)
	runtime.KeepAlive(node)

	var _scalingFilter ScalingFilter // out

	_scalingFilter = ScalingFilter(_cret)

	return _scalingFilter
}

// Texture retrieves the GdkTexture used when creating this GskRenderNode.
//
// The function returns the following values:
//
//   - texture: GdkTexture.
func (node *TextureScaleNode) Texture() gdk.Texturer {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GdkTexture    // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_texture_scale_node_get_texture(_arg0)
	runtime.KeepAlive(node)

	var _texture gdk.Texturer // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gdk.Texturer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Texturer)
			return ok
		})
		rv, ok := casted.(gdk.Texturer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Texturer")
		}
		_texture = rv
	}

	return _texture
}

// TransformNode: render node applying a GskTransform to its single child node.
type TransformNode struct {
	_ [0]func() // equal guard
	RenderNode
}

var (
	_ RenderNoder = (*TransformNode)(nil)
)

func wrapTransformNode(obj *coreglib.Object) *TransformNode {
	return &TransformNode{
		RenderNode: RenderNode{
			Object: obj,
		},
	}
}

func marshalTransformNode(p uintptr) (interface{}, error) {
	return wrapTransformNode(coreglib.ValueFromNative(unsafe.Pointer(p)).Object()), nil
}

// NewTransformNode creates a GskRenderNode that will transform the given child
// with the given transform.
//
// The function takes the following parameters:
//
//   - child: node to transform.
//   - transform to apply.
//
// The function returns the following values:
//
//   - transformNode: new GskRenderNode.
func NewTransformNode(child RenderNoder, transform *Transform) *TransformNode {
	var _arg1 *C.GskRenderNode // out
	var _arg2 *C.GskTransform  // out
	var _cret *C.GskRenderNode // in

	_arg1 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(child).Native()))
	_arg2 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(transform)))

	_cret = C.gsk_transform_node_new(_arg1, _arg2)
	runtime.KeepAlive(child)
	runtime.KeepAlive(transform)

	var _transformNode *TransformNode // out

	_transformNode = wrapTransformNode(coreglib.AssumeOwnership(unsafe.Pointer(_cret)))

	return _transformNode
}

// Child gets the child node that is getting transformed by the given node.
//
// The function returns the following values:
//
//   - renderNode: child that is getting transformed.
func (node *TransformNode) Child() RenderNoder {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskRenderNode // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_transform_node_get_child(_arg0)
	runtime.KeepAlive(node)

	var _renderNode RenderNoder // out

	{
		objptr := unsafe.Pointer(_cret)
		if objptr == nil {
			panic("object of type gsk.RenderNoder is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(RenderNoder)
			return ok
		})
		rv, ok := casted.(RenderNoder)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gsk.RenderNoder")
		}
		_renderNode = rv
	}

	return _renderNode
}

// Transform retrieves the GskTransform used by the node.
//
// The function returns the following values:
//
//   - transform: GskTransform.
func (node *TransformNode) Transform() *Transform {
	var _arg0 *C.GskRenderNode // out
	var _cret *C.GskTransform  // in

	_arg0 = (*C.GskRenderNode)(unsafe.Pointer(coreglib.InternObject(node).Native()))

	_cret = C.gsk_transform_node_get_transform(_arg0)
	runtime.KeepAlive(node)

	var _transform *Transform // out

	_transform = (*Transform)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	C.gsk_transform_ref(_cret)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_transform)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_transform_unref((*C.GskTransform)(intern.C))
		},
	)

	return _transform
}

// ColorStop: color stop in a gradient node.
//
// An instance of this type is always passed by reference.
type ColorStop struct {
	*colorStop
}

// colorStop is the struct that's finalized.
type colorStop struct {
	native *C.GskColorStop
}

// Offset: offset of the color stop.
func (c *ColorStop) Offset() float32 {
	valptr := &c.native.offset
	var _v float32 // out
	_v = float32(*valptr)
	return _v
}

// Color: color at the given offset.
func (c *ColorStop) Color() *gdk.RGBA {
	valptr := &c.native.color
	var _v *gdk.RGBA // out
	_v = (*gdk.RGBA)(gextras.NewStructNative(unsafe.Pointer(valptr)))
	return _v
}

// Offset: offset of the color stop.
func (c *ColorStop) SetOffset(offset float32) {
	valptr := &c.native.offset
	*valptr = C.float(offset)
}

// GLShaderClass: instance of this type is always passed by reference.
type GLShaderClass struct {
	*glShaderClass
}

// glShaderClass is the struct that's finalized.
type glShaderClass struct {
	native *C.GskGLShaderClass
}

// ParseLocation: location in a parse buffer.
//
// An instance of this type is always passed by reference.
type ParseLocation struct {
	*parseLocation
}

// parseLocation is the struct that's finalized.
type parseLocation struct {
	native *C.GskParseLocation
}

// NewParseLocation creates a new ParseLocation instance from the given
// fields. Beware that this function allocates on the Go heap; be careful
// when using it!
func NewParseLocation(bytes, chars, lines, lineBytes, lineChars uint) ParseLocation {
	var f0 C.gsize // out
	f0 = C.gsize(bytes)
	var f1 C.gsize // out
	f1 = C.gsize(chars)
	var f2 C.gsize // out
	f2 = C.gsize(lines)
	var f3 C.gsize // out
	f3 = C.gsize(lineBytes)
	var f4 C.gsize // out
	f4 = C.gsize(lineChars)

	v := C.GskParseLocation{
		bytes:      f0,
		chars:      f1,
		lines:      f2,
		line_bytes: f3,
		line_chars: f4,
	}

	return *(*ParseLocation)(gextras.NewStructNative(unsafe.Pointer(&v)))
}

// Bytes: offset of the location in the parse buffer, as bytes.
func (p *ParseLocation) Bytes() uint {
	valptr := &p.native.bytes
	var _v uint // out
	_v = uint(*valptr)
	return _v
}

// Chars: offset of the location in the parse buffer, as characters.
func (p *ParseLocation) Chars() uint {
	valptr := &p.native.chars
	var _v uint // out
	_v = uint(*valptr)
	return _v
}

// Lines: line of the location in the parse buffer.
func (p *ParseLocation) Lines() uint {
	valptr := &p.native.lines
	var _v uint // out
	_v = uint(*valptr)
	return _v
}

// LineBytes: position in the line, as bytes.
func (p *ParseLocation) LineBytes() uint {
	valptr := &p.native.line_bytes
	var _v uint // out
	_v = uint(*valptr)
	return _v
}

// LineChars: position in the line, as characters.
func (p *ParseLocation) LineChars() uint {
	valptr := &p.native.line_chars
	var _v uint // out
	_v = uint(*valptr)
	return _v
}

// Bytes: offset of the location in the parse buffer, as bytes.
func (p *ParseLocation) SetBytes(bytes uint) {
	valptr := &p.native.bytes
	*valptr = C.gsize(bytes)
}

// Chars: offset of the location in the parse buffer, as characters.
func (p *ParseLocation) SetChars(chars uint) {
	valptr := &p.native.chars
	*valptr = C.gsize(chars)
}

// Lines: line of the location in the parse buffer.
func (p *ParseLocation) SetLines(lines uint) {
	valptr := &p.native.lines
	*valptr = C.gsize(lines)
}

// LineBytes: position in the line, as bytes.
func (p *ParseLocation) SetLineBytes(lineBytes uint) {
	valptr := &p.native.line_bytes
	*valptr = C.gsize(lineBytes)
}

// LineChars: position in the line, as characters.
func (p *ParseLocation) SetLineChars(lineChars uint) {
	valptr := &p.native.line_chars
	*valptr = C.gsize(lineChars)
}

// Path: GskPath describes lines and curves that are more complex than simple
// rectangles.
//
// Paths can used for rendering (filling or stroking) and for animations (e.g.
// as trajectories).
//
// GskPath is an immutable, opaque, reference-counted struct. After creation,
// you cannot change the types it represents. Instead, new GskPath objects
// have to be created. The gsk.PathBuilder structure is meant to help in this
// endeavor.
//
// Conceptually, a path consists of zero or more contours (continuous, connected
// curves), each of which may or may not be closed. Contours are typically
// constructed from Bézier segments.
//
// <picture> <source srcset="path-dark.png" media="(prefers-color-scheme:
// dark)"> <img alt="A Path" src="path-light.png"> </picture>
//
// An instance of this type is always passed by reference.
type Path struct {
	*path
}

// path is the struct that's finalized.
type path struct {
	native *C.GskPath
}

func marshalPath(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Path{&path{(*C.GskPath)(b)}}, nil
}

// ForEach calls func for every operation of the path.
//
// Note that this may only approximate self, because paths can contain
// optimizations for various specialized contours, and depending on the flags,
// the path may be decomposed into simpler curves than the ones that it
// contained originally.
//
// This function serves two purposes:
//
// - When the flags allow everything, it provides access to the raw, unmodified
// data of the path.
//
// - When the flags disallow certain operations, it provides an approximation of
// the path using just the allowed operations.
//
// The function takes the following parameters:
//
//   - flags to pass to the foreach function. See gsk.PathForEachFlags for
//     details about flags.
//   - fn: function to call for operations.
//
// The function returns the following values:
//
//   - ok: FALSE if func returned FALSE, TRUE` otherwise.
func (self *Path) ForEach(flags PathForEachFlags, fn PathForEachFunc) bool {
	var _arg0 *C.GskPath            // out
	var _arg1 C.GskPathForeachFlags // out
	var _arg2 C.GskPathForeachFunc  // out
	var _arg3 C.gpointer
	var _cret C.gboolean // in

	_arg0 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.GskPathForeachFlags(flags)
	_arg2 = (*[0]byte)(C._gotk4_gsk4_PathForEachFunc)
	_arg3 = C.gpointer(gbox.Assign(fn))
	defer gbox.Delete(uintptr(_arg3))

	_cret = C.gsk_path_foreach(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(self)
	runtime.KeepAlive(flags)
	runtime.KeepAlive(fn)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// Bounds computes the bounds of the given path.
//
// The returned bounds may be larger than necessary, because this function aims
// to be fast, not accurate. The bounds are guaranteed to contain the path.
//
// It is possible that the returned rectangle has 0 width and/or height.
// This can happen when the path only describes a point or an axis-aligned line.
//
// If the path is empty, FALSE is returned and bounds are set to
// graphene_rect_zero(). This is different from the case where the path is a
// single point at the origin, where the bounds will also be set to the zero
// rectangle but TRUE will be returned.
//
// The function returns the following values:
//
//   - bounds of the given path.
//   - ok: TRUE if the path has bounds, FALSE if the path is known to be empty
//     and have no bounds.
func (self *Path) Bounds() (*graphene.Rect, bool) {
	var _arg0 *C.GskPath        // out
	var _arg1 C.graphene_rect_t // in
	var _cret C.gboolean        // in

	_arg0 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_path_get_bounds(_arg0, &_arg1)
	runtime.KeepAlive(self)

	var _bounds *graphene.Rect // out
	var _ok bool               // out

	_bounds = (*graphene.Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))
	if _cret != 0 {
		_ok = true
	}

	return _bounds, _ok
}

// ClosestPoint computes the closest point on the path to the given point and
// sets the result to it.
//
// If there is no point closer than the given threshold, FALSE is returned.
//
// The function takes the following parameters:
//
//   - point: point.
//   - threshold: maximum allowed distance.
//
// The function returns the following values:
//
//   - result: return location for the closest point.
//   - distance (optional): return location for the distance.
//   - ok: TRUE if point was set to the closest point on self, FALSE if no point
//     is closer than threshold.
func (self *Path) ClosestPoint(point *graphene.Point, threshold float32) (*PathPoint, float32, bool) {
	var _arg0 *C.GskPath          // out
	var _arg1 *C.graphene_point_t // out
	var _arg2 C.float             // out
	var _arg3 C.GskPathPoint      // in
	var _arg4 C.float             // in
	var _cret C.gboolean          // in

	_arg0 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(point)))
	_arg2 = C.float(threshold)

	_cret = C.gsk_path_get_closest_point(_arg0, _arg1, _arg2, &_arg3, &_arg4)
	runtime.KeepAlive(self)
	runtime.KeepAlive(point)
	runtime.KeepAlive(threshold)

	var _result *PathPoint // out
	var _distance float32  // out
	var _ok bool           // out

	_result = (*PathPoint)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))
	_distance = float32(_arg4)
	if _cret != 0 {
		_ok = true
	}

	return _result, _distance, _ok
}

// EndPoint gets the end point of the path.
//
// An empty path has no points, so FALSE is returned in this case.
//
// The function returns the following values:
//
//   - result: return location for point.
//   - ok: TRUE if result was filled.
func (self *Path) EndPoint() (*PathPoint, bool) {
	var _arg0 *C.GskPath     // out
	var _arg1 C.GskPathPoint // in
	var _cret C.gboolean     // in

	_arg0 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_path_get_end_point(_arg0, &_arg1)
	runtime.KeepAlive(self)

	var _result *PathPoint // out
	var _ok bool           // out

	_result = (*PathPoint)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))
	if _cret != 0 {
		_ok = true
	}

	return _result, _ok
}

// StartPoint gets the start point of the path.
//
// An empty path has no points, so FALSE is returned in this case.
//
// The function returns the following values:
//
//   - result: return location for point.
//   - ok: TRUE if result was filled.
func (self *Path) StartPoint() (*PathPoint, bool) {
	var _arg0 *C.GskPath     // out
	var _arg1 C.GskPathPoint // in
	var _cret C.gboolean     // in

	_arg0 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_path_get_start_point(_arg0, &_arg1)
	runtime.KeepAlive(self)

	var _result *PathPoint // out
	var _ok bool           // out

	_result = (*PathPoint)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))
	if _cret != 0 {
		_ok = true
	}

	return _result, _ok
}

// StrokeBounds computes the bounds for stroking the given path with the
// parameters in stroke.
//
// The returned bounds may be larger than necessary, because this function
// aims to be fast, not accurate. The bounds are guaranteed to contain the area
// affected by the stroke, including protrusions like miters.
//
// The function takes the following parameters:
//
//   - stroke parameters.
//
// The function returns the following values:
//
//   - bounds to fill in.
//   - ok: TRUE if the path has bounds, FALSE if the path is known to be empty
//     and have no bounds.
func (self *Path) StrokeBounds(stroke *Stroke) (*graphene.Rect, bool) {
	var _arg0 *C.GskPath        // out
	var _arg1 *C.GskStroke      // out
	var _arg2 C.graphene_rect_t // in
	var _cret C.gboolean        // in

	_arg0 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(stroke)))

	_cret = C.gsk_path_get_stroke_bounds(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(self)
	runtime.KeepAlive(stroke)

	var _bounds *graphene.Rect // out
	var _ok bool               // out

	_bounds = (*graphene.Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))
	if _cret != 0 {
		_ok = true
	}

	return _bounds, _ok
}

// InFill returns whether the given point is inside the area that would be
// affected if the path was filled according to fill_rule.
//
// Note that this function assumes that filling a contour implicitly closes it.
//
// The function takes the following parameters:
//
//   - point to test.
//   - fillRule: fill rule to follow.
//
// The function returns the following values:
//
//   - ok: TRUE if point is inside.
func (self *Path) InFill(point *graphene.Point, fillRule FillRule) bool {
	var _arg0 *C.GskPath          // out
	var _arg1 *C.graphene_point_t // out
	var _arg2 C.GskFillRule       // out
	var _cret C.gboolean          // in

	_arg0 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(point)))
	_arg2 = C.GskFillRule(fillRule)

	_cret = C.gsk_path_in_fill(_arg0, _arg1, _arg2)
	runtime.KeepAlive(self)
	runtime.KeepAlive(point)
	runtime.KeepAlive(fillRule)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// IsClosed returns if the path represents a single closed contour.
//
// The function returns the following values:
//
//   - ok: TRUE if the path is closed.
func (self *Path) IsClosed() bool {
	var _arg0 *C.GskPath // out
	var _cret C.gboolean // in

	_arg0 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_path_is_closed(_arg0)
	runtime.KeepAlive(self)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// IsEmpty checks if the path is empty, i.e. contains no lines or curves.
//
// The function returns the following values:
//
//   - ok: TRUE if the path is empty.
func (self *Path) IsEmpty() bool {
	var _arg0 *C.GskPath // out
	var _cret C.gboolean // in

	_arg0 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_path_is_empty(_arg0)
	runtime.KeepAlive(self)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// ToCairo appends the given path to the given cairo context for drawing with
// Cairo.
//
// This may cause some suboptimal conversions to be performed as Cairo does not
// support all features of GskPath.
//
// This function does not clear the existing Cairo path. Call cairo_new_path()
// if you want this.
//
// The function takes the following parameters:
//
//   - cr: cairo context.
func (self *Path) ToCairo(cr *cairo.Context) {
	var _arg0 *C.GskPath // out
	var _arg1 *C.cairo_t // out

	_arg0 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.cairo_t)(unsafe.Pointer(cr.Native()))

	C.gsk_path_to_cairo(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(cr)
}

// String converts the path into a string that is suitable for printing.
//
// You can use this function in a debugger to get a quick overview of the path.
//
// This is a wrapper around gsk.Path.Print(), see that function for details.
//
// The function returns the following values:
//
//   - utf8: new string for self.
func (self *Path) String() string {
	var _arg0 *C.GskPath // out
	var _cret *C.char    // in

	_arg0 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_path_to_string(_arg0)
	runtime.KeepAlive(self)

	var _utf8 string // out

	_utf8 = C.GoString((*C.gchar)(unsafe.Pointer(_cret)))
	defer C.free(unsafe.Pointer(_cret))

	return _utf8
}

// PathParse: this is a convenience function that constructs a GskPath from a
// serialized form.
//
// The string is expected to be in (a superset of) SVG path syntax
// (https://www.w3.org/TR/SVG11/paths.htmlData), as e.g. produced by
// gsk.Path.ToString().
//
// A high-level summary of the syntax:
//
// - M x y Move to (x, y)
//
// - L x y Add a line from the current point to (x, y)
//
// - Q x1 y1 x2 y2 Add a quadratic Bézier from the current point to (x2, y2),
// with control point (x1, y1)
//
// - C x1 y1 x2 y2 x3 y3 Add a cubic Bézier from the current point to (x3, y3),
// with control points (x1, y1) and (x2, y2)
//
// - Z Close the contour by drawing a line back to the start point
//
// - H x Add a horizontal line from the current point to the given x value
//
// - V y Add a vertical line from the current point to the given y value
//
// - T x2 y2 Add a quadratic Bézier, using the reflection of the previous
// segments' control point as control point
//
// - S x2 y2 x3 y3 Add a cubic Bézier, using the reflection of the previous
// segments' second control point as first control point
//
// - A rx ry r l s x y Add an elliptical arc from the current point to (x, y)
// with radii rx and ry. See the SVG documentation for how the other parameters
// influence the arc.
//
// - O x1 y1 x2 y2 w Add a rational quadratic Bézier from the current point to
// (x2, y2) with control point (x1, y1) and weight w.
//
// All the commands have lowercase variants that interpret coordinates relative
// to the current point.
//
// The O command is an extension that is not supported in SVG.
//
// The function takes the following parameters:
//
//   - str: string.
//
// The function returns the following values:
//
//   - path (optional): new GskPath, or NULL if string could not be parsed.
func PathParse(str string) *Path {
	var _arg1 *C.char    // out
	var _cret *C.GskPath // in

	_arg1 = (*C.char)(unsafe.Pointer(C.CString(str)))
	defer C.free(unsafe.Pointer(_arg1))

	_cret = C.gsk_path_parse(_arg1)
	runtime.KeepAlive(str)

	var _path *Path // out

	if _cret != nil {
		_path = (*Path)(gextras.NewStructNative(unsafe.Pointer(_cret)))
		runtime.SetFinalizer(
			gextras.StructIntern(unsafe.Pointer(_path)),
			func(intern *struct{ C unsafe.Pointer }) {
				C.gsk_path_unref((*C.GskPath)(intern.C))
			},
		)
	}

	return _path
}

// PathBuilder: GskPathBuilder is an auxiliary object for constructing GskPath
// objects.
//
// A path is constructed like this:
//
//	GskPath *
//	construct_path (void)
//	{
//	  GskPathBuilder *builder;
//
//	  builder = gsk_path_builder_new ();
//
//	  // add contours to the path here
//
//	  return gsk_path_builder_free_to_path (builder);
//
// Adding contours to the path can be done in two ways. The easiest
// option is to use the gsk_path_builder_add_* group of functions that
// add predefined contours to the current path, either common shapes
// like gsk.PathBuilder.AddCircle() or by adding from other paths like
// gsk.PathBuilder.AddPath().
//
// The gsk_path_builder_add_* methods always add complete contours, and do not
// use or modify the current point.
//
// The other option is to define each line and curve manually with the
// gsk_path_builder_*_to group of functions. You start with a call to
// gsk.PathBuilder.MoveTo() to set the starting point and then use multiple
// calls to any of the drawing functions to move the pen along the plane.
// Once you are done, you can call gsk.PathBuilder.Close() to close the path by
// connecting it back with a line to the starting point.
//
// This is similar to how paths are drawn in Cairo.
//
// Note that GskPathBuilder will reduce the degree of added Bézier curves as
// much as possible, to simplify rendering.
//
// An instance of this type is always passed by reference.
type PathBuilder struct {
	*pathBuilder
}

// pathBuilder is the struct that's finalized.
type pathBuilder struct {
	native *C.GskPathBuilder
}

func marshalPathBuilder(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &PathBuilder{&pathBuilder{(*C.GskPathBuilder)(b)}}, nil
}

// NewPathBuilder constructs a struct PathBuilder.
func NewPathBuilder() *PathBuilder {
	var _cret *C.GskPathBuilder // in

	_cret = C.gsk_path_builder_new()

	var _pathBuilder *PathBuilder // out

	_pathBuilder = (*PathBuilder)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_pathBuilder)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_path_builder_unref((*C.GskPathBuilder)(intern.C))
		},
	)

	return _pathBuilder
}

// AddCircle adds a circle with the center and radius.
//
// The path is going around the circle in clockwise direction.
//
// If radius is zero, the contour will be a closed point.
//
// The function takes the following parameters:
//
//   - center of the circle.
//   - radius of the circle.
func (self *PathBuilder) AddCircle(center *graphene.Point, radius float32) {
	var _arg0 *C.GskPathBuilder   // out
	var _arg1 *C.graphene_point_t // out
	var _arg2 C.float             // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(center)))
	_arg2 = C.float(radius)

	C.gsk_path_builder_add_circle(_arg0, _arg1, _arg2)
	runtime.KeepAlive(self)
	runtime.KeepAlive(center)
	runtime.KeepAlive(radius)
}

// AddLayout adds the outlines for the glyphs in layout to the builder.
//
// The function takes the following parameters:
//
//   - layout: pango layout to add.
func (self *PathBuilder) AddLayout(layout *pango.Layout) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 *C.PangoLayout    // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.PangoLayout)(unsafe.Pointer(coreglib.InternObject(layout).Native()))

	C.gsk_path_builder_add_layout(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(layout)
}

// AddPath appends all of path to the builder.
//
// The function takes the following parameters:
//
//   - path to append.
func (self *PathBuilder) AddPath(path *Path) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 *C.GskPath        // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(path)))

	C.gsk_path_builder_add_path(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(path)
}

// AddRect adds rect as a new contour to the path built by the builder.
//
// The path is going around the rectangle in clockwise direction.
//
// If the the width or height are 0, the path will be a closed horizontal or
// vertical line. If both are 0, it'll be a closed dot.
//
// The function takes the following parameters:
//
//   - rect: rectangle to create a path for.
func (self *PathBuilder) AddRect(rect *graphene.Rect) {
	var _arg0 *C.GskPathBuilder  // out
	var _arg1 *C.graphene_rect_t // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(rect)))

	C.gsk_path_builder_add_rect(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(rect)
}

// AddReversePath appends all of path to the builder, in reverse order.
//
// The function takes the following parameters:
//
//   - path to append.
func (self *PathBuilder) AddReversePath(path *Path) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 *C.GskPath        // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(path)))

	C.gsk_path_builder_add_reverse_path(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(path)
}

// AddRoundedRect adds rect as a new contour to the path built in self.
//
// The path is going around the rectangle in clockwise direction.
//
// The function takes the following parameters:
//
//   - rect: rounded rect.
func (self *PathBuilder) AddRoundedRect(rect *RoundedRect) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 *C.GskRoundedRect // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(rect)))

	C.gsk_path_builder_add_rounded_rect(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(rect)
}

// AddSegment adds to self the segment of path from start to end.
//
// If start is equal to or after end, the path will first add the segment from
// start to the end of the path, and then add the segment from the beginning to
// end. If the path is closed, these segments will be connected.
//
// Note that this method always adds a path with the given start point and end
// point. To add a closed path, use gsk.PathBuilder.AddPath().
//
// The function takes the following parameters:
//
//   - path: GskPath to take the segment to.
//   - start: point on path to start at.
//   - end: point on path to end at.
func (self *PathBuilder) AddSegment(path *Path, start *PathPoint, end *PathPoint) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 *C.GskPath        // out
	var _arg2 *C.GskPathPoint   // out
	var _arg3 *C.GskPathPoint   // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(path)))
	_arg2 = (*C.GskPathPoint)(gextras.StructNative(unsafe.Pointer(start)))
	_arg3 = (*C.GskPathPoint)(gextras.StructNative(unsafe.Pointer(end)))

	C.gsk_path_builder_add_segment(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(self)
	runtime.KeepAlive(path)
	runtime.KeepAlive(start)
	runtime.KeepAlive(end)
}

// ArcTo adds an elliptical arc from the current point to x2, y2 with x1,
// y1 determining the tangent directions.
//
// After this, x2, y2 will be the new current point.
//
// Note: Two points and their tangents do not determine a unique ellipse,
// so GSK just picks one. If you need more precise control, use
// gsk.PathBuilder.ConicTo() or gsk.PathBuilder.SVGArcTo().
//
// <picture> <source srcset="arc-dark.png" media="(prefers-color-scheme:
// dark)"> <img alt="Arc To" src="arc-light.png"> </picture>.
//
// The function takes the following parameters:
//
//   - x1: x coordinate of first control point.
//   - y1: y coordinate of first control point.
//   - x2: x coordinate of second control point.
//   - y2: y coordinate of second control point.
func (self *PathBuilder) ArcTo(x1 float32, y1 float32, x2 float32, y2 float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out
	var _arg3 C.float           // out
	var _arg4 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(x1)
	_arg2 = C.float(y1)
	_arg3 = C.float(x2)
	_arg4 = C.float(y2)

	C.gsk_path_builder_arc_to(_arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(self)
	runtime.KeepAlive(x1)
	runtime.KeepAlive(y1)
	runtime.KeepAlive(x2)
	runtime.KeepAlive(y2)
}

// Close ends the current contour with a line back to the start point.
//
// Note that this is different from calling gsk.PathBuilder.LineTo() with the
// start point in that the contour will be closed. A closed contour behaves
// differently from an open one. When stroking, its start and end point are
// considered connected, so they will be joined via the line join, and not ended
// with line caps.
func (self *PathBuilder) Close() {
	var _arg0 *C.GskPathBuilder // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))

	C.gsk_path_builder_close(_arg0)
	runtime.KeepAlive(self)
}

// ConicTo adds a conic curve
// (https://en.wikipedia.org/wiki/Non-uniform_rational_B-spline) from the
// current point to x2, y2 with the given weight and x1, y1 as the control
// point.
//
// The weight determines how strongly the curve is pulled towards the control
// point. A conic with weight 1 is identical to a quadratic Bézier curve with
// the same points.
//
// Conic curves can be used to draw ellipses and circles. They are also known as
// rational quadratic Bézier curves.
//
// After this, x2, y2 will be the new current point.
//
// <picture> <source srcset="conic-dark.png" media="(prefers-color-scheme:
// dark)"> <img alt="Conic To" src="conic-light.png"> </picture>.
//
// The function takes the following parameters:
//
//   - x1: x coordinate of control point.
//   - y1: y coordinate of control point.
//   - x2: x coordinate of the end of the curve.
//   - y2: y coordinate of the end of the curve.
//   - weight of the control point, must be greater than zero.
func (self *PathBuilder) ConicTo(x1 float32, y1 float32, x2 float32, y2 float32, weight float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out
	var _arg3 C.float           // out
	var _arg4 C.float           // out
	var _arg5 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(x1)
	_arg2 = C.float(y1)
	_arg3 = C.float(x2)
	_arg4 = C.float(y2)
	_arg5 = C.float(weight)

	C.gsk_path_builder_conic_to(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5)
	runtime.KeepAlive(self)
	runtime.KeepAlive(x1)
	runtime.KeepAlive(y1)
	runtime.KeepAlive(x2)
	runtime.KeepAlive(y2)
	runtime.KeepAlive(weight)
}

// CubicTo adds a cubic Bézier curve
// (https://en.wikipedia.org/wiki/BC3A9zier_curve) from the current point to x3,
// y3 with x1, y1 and x2, y2 as the control points.
//
// After this, x3, y3 will be the new current point.
//
// <picture> <source srcset="cubic-dark.png" media="(prefers-color-scheme:
// dark)"> <img alt="Cubic To" src="cubic-light.png"> </picture>.
//
// The function takes the following parameters:
//
//   - x1: x coordinate of first control point.
//   - y1: y coordinate of first control point.
//   - x2: x coordinate of second control point.
//   - y2: y coordinate of second control point.
//   - x3: x coordinate of the end of the curve.
//   - y3: y coordinate of the end of the curve.
func (self *PathBuilder) CubicTo(x1 float32, y1 float32, x2 float32, y2 float32, x3 float32, y3 float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out
	var _arg3 C.float           // out
	var _arg4 C.float           // out
	var _arg5 C.float           // out
	var _arg6 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(x1)
	_arg2 = C.float(y1)
	_arg3 = C.float(x2)
	_arg4 = C.float(y2)
	_arg5 = C.float(x3)
	_arg6 = C.float(y3)

	C.gsk_path_builder_cubic_to(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, _arg6)
	runtime.KeepAlive(self)
	runtime.KeepAlive(x1)
	runtime.KeepAlive(y1)
	runtime.KeepAlive(x2)
	runtime.KeepAlive(y2)
	runtime.KeepAlive(x3)
	runtime.KeepAlive(y3)
}

// CurrentPoint gets the current point.
//
// The current point is used for relative drawing commands and updated after
// every operation.
//
// When the builder is created, the default current point is set to 0, 0. Note
// that this is different from cairo, which starts out without a current point.
//
// The function returns the following values:
//
//   - point: current point.
func (self *PathBuilder) CurrentPoint() *graphene.Point {
	var _arg0 *C.GskPathBuilder   // out
	var _cret *C.graphene_point_t // in

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_path_builder_get_current_point(_arg0)
	runtime.KeepAlive(self)

	var _point *graphene.Point // out

	_point = (*graphene.Point)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _point
}

// HtmlArcTo implements arc-to according to the HTML Canvas spec.
//
// A convenience function that implements the HTML arc_to
// (https://html.spec.whatwg.org/multipage/canvas.html#dom-context-2d-arcto-dev)
// functionality.
//
// After this, the current point will be the point where the circle with the
// given radius touches the line from x1, y1 to x2, y2.
//
// The function takes the following parameters:
//
//   - x1: x coordinate of first control point.
//   - y1: y coordinate of first control point.
//   - x2: x coordinate of second control point.
//   - y2: y coordinate of second control point.
//   - radius radius of the circle.
func (self *PathBuilder) HtmlArcTo(x1 float32, y1 float32, x2 float32, y2 float32, radius float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out
	var _arg3 C.float           // out
	var _arg4 C.float           // out
	var _arg5 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(x1)
	_arg2 = C.float(y1)
	_arg3 = C.float(x2)
	_arg4 = C.float(y2)
	_arg5 = C.float(radius)

	C.gsk_path_builder_html_arc_to(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5)
	runtime.KeepAlive(self)
	runtime.KeepAlive(x1)
	runtime.KeepAlive(y1)
	runtime.KeepAlive(x2)
	runtime.KeepAlive(y2)
	runtime.KeepAlive(radius)
}

// LineTo draws a line from the current point to x, y and makes it the new
// current point.
//
// <picture> <source srcset="line-dark.png" media="(prefers-color-scheme:
// dark)"> <img alt="Line To" src="line-light.png"> </picture>.
//
// The function takes the following parameters:
//
//   - x coordinate.
//   - y coordinate.
func (self *PathBuilder) LineTo(x float32, y float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(x)
	_arg2 = C.float(y)

	C.gsk_path_builder_line_to(_arg0, _arg1, _arg2)
	runtime.KeepAlive(self)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
}

// MoveTo starts a new contour by placing the pen at x, y.
//
// If this function is called twice in succession, the first call will result
// in a contour made up of a single point. The second call will start a new
// contour.
//
// The function takes the following parameters:
//
//   - x coordinate.
//   - y coordinate.
func (self *PathBuilder) MoveTo(x float32, y float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(x)
	_arg2 = C.float(y)

	C.gsk_path_builder_move_to(_arg0, _arg1, _arg2)
	runtime.KeepAlive(self)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
}

// QuadTo adds a quadratic Bézier curve
// (https://en.wikipedia.org/wiki/BC3A9zier_curve) from the current point to x2,
// y2 with x1, y1 as the control point.
//
// After this, x2, y2 will be the new current point.
//
// <picture> <source srcset="quad-dark.png" media="(prefers-color-scheme:
// dark)"> <img alt="Quad To" src="quad-light.png"> </picture>.
//
// The function takes the following parameters:
//
//   - x1: x coordinate of control point.
//   - y1: y coordinate of control point.
//   - x2: x coordinate of the end of the curve.
//   - y2: y coordinate of the end of the curve.
func (self *PathBuilder) QuadTo(x1 float32, y1 float32, x2 float32, y2 float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out
	var _arg3 C.float           // out
	var _arg4 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(x1)
	_arg2 = C.float(y1)
	_arg3 = C.float(x2)
	_arg4 = C.float(y2)

	C.gsk_path_builder_quad_to(_arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(self)
	runtime.KeepAlive(x1)
	runtime.KeepAlive(y1)
	runtime.KeepAlive(x2)
	runtime.KeepAlive(y2)
}

// RelArcTo adds an elliptical arc from the current point to x2, y2 with x1,
// y1 determining the tangent directions.
//
// All coordinates are given relative to the current point.
//
// This is the relative version of gsk.PathBuilder.ArcTo().
//
// The function takes the following parameters:
//
//   - x1: x coordinate of first control point.
//   - y1: y coordinate of first control point.
//   - x2: x coordinate of second control point.
//   - y2: y coordinate of second control point.
func (self *PathBuilder) RelArcTo(x1 float32, y1 float32, x2 float32, y2 float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out
	var _arg3 C.float           // out
	var _arg4 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(x1)
	_arg2 = C.float(y1)
	_arg3 = C.float(x2)
	_arg4 = C.float(y2)

	C.gsk_path_builder_rel_arc_to(_arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(self)
	runtime.KeepAlive(x1)
	runtime.KeepAlive(y1)
	runtime.KeepAlive(x2)
	runtime.KeepAlive(y2)
}

// RelConicTo adds a conic curve
// (https://en.wikipedia.org/wiki/Non-uniform_rational_B-spline) from the
// current point to x2, y2 with the given weight and x1, y1 as the control
// point.
//
// All coordinates are given relative to the current point.
//
// This is the relative version of gsk.PathBuilder.ConicTo().
//
// The function takes the following parameters:
//
//   - x1: x offset of control point.
//   - y1: y offset of control point.
//   - x2: x offset of the end of the curve.
//   - y2: y offset of the end of the curve.
//   - weight of the curve, must be greater than zero.
func (self *PathBuilder) RelConicTo(x1 float32, y1 float32, x2 float32, y2 float32, weight float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out
	var _arg3 C.float           // out
	var _arg4 C.float           // out
	var _arg5 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(x1)
	_arg2 = C.float(y1)
	_arg3 = C.float(x2)
	_arg4 = C.float(y2)
	_arg5 = C.float(weight)

	C.gsk_path_builder_rel_conic_to(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5)
	runtime.KeepAlive(self)
	runtime.KeepAlive(x1)
	runtime.KeepAlive(y1)
	runtime.KeepAlive(x2)
	runtime.KeepAlive(y2)
	runtime.KeepAlive(weight)
}

// RelCubicTo adds a cubic Bézier curve
// (https://en.wikipedia.org/wiki/BC3A9zier_curve) from the current point to x3,
// y3 with x1, y1 and x2, y2 as the control points.
//
// All coordinates are given relative to the current point.
//
// This is the relative version of gsk.PathBuilder.CubicTo().
//
// The function takes the following parameters:
//
//   - x1: x offset of first control point.
//   - y1: y offset of first control point.
//   - x2: x offset of second control point.
//   - y2: y offset of second control point.
//   - x3: x offset of the end of the curve.
//   - y3: y offset of the end of the curve.
func (self *PathBuilder) RelCubicTo(x1 float32, y1 float32, x2 float32, y2 float32, x3 float32, y3 float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out
	var _arg3 C.float           // out
	var _arg4 C.float           // out
	var _arg5 C.float           // out
	var _arg6 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(x1)
	_arg2 = C.float(y1)
	_arg3 = C.float(x2)
	_arg4 = C.float(y2)
	_arg5 = C.float(x3)
	_arg6 = C.float(y3)

	C.gsk_path_builder_rel_cubic_to(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, _arg6)
	runtime.KeepAlive(self)
	runtime.KeepAlive(x1)
	runtime.KeepAlive(y1)
	runtime.KeepAlive(x2)
	runtime.KeepAlive(y2)
	runtime.KeepAlive(x3)
	runtime.KeepAlive(y3)
}

// RelHtmlArcTo implements arc-to according to the HTML Canvas spec.
//
// All coordinates are given relative to the current point.
//
// This is the relative version of gsk.PathBuilder.HtmlArcTo().
//
// The function takes the following parameters:
//
//   - x1: x coordinate of first control point.
//   - y1: y coordinate of first control point.
//   - x2: x coordinate of second control point.
//   - y2: y coordinate of second control point.
//   - radius radius of the circle.
func (self *PathBuilder) RelHtmlArcTo(x1 float32, y1 float32, x2 float32, y2 float32, radius float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out
	var _arg3 C.float           // out
	var _arg4 C.float           // out
	var _arg5 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(x1)
	_arg2 = C.float(y1)
	_arg3 = C.float(x2)
	_arg4 = C.float(y2)
	_arg5 = C.float(radius)

	C.gsk_path_builder_rel_html_arc_to(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5)
	runtime.KeepAlive(self)
	runtime.KeepAlive(x1)
	runtime.KeepAlive(y1)
	runtime.KeepAlive(x2)
	runtime.KeepAlive(y2)
	runtime.KeepAlive(radius)
}

// RelLineTo draws a line from the current point to a point offset from it by x,
// y and makes it the new current point.
//
// This is the relative version of gsk.PathBuilder.LineTo().
//
// The function takes the following parameters:
//
//   - x offset.
//   - y offset.
func (self *PathBuilder) RelLineTo(x float32, y float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(x)
	_arg2 = C.float(y)

	C.gsk_path_builder_rel_line_to(_arg0, _arg1, _arg2)
	runtime.KeepAlive(self)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
}

// RelMoveTo starts a new contour by placing the pen at x, y relative to the
// current point.
//
// This is the relative version of gsk.PathBuilder.MoveTo().
//
// The function takes the following parameters:
//
//   - x offset.
//   - y offset.
func (self *PathBuilder) RelMoveTo(x float32, y float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(x)
	_arg2 = C.float(y)

	C.gsk_path_builder_rel_move_to(_arg0, _arg1, _arg2)
	runtime.KeepAlive(self)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
}

// RelQuadTo adds a quadratic Bézier curve
// (https://en.wikipedia.org/wiki/BC3A9zier_curve) from the current point to x2,
// y2 with x1, y1 the control point.
//
// All coordinates are given relative to the current point.
//
// This is the relative version of gsk.PathBuilder.QuadTo().
//
// The function takes the following parameters:
//
//   - x1: x offset of control point.
//   - y1: y offset of control point.
//   - x2: x offset of the end of the curve.
//   - y2: y offset of the end of the curve.
func (self *PathBuilder) RelQuadTo(x1 float32, y1 float32, x2 float32, y2 float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out
	var _arg3 C.float           // out
	var _arg4 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(x1)
	_arg2 = C.float(y1)
	_arg3 = C.float(x2)
	_arg4 = C.float(y2)

	C.gsk_path_builder_rel_quad_to(_arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(self)
	runtime.KeepAlive(x1)
	runtime.KeepAlive(y1)
	runtime.KeepAlive(x2)
	runtime.KeepAlive(y2)
}

// RelSVGArcTo implements arc-to according to the SVG spec.
//
// All coordinates are given relative to the current point.
//
// This is the relative version of gsk.PathBuilder.SVGArcTo().
//
// The function takes the following parameters:
//
//   - rx: x radius.
//   - ry: y radius.
//   - xAxisRotation: rotation of the ellipsis.
//   - largeArc: whether to add the large arc.
//   - positiveSweep: whether to sweep in the positive direction.
//   - x: x coordinate of the endpoint.
//   - y: y coordinate of the endpoint.
func (self *PathBuilder) RelSVGArcTo(rx float32, ry float32, xAxisRotation float32, largeArc bool, positiveSweep bool, x float32, y float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out
	var _arg3 C.float           // out
	var _arg4 C.gboolean        // out
	var _arg5 C.gboolean        // out
	var _arg6 C.float           // out
	var _arg7 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(rx)
	_arg2 = C.float(ry)
	_arg3 = C.float(xAxisRotation)
	if largeArc {
		_arg4 = C.TRUE
	}
	if positiveSweep {
		_arg5 = C.TRUE
	}
	_arg6 = C.float(x)
	_arg7 = C.float(y)

	C.gsk_path_builder_rel_svg_arc_to(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, _arg6, _arg7)
	runtime.KeepAlive(self)
	runtime.KeepAlive(rx)
	runtime.KeepAlive(ry)
	runtime.KeepAlive(xAxisRotation)
	runtime.KeepAlive(largeArc)
	runtime.KeepAlive(positiveSweep)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
}

// SVGArcTo implements arc-to according to the SVG spec.
//
// A convenience function that implements the SVG arc_to
// (https://www.w3.org/TR/SVG11/paths.htmlDataEllipticalArcCommands)
// functionality.
//
// After this, x, y will be the new current point.
//
// The function takes the following parameters:
//
//   - rx: x radius.
//   - ry: y radius.
//   - xAxisRotation: rotation of the ellipsis.
//   - largeArc: whether to add the large arc.
//   - positiveSweep: whether to sweep in the positive direction.
//   - x: x coordinate of the endpoint.
//   - y: y coordinate of the endpoint.
func (self *PathBuilder) SVGArcTo(rx float32, ry float32, xAxisRotation float32, largeArc bool, positiveSweep bool, x float32, y float32) {
	var _arg0 *C.GskPathBuilder // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out
	var _arg3 C.float           // out
	var _arg4 C.gboolean        // out
	var _arg5 C.gboolean        // out
	var _arg6 C.float           // out
	var _arg7 C.float           // out

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(rx)
	_arg2 = C.float(ry)
	_arg3 = C.float(xAxisRotation)
	if largeArc {
		_arg4 = C.TRUE
	}
	if positiveSweep {
		_arg5 = C.TRUE
	}
	_arg6 = C.float(x)
	_arg7 = C.float(y)

	C.gsk_path_builder_svg_arc_to(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, _arg6, _arg7)
	runtime.KeepAlive(self)
	runtime.KeepAlive(rx)
	runtime.KeepAlive(ry)
	runtime.KeepAlive(xAxisRotation)
	runtime.KeepAlive(largeArc)
	runtime.KeepAlive(positiveSweep)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
}

// ToPath creates a new GskPath from the given builder.
//
// The given GskPathBuilder is reset once this function returns; you cannot call
// this function multiple times on the same builder instance.
//
// This function is intended primarily for language bindings. C code should use
// gsk.PathBuilder.FreeToPath().
//
// The function returns the following values:
//
//   - path: newly created GskPath with all the contours added to the builder.
func (self *PathBuilder) ToPath() *Path {
	var _arg0 *C.GskPathBuilder // out
	var _cret *C.GskPath        // in

	_arg0 = (*C.GskPathBuilder)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_path_builder_to_path(_arg0)
	runtime.KeepAlive(self)

	var _path *Path // out

	_path = (*Path)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_path)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_path_unref((*C.GskPath)(intern.C))
		},
	)

	return _path
}

// PathMeasure: GskPathMeasure is an object that allows measurements on GskPaths
// such as determining the length of the path.
//
// Many measuring operations require sampling the path length at intermediate
// points. Therefore, a GskPathMeasure has a tolerance that determines what
// precision is required for such approximations.
//
// A GskPathMeasure struct is a reference counted struct and should be treated
// as opaque.
//
// An instance of this type is always passed by reference.
type PathMeasure struct {
	*pathMeasure
}

// pathMeasure is the struct that's finalized.
type pathMeasure struct {
	native *C.GskPathMeasure
}

func marshalPathMeasure(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &PathMeasure{&pathMeasure{(*C.GskPathMeasure)(b)}}, nil
}

// NewPathMeasure constructs a struct PathMeasure.
func NewPathMeasure(path *Path) *PathMeasure {
	var _arg1 *C.GskPath        // out
	var _cret *C.GskPathMeasure // in

	_arg1 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(path)))

	_cret = C.gsk_path_measure_new(_arg1)
	runtime.KeepAlive(path)

	var _pathMeasure *PathMeasure // out

	_pathMeasure = (*PathMeasure)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_pathMeasure)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_path_measure_unref((*C.GskPathMeasure)(intern.C))
		},
	)

	return _pathMeasure
}

// NewPathMeasureWithTolerance constructs a struct PathMeasure.
func NewPathMeasureWithTolerance(path *Path, tolerance float32) *PathMeasure {
	var _arg1 *C.GskPath        // out
	var _arg2 C.float           // out
	var _cret *C.GskPathMeasure // in

	_arg1 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(path)))
	_arg2 = C.float(tolerance)

	_cret = C.gsk_path_measure_new_with_tolerance(_arg1, _arg2)
	runtime.KeepAlive(path)
	runtime.KeepAlive(tolerance)

	var _pathMeasure *PathMeasure // out

	_pathMeasure = (*PathMeasure)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_pathMeasure)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_path_measure_unref((*C.GskPathMeasure)(intern.C))
		},
	)

	return _pathMeasure
}

// Length gets the length of the path being measured.
//
// The length is cached, so this function does not do any work.
//
// The function returns the following values:
//
//   - gfloat: length of the path measured by self.
func (self *PathMeasure) Length() float32 {
	var _arg0 *C.GskPathMeasure // out
	var _cret C.float           // in

	_arg0 = (*C.GskPathMeasure)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_path_measure_get_length(_arg0)
	runtime.KeepAlive(self)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Path returns the path that the measure was created for.
//
// The function returns the following values:
//
//   - path of self.
func (self *PathMeasure) Path() *Path {
	var _arg0 *C.GskPathMeasure // out
	var _cret *C.GskPath        // in

	_arg0 = (*C.GskPathMeasure)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_path_measure_get_path(_arg0)
	runtime.KeepAlive(self)

	var _path *Path // out

	_path = (*Path)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	C.gsk_path_ref(_cret)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_path)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_path_unref((*C.GskPath)(intern.C))
		},
	)

	return _path
}

// Point sets result to the point at the given distance into the path.
//
// An empty path has no points, so FALSE is returned in that case.
//
// The function takes the following parameters:
//
//   - distance: distance.
//
// The function returns the following values:
//
//   - result: return location for the result.
//   - ok: TRUE if result was set.
func (self *PathMeasure) Point(distance float32) (*PathPoint, bool) {
	var _arg0 *C.GskPathMeasure // out
	var _arg1 C.float           // out
	var _arg2 C.GskPathPoint    // in
	var _cret C.gboolean        // in

	_arg0 = (*C.GskPathMeasure)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(distance)

	_cret = C.gsk_path_measure_get_point(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(self)
	runtime.KeepAlive(distance)

	var _result *PathPoint // out
	var _ok bool           // out

	_result = (*PathPoint)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))
	if _cret != 0 {
		_ok = true
	}

	return _result, _ok
}

// Tolerance returns the tolerance that the measure was created with.
//
// The function returns the following values:
//
//   - gfloat: tolerance of self.
func (self *PathMeasure) Tolerance() float32 {
	var _arg0 *C.GskPathMeasure // out
	var _cret C.float           // in

	_arg0 = (*C.GskPathMeasure)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_path_measure_get_tolerance(_arg0)
	runtime.KeepAlive(self)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// PathPoint: GskPathPoint is an opaque type representing a point on a path.
//
// It can be queried for properties of the path at that point, such as its
// tangent or its curvature.
//
// To obtain a GskPathPoint, use gsk.Path.GetClosestPoint(),
// gsk.Path.GetStartPoint(), gsk.Path.GetEndPoint() or
// gsk.PathMeasure.GetPoint().
//
// Note that GskPathPoint structs are meant to be stack-allocated, and don't
// hold a reference to the path object they are obtained from. It is the callers
// responsibility to keep a reference to the path as long as the GskPathPoint is
// used.
//
// An instance of this type is always passed by reference.
type PathPoint struct {
	*pathPoint
}

// pathPoint is the struct that's finalized.
type pathPoint struct {
	native *C.GskPathPoint
}

func marshalPathPoint(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &PathPoint{&pathPoint{(*C.GskPathPoint)(b)}}, nil
}

// Compare returns whether point1 is before or after point2.
//
// The function takes the following parameters:
//
//   - point2: another GskPathPoint.
//
// The function returns the following values:
//
//   - gint: -1 if point1 is before point2, 1 if point1 is after point2,
//     0 if they are equal.
func (point1 *PathPoint) Compare(point2 *PathPoint) int {
	var _arg0 *C.GskPathPoint // out
	var _arg1 *C.GskPathPoint // out
	var _cret C.int           // in

	_arg0 = (*C.GskPathPoint)(gextras.StructNative(unsafe.Pointer(point1)))
	_arg1 = (*C.GskPathPoint)(gextras.StructNative(unsafe.Pointer(point2)))

	_cret = C.gsk_path_point_compare(_arg0, _arg1)
	runtime.KeepAlive(point1)
	runtime.KeepAlive(point2)

	var _gint int // out

	_gint = int(_cret)

	return _gint
}

func (point *PathPoint) Copy() *PathPoint {
	var _arg0 *C.GskPathPoint // out
	var _cret *C.GskPathPoint // in

	_arg0 = (*C.GskPathPoint)(gextras.StructNative(unsafe.Pointer(point)))

	_cret = C.gsk_path_point_copy(_arg0)
	runtime.KeepAlive(point)

	var _pathPoint *PathPoint // out

	_pathPoint = (*PathPoint)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_pathPoint)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_path_point_free((*C.GskPathPoint)(intern.C))
		},
	)

	return _pathPoint
}

// Equal returns whether the two path points refer to the same location on all
// paths.
//
// Note that the start- and endpoint of a closed contour will compare nonequal
// according to this definition. Use gsk.Path.IsClosed() to find out if the
// start- and endpoint of a concrete path refer to the same location.
//
// The function takes the following parameters:
//
//   - point2: another GskPathPoint.
//
// The function returns the following values:
//
//   - ok: TRUE if point1 and point2 are equal.
func (point1 *PathPoint) Equal(point2 *PathPoint) bool {
	var _arg0 *C.GskPathPoint // out
	var _arg1 *C.GskPathPoint // out
	var _cret C.gboolean      // in

	_arg0 = (*C.GskPathPoint)(gextras.StructNative(unsafe.Pointer(point1)))
	_arg1 = (*C.GskPathPoint)(gextras.StructNative(unsafe.Pointer(point2)))

	_cret = C.gsk_path_point_equal(_arg0, _arg1)
	runtime.KeepAlive(point1)
	runtime.KeepAlive(point2)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// Curvature calculates the curvature of the path at the point.
//
// Optionally, returns the center of the osculating circle as well. The
// curvature is the inverse of the radius of the osculating circle.
//
// Lines have a curvature of zero (indicating an osculating circle of infinite
// radius. In this case, the center is not modified.
//
// # Circles with a radius of zero have INFINITY as curvature
//
// Note that certain points on a path may not have a single curvature, such as
// sharp turns. At such points, there are two curvatures -- the (limit of) the
// curvature of the path going into the point, and the (limit of) the curvature
// of the path coming out of it. The direction argument lets you choose which
// one to get.
//
// <picture> <source srcset="curvature-dark.png" media="(prefers-color-scheme:
// dark)"> <img alt="Osculating circle" src="curvature-light.png"> </picture>.
//
// The function takes the following parameters:
//
//   - path that point is on.
//   - direction for which to return the curvature.
//
// The function returns the following values:
//
//   - center (optional): return location for the center of the osculating
//     circle.
//   - gfloat: curvature of the path at the given point.
func (point *PathPoint) Curvature(path *Path, direction PathDirection) (*graphene.Point, float32) {
	var _arg0 *C.GskPathPoint    // out
	var _arg1 *C.GskPath         // out
	var _arg2 C.GskPathDirection // out
	var _arg3 C.graphene_point_t // in
	var _cret C.float            // in

	_arg0 = (*C.GskPathPoint)(gextras.StructNative(unsafe.Pointer(point)))
	_arg1 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(path)))
	_arg2 = C.GskPathDirection(direction)

	_cret = C.gsk_path_point_get_curvature(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(point)
	runtime.KeepAlive(path)
	runtime.KeepAlive(direction)

	var _center *graphene.Point // out
	var _gfloat float32         // out

	_center = (*graphene.Point)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))
	_gfloat = float32(_cret)

	return _center, _gfloat
}

// Distance returns the distance from the beginning of the path to point.
//
// The function takes the following parameters:
//
//   - measure: GskPathMeasure for the path.
//
// The function returns the following values:
//
//   - gfloat: distance of point.
func (point *PathPoint) Distance(measure *PathMeasure) float32 {
	var _arg0 *C.GskPathPoint   // out
	var _arg1 *C.GskPathMeasure // out
	var _cret C.float           // in

	_arg0 = (*C.GskPathPoint)(gextras.StructNative(unsafe.Pointer(point)))
	_arg1 = (*C.GskPathMeasure)(gextras.StructNative(unsafe.Pointer(measure)))

	_cret = C.gsk_path_point_get_distance(_arg0, _arg1)
	runtime.KeepAlive(point)
	runtime.KeepAlive(measure)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Position gets the position of the point.
//
// The function takes the following parameters:
//
//   - path that point is on.
//
// The function returns the following values:
//
//   - position: return location for the coordinates of the point.
func (point *PathPoint) Position(path *Path) *graphene.Point {
	var _arg0 *C.GskPathPoint    // out
	var _arg1 *C.GskPath         // out
	var _arg2 C.graphene_point_t // in

	_arg0 = (*C.GskPathPoint)(gextras.StructNative(unsafe.Pointer(point)))
	_arg1 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(path)))

	C.gsk_path_point_get_position(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(point)
	runtime.KeepAlive(path)

	var _position *graphene.Point // out

	_position = (*graphene.Point)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _position
}

// Rotation gets the direction of the tangent at a given point.
//
// This is a convenience variant of gsk.PathPoint.GetTangent() that returns
// the angle between the tangent and the X axis. The angle can e.g. be used in
// gtk_snapshot_rotate() (../gtk4/method.Snapshot.rotate.html).
//
// The function takes the following parameters:
//
//   - path that point is on.
//   - direction for which to return the rotation.
//
// The function returns the following values:
//
//   - gfloat: angle between the tangent and the X axis, in degrees.
func (point *PathPoint) Rotation(path *Path, direction PathDirection) float32 {
	var _arg0 *C.GskPathPoint    // out
	var _arg1 *C.GskPath         // out
	var _arg2 C.GskPathDirection // out
	var _cret C.float            // in

	_arg0 = (*C.GskPathPoint)(gextras.StructNative(unsafe.Pointer(point)))
	_arg1 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(path)))
	_arg2 = C.GskPathDirection(direction)

	_cret = C.gsk_path_point_get_rotation(_arg0, _arg1, _arg2)
	runtime.KeepAlive(point)
	runtime.KeepAlive(path)
	runtime.KeepAlive(direction)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Tangent gets the tangent of the path at the point.
//
// Note that certain points on a path may not have a single tangent, such as
// sharp turns. At such points, there are two tangents -- the direction of the
// path going into the point, and the direction coming out of it. The direction
// argument lets you choose which one to get.
//
// If the path is just a single point (e.g. a circle with radius zero), then
// tangent is set to 0, 0.
//
// If you want to orient something in the direction of the path,
// gsk.PathPoint.GetRotation() may be more convenient to use.
//
// The function takes the following parameters:
//
//   - path that point is on.
//   - direction for which to return the tangent.
//
// The function returns the following values:
//
//   - tangent: return location for the tangent at the point.
func (point *PathPoint) Tangent(path *Path, direction PathDirection) *graphene.Vec2 {
	var _arg0 *C.GskPathPoint    // out
	var _arg1 *C.GskPath         // out
	var _arg2 C.GskPathDirection // out
	var _arg3 C.graphene_vec2_t  // in

	_arg0 = (*C.GskPathPoint)(gextras.StructNative(unsafe.Pointer(point)))
	_arg1 = (*C.GskPath)(gextras.StructNative(unsafe.Pointer(path)))
	_arg2 = C.GskPathDirection(direction)

	C.gsk_path_point_get_tangent(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(point)
	runtime.KeepAlive(path)
	runtime.KeepAlive(direction)

	var _tangent *graphene.Vec2 // out

	_tangent = (*graphene.Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _tangent
}

// RoundedRect: rectangular region with rounded corners.
//
// Application code should normalize rectangles using
// gsk.RoundedRect.Normalize(); this function will ensure that the bounds of the
// rectangle are normalized and ensure that the corner values are positive and
// the corners do not overlap.
//
// All functions taking a GskRoundedRect as an argument will internally operate
// on a normalized copy; all functions returning a GskRoundedRect will always
// return a normalized one.
//
// The algorithm used for normalizing corner sizes is described in the CSS
// specification (https://drafts.csswg.org/css-backgrounds-3/#border-radius).
//
// An instance of this type is always passed by reference.
type RoundedRect struct {
	*roundedRect
}

// roundedRect is the struct that's finalized.
type roundedRect struct {
	native *C.GskRoundedRect
}

// Bounds bounds of the rectangle.
func (r *RoundedRect) Bounds() *graphene.Rect {
	valptr := &r.native.bounds
	var _v *graphene.Rect // out
	_v = (*graphene.Rect)(gextras.NewStructNative(unsafe.Pointer(valptr)))
	return _v
}

// Corner: size of the 4 rounded corners.
func (r *RoundedRect) Corner() [4]graphene.Size {
	valptr := &r.native.corner
	var _v [4]graphene.Size // out
	{
		src := &*valptr
		for i := 0; i < 4; i++ {
			_v[i] = *(*graphene.Size)(gextras.NewStructNative(unsafe.Pointer((&src[i]))))
		}
	}
	return _v
}

// ContainsPoint checks if the given point is inside the rounded rectangle.
//
// The function takes the following parameters:
//
//   - point to check.
//
// The function returns the following values:
//
//   - ok: TRUE if the point is inside the rounded rectangle.
func (self *RoundedRect) ContainsPoint(point *graphene.Point) bool {
	var _arg0 *C.GskRoundedRect   // out
	var _arg1 *C.graphene_point_t // out
	var _cret C.gboolean          // in

	_arg0 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(point)))

	_cret = C.gsk_rounded_rect_contains_point(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(point)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// ContainsRect checks if the given rect is contained inside the rounded
// rectangle.
//
// The function takes the following parameters:
//
//   - rect: rectangle to check.
//
// The function returns the following values:
//
//   - ok: TRUE if the rect is fully contained inside the rounded rectangle.
func (self *RoundedRect) ContainsRect(rect *graphene.Rect) bool {
	var _arg0 *C.GskRoundedRect  // out
	var _arg1 *C.graphene_rect_t // out
	var _cret C.gboolean         // in

	_arg0 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(rect)))

	_cret = C.gsk_rounded_rect_contains_rect(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(rect)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// Init initializes the given GskRoundedRect with the given values.
//
// This function will implicitly normalize the GskRoundedRect before returning.
//
// The function takes the following parameters:
//
//   - bounds: graphene_rect_t describing the bounds.
//   - topLeft: rounding radius of the top left corner.
//   - topRight: rounding radius of the top right corner.
//   - bottomRight: rounding radius of the bottom right corner.
//   - bottomLeft: rounding radius of the bottom left corner.
//
// The function returns the following values:
//
//   - roundedRect: initialized rectangle.
func (self *RoundedRect) Init(bounds *graphene.Rect, topLeft *graphene.Size, topRight *graphene.Size, bottomRight *graphene.Size, bottomLeft *graphene.Size) *RoundedRect {
	var _arg0 *C.GskRoundedRect  // out
	var _arg1 *C.graphene_rect_t // out
	var _arg2 *C.graphene_size_t // out
	var _arg3 *C.graphene_size_t // out
	var _arg4 *C.graphene_size_t // out
	var _arg5 *C.graphene_size_t // out
	var _cret *C.GskRoundedRect  // in

	_arg0 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(bounds)))
	_arg2 = (*C.graphene_size_t)(gextras.StructNative(unsafe.Pointer(topLeft)))
	_arg3 = (*C.graphene_size_t)(gextras.StructNative(unsafe.Pointer(topRight)))
	_arg4 = (*C.graphene_size_t)(gextras.StructNative(unsafe.Pointer(bottomRight)))
	_arg5 = (*C.graphene_size_t)(gextras.StructNative(unsafe.Pointer(bottomLeft)))

	_cret = C.gsk_rounded_rect_init(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5)
	runtime.KeepAlive(self)
	runtime.KeepAlive(bounds)
	runtime.KeepAlive(topLeft)
	runtime.KeepAlive(topRight)
	runtime.KeepAlive(bottomRight)
	runtime.KeepAlive(bottomLeft)

	var _roundedRect *RoundedRect // out

	_roundedRect = (*RoundedRect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _roundedRect
}

// InitCopy initializes self using the given src rectangle.
//
// This function will not normalize the GskRoundedRect, so make sure the source
// is normalized.
//
// The function takes the following parameters:
//
//   - src: GskRoundedRect.
//
// The function returns the following values:
//
//   - roundedRect: initialized rectangle.
func (self *RoundedRect) InitCopy(src *RoundedRect) *RoundedRect {
	var _arg0 *C.GskRoundedRect // out
	var _arg1 *C.GskRoundedRect // out
	var _cret *C.GskRoundedRect // in

	_arg0 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.gsk_rounded_rect_init_copy(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(src)

	var _roundedRect *RoundedRect // out

	_roundedRect = (*RoundedRect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _roundedRect
}

// InitFromRect initializes self to the given bounds and sets the radius of all
// four corners to radius.
//
// The function takes the following parameters:
//
//   - bounds: graphene_rect_t.
//   - radius: border radius.
//
// The function returns the following values:
//
//   - roundedRect: initialized rectangle.
func (self *RoundedRect) InitFromRect(bounds *graphene.Rect, radius float32) *RoundedRect {
	var _arg0 *C.GskRoundedRect  // out
	var _arg1 *C.graphene_rect_t // out
	var _arg2 C.float            // out
	var _cret *C.GskRoundedRect  // in

	_arg0 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(bounds)))
	_arg2 = C.float(radius)

	_cret = C.gsk_rounded_rect_init_from_rect(_arg0, _arg1, _arg2)
	runtime.KeepAlive(self)
	runtime.KeepAlive(bounds)
	runtime.KeepAlive(radius)

	var _roundedRect *RoundedRect // out

	_roundedRect = (*RoundedRect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _roundedRect
}

// IntersectsRect checks if part of the given rect is contained inside the
// rounded rectangle.
//
// The function takes the following parameters:
//
//   - rect: rectangle to check.
//
// The function returns the following values:
//
//   - ok: TRUE if the rect intersects with the rounded rectangle.
func (self *RoundedRect) IntersectsRect(rect *graphene.Rect) bool {
	var _arg0 *C.GskRoundedRect  // out
	var _arg1 *C.graphene_rect_t // out
	var _cret C.gboolean         // in

	_arg0 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(rect)))

	_cret = C.gsk_rounded_rect_intersects_rect(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(rect)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// IsRectilinear checks if all corners of self are right angles and the
// rectangle covers all of its bounds.
//
// This information can be used to decide if gsk.ClipNode.New or
// gsk.RoundedClipNode.New should be called.
//
// The function returns the following values:
//
//   - ok: TRUE if the rectangle is rectilinear.
func (self *RoundedRect) IsRectilinear() bool {
	var _arg0 *C.GskRoundedRect // out
	var _cret C.gboolean        // in

	_arg0 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_rounded_rect_is_rectilinear(_arg0)
	runtime.KeepAlive(self)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// Normalize normalizes the passed rectangle.
//
// This function will ensure that the bounds of the rectangle are normalized and
// ensure that the corner values are positive and the corners do not overlap.
//
// The function returns the following values:
//
//   - roundedRect: normalized rectangle.
func (self *RoundedRect) Normalize() *RoundedRect {
	var _arg0 *C.GskRoundedRect // out
	var _cret *C.GskRoundedRect // in

	_arg0 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_rounded_rect_normalize(_arg0)
	runtime.KeepAlive(self)

	var _roundedRect *RoundedRect // out

	_roundedRect = (*RoundedRect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _roundedRect
}

// Offset offsets the bound's origin by dx and dy.
//
// The size and corners of the rectangle are unchanged.
//
// The function takes the following parameters:
//
//   - dx: horizontal offset.
//   - dy: vertical offset.
//
// The function returns the following values:
//
//   - roundedRect: offset rectangle.
func (self *RoundedRect) Offset(dx float32, dy float32) *RoundedRect {
	var _arg0 *C.GskRoundedRect // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out
	var _cret *C.GskRoundedRect // in

	_arg0 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(dx)
	_arg2 = C.float(dy)

	_cret = C.gsk_rounded_rect_offset(_arg0, _arg1, _arg2)
	runtime.KeepAlive(self)
	runtime.KeepAlive(dx)
	runtime.KeepAlive(dy)

	var _roundedRect *RoundedRect // out

	_roundedRect = (*RoundedRect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _roundedRect
}

// Shrink shrinks (or grows) the given rectangle by moving the 4 sides according
// to the offsets given.
//
// The corner radii will be changed in a way that tries to keep the center of
// the corner circle intact. This emulates CSS behavior.
//
// This function also works for growing rectangles if you pass negative values
// for the top, right, bottom or left.
//
// The function takes the following parameters:
//
//   - top: how far to move the top side downwards.
//   - right: how far to move the right side to the left.
//   - bottom: how far to move the bottom side upwards.
//   - left: how far to move the left side to the right.
//
// The function returns the following values:
//
//   - roundedRect: resized GskRoundedRect.
func (self *RoundedRect) Shrink(top float32, right float32, bottom float32, left float32) *RoundedRect {
	var _arg0 *C.GskRoundedRect // out
	var _arg1 C.float           // out
	var _arg2 C.float           // out
	var _arg3 C.float           // out
	var _arg4 C.float           // out
	var _cret *C.GskRoundedRect // in

	_arg0 = (*C.GskRoundedRect)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(top)
	_arg2 = C.float(right)
	_arg3 = C.float(bottom)
	_arg4 = C.float(left)

	_cret = C.gsk_rounded_rect_shrink(_arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(self)
	runtime.KeepAlive(top)
	runtime.KeepAlive(right)
	runtime.KeepAlive(bottom)
	runtime.KeepAlive(left)

	var _roundedRect *RoundedRect // out

	_roundedRect = (*RoundedRect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _roundedRect
}

// ShaderArgsBuilder: object to build the uniforms data for a GskGLShader.
//
// An instance of this type is always passed by reference.
type ShaderArgsBuilder struct {
	*shaderArgsBuilder
}

// shaderArgsBuilder is the struct that's finalized.
type shaderArgsBuilder struct {
	native *C.GskShaderArgsBuilder
}

func marshalShaderArgsBuilder(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &ShaderArgsBuilder{&shaderArgsBuilder{(*C.GskShaderArgsBuilder)(b)}}, nil
}

// NewShaderArgsBuilder constructs a struct ShaderArgsBuilder.
func NewShaderArgsBuilder(shader *GLShader, initialValues *glib.Bytes) *ShaderArgsBuilder {
	var _arg1 *C.GskGLShader          // out
	var _arg2 *C.GBytes               // out
	var _cret *C.GskShaderArgsBuilder // in

	_arg1 = (*C.GskGLShader)(unsafe.Pointer(coreglib.InternObject(shader).Native()))
	if initialValues != nil {
		_arg2 = (*C.GBytes)(gextras.StructNative(unsafe.Pointer(initialValues)))
	}

	_cret = C.gsk_shader_args_builder_new(_arg1, _arg2)
	runtime.KeepAlive(shader)
	runtime.KeepAlive(initialValues)

	var _shaderArgsBuilder *ShaderArgsBuilder // out

	_shaderArgsBuilder = (*ShaderArgsBuilder)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_shaderArgsBuilder)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_shader_args_builder_unref((*C.GskShaderArgsBuilder)(intern.C))
		},
	)

	return _shaderArgsBuilder
}

// SetBool sets the value of the uniform idx.
//
// The uniform must be of bool type.
//
// The function takes the following parameters:
//
//   - idx: index of the uniform.
//   - value to set the uniform to.
func (builder *ShaderArgsBuilder) SetBool(idx int, value bool) {
	var _arg0 *C.GskShaderArgsBuilder // out
	var _arg1 C.int                   // out
	var _arg2 C.gboolean              // out

	_arg0 = (*C.GskShaderArgsBuilder)(gextras.StructNative(unsafe.Pointer(builder)))
	_arg1 = C.int(idx)
	if value {
		_arg2 = C.TRUE
	}

	C.gsk_shader_args_builder_set_bool(_arg0, _arg1, _arg2)
	runtime.KeepAlive(builder)
	runtime.KeepAlive(idx)
	runtime.KeepAlive(value)
}

// SetFloat sets the value of the uniform idx.
//
// The uniform must be of float type.
//
// The function takes the following parameters:
//
//   - idx: index of the uniform.
//   - value to set the uniform to.
func (builder *ShaderArgsBuilder) SetFloat(idx int, value float32) {
	var _arg0 *C.GskShaderArgsBuilder // out
	var _arg1 C.int                   // out
	var _arg2 C.float                 // out

	_arg0 = (*C.GskShaderArgsBuilder)(gextras.StructNative(unsafe.Pointer(builder)))
	_arg1 = C.int(idx)
	_arg2 = C.float(value)

	C.gsk_shader_args_builder_set_float(_arg0, _arg1, _arg2)
	runtime.KeepAlive(builder)
	runtime.KeepAlive(idx)
	runtime.KeepAlive(value)
}

// SetInt sets the value of the uniform idx.
//
// The uniform must be of int type.
//
// The function takes the following parameters:
//
//   - idx: index of the uniform.
//   - value to set the uniform to.
func (builder *ShaderArgsBuilder) SetInt(idx int, value int32) {
	var _arg0 *C.GskShaderArgsBuilder // out
	var _arg1 C.int                   // out
	var _arg2 C.gint32                // out

	_arg0 = (*C.GskShaderArgsBuilder)(gextras.StructNative(unsafe.Pointer(builder)))
	_arg1 = C.int(idx)
	_arg2 = C.gint32(value)

	C.gsk_shader_args_builder_set_int(_arg0, _arg1, _arg2)
	runtime.KeepAlive(builder)
	runtime.KeepAlive(idx)
	runtime.KeepAlive(value)
}

// SetUint sets the value of the uniform idx.
//
// The uniform must be of uint type.
//
// The function takes the following parameters:
//
//   - idx: index of the uniform.
//   - value to set the uniform to.
func (builder *ShaderArgsBuilder) SetUint(idx int, value uint32) {
	var _arg0 *C.GskShaderArgsBuilder // out
	var _arg1 C.int                   // out
	var _arg2 C.guint32               // out

	_arg0 = (*C.GskShaderArgsBuilder)(gextras.StructNative(unsafe.Pointer(builder)))
	_arg1 = C.int(idx)
	_arg2 = C.guint32(value)

	C.gsk_shader_args_builder_set_uint(_arg0, _arg1, _arg2)
	runtime.KeepAlive(builder)
	runtime.KeepAlive(idx)
	runtime.KeepAlive(value)
}

// SetVec2 sets the value of the uniform idx.
//
// The uniform must be of vec2 type.
//
// The function takes the following parameters:
//
//   - idx: index of the uniform.
//   - value to set the uniform too.
func (builder *ShaderArgsBuilder) SetVec2(idx int, value *graphene.Vec2) {
	var _arg0 *C.GskShaderArgsBuilder // out
	var _arg1 C.int                   // out
	var _arg2 *C.graphene_vec2_t      // out

	_arg0 = (*C.GskShaderArgsBuilder)(gextras.StructNative(unsafe.Pointer(builder)))
	_arg1 = C.int(idx)
	_arg2 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(value)))

	C.gsk_shader_args_builder_set_vec2(_arg0, _arg1, _arg2)
	runtime.KeepAlive(builder)
	runtime.KeepAlive(idx)
	runtime.KeepAlive(value)
}

// SetVec3 sets the value of the uniform idx.
//
// The uniform must be of vec3 type.
//
// The function takes the following parameters:
//
//   - idx: index of the uniform.
//   - value to set the uniform too.
func (builder *ShaderArgsBuilder) SetVec3(idx int, value *graphene.Vec3) {
	var _arg0 *C.GskShaderArgsBuilder // out
	var _arg1 C.int                   // out
	var _arg2 *C.graphene_vec3_t      // out

	_arg0 = (*C.GskShaderArgsBuilder)(gextras.StructNative(unsafe.Pointer(builder)))
	_arg1 = C.int(idx)
	_arg2 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(value)))

	C.gsk_shader_args_builder_set_vec3(_arg0, _arg1, _arg2)
	runtime.KeepAlive(builder)
	runtime.KeepAlive(idx)
	runtime.KeepAlive(value)
}

// SetVec4 sets the value of the uniform idx.
//
// The uniform must be of vec4 type.
//
// The function takes the following parameters:
//
//   - idx: index of the uniform.
//   - value to set the uniform too.
func (builder *ShaderArgsBuilder) SetVec4(idx int, value *graphene.Vec4) {
	var _arg0 *C.GskShaderArgsBuilder // out
	var _arg1 C.int                   // out
	var _arg2 *C.graphene_vec4_t      // out

	_arg0 = (*C.GskShaderArgsBuilder)(gextras.StructNative(unsafe.Pointer(builder)))
	_arg1 = C.int(idx)
	_arg2 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(value)))

	C.gsk_shader_args_builder_set_vec4(_arg0, _arg1, _arg2)
	runtime.KeepAlive(builder)
	runtime.KeepAlive(idx)
	runtime.KeepAlive(value)
}

// ToArgs creates a new GBytes args from the current state of the given builder.
//
// Any uniforms of the shader that have not been explicitly set on the builder
// are zero-initialized.
//
// The given GskShaderArgsBuilder is reset once this function returns;
// you cannot call this function multiple times on the same builder instance.
//
// This function is intended primarily for bindings. C code should use
// gsk.ShaderArgsBuilder.FreeToArgs().
//
// The function returns the following values:
//
//   - bytes: newly allocated buffer with all the args added to builder.
func (builder *ShaderArgsBuilder) ToArgs() *glib.Bytes {
	var _arg0 *C.GskShaderArgsBuilder // out
	var _cret *C.GBytes               // in

	_arg0 = (*C.GskShaderArgsBuilder)(gextras.StructNative(unsafe.Pointer(builder)))

	_cret = C.gsk_shader_args_builder_to_args(_arg0)
	runtime.KeepAlive(builder)

	var _bytes *glib.Bytes // out

	_bytes = (*glib.Bytes)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_bytes)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.g_bytes_unref((*C.GBytes)(intern.C))
		},
	)

	return _bytes
}

// Shadow: shadow parameters in a shadow node.
//
// An instance of this type is always passed by reference.
type Shadow struct {
	*shadow
}

// shadow is the struct that's finalized.
type shadow struct {
	native *C.GskShadow
}

// Color: color of the shadow.
func (s *Shadow) Color() *gdk.RGBA {
	valptr := &s.native.color
	var _v *gdk.RGBA // out
	_v = (*gdk.RGBA)(gextras.NewStructNative(unsafe.Pointer(valptr)))
	return _v
}

// Dx: horizontal offset of the shadow.
func (s *Shadow) Dx() float32 {
	valptr := &s.native.dx
	var _v float32 // out
	_v = float32(*valptr)
	return _v
}

// Dy: vertical offset of the shadow.
func (s *Shadow) Dy() float32 {
	valptr := &s.native.dy
	var _v float32 // out
	_v = float32(*valptr)
	return _v
}

// Radius radius of the shadow.
func (s *Shadow) Radius() float32 {
	valptr := &s.native.radius
	var _v float32 // out
	_v = float32(*valptr)
	return _v
}

// Dx: horizontal offset of the shadow.
func (s *Shadow) SetDx(dx float32) {
	valptr := &s.native.dx
	*valptr = C.float(dx)
}

// Dy: vertical offset of the shadow.
func (s *Shadow) SetDy(dy float32) {
	valptr := &s.native.dy
	*valptr = C.float(dy)
}

// Radius radius of the shadow.
func (s *Shadow) SetRadius(radius float32) {
	valptr := &s.native.radius
	*valptr = C.float(radius)
}

// Stroke: GskStroke struct collects the parameters that influence the operation
// of stroking a path.
//
// An instance of this type is always passed by reference.
type Stroke struct {
	*stroke
}

// stroke is the struct that's finalized.
type stroke struct {
	native *C.GskStroke
}

func marshalStroke(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Stroke{&stroke{(*C.GskStroke)(b)}}, nil
}

// NewStroke constructs a struct Stroke.
func NewStroke(lineWidth float32) *Stroke {
	var _arg1 C.float      // out
	var _cret *C.GskStroke // in

	_arg1 = C.float(lineWidth)

	_cret = C.gsk_stroke_new(_arg1)
	runtime.KeepAlive(lineWidth)

	var _stroke *Stroke // out

	_stroke = (*Stroke)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_stroke)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_stroke_free((*C.GskStroke)(intern.C))
		},
	)

	return _stroke
}

// Copy creates a copy of the given other stroke.
//
// The function returns the following values:
//
//   - stroke: new GskStroke. Use gsk.Stroke.Free() to free it.
func (other *Stroke) Copy() *Stroke {
	var _arg0 *C.GskStroke // out
	var _cret *C.GskStroke // in

	_arg0 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(other)))

	_cret = C.gsk_stroke_copy(_arg0)
	runtime.KeepAlive(other)

	var _stroke *Stroke // out

	_stroke = (*Stroke)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_stroke)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_stroke_free((*C.GskStroke)(intern.C))
		},
	)

	return _stroke
}

// Dash gets the dash array in use or NULL if dashing is disabled.
//
// The function returns the following values:
//
//   - gfloats (optional): The dash array or NULL if the dash array is empty.
func (self *Stroke) Dash() []float32 {
	var _arg0 *C.GskStroke // out
	var _cret *C.float     // in
	var _arg1 C.gsize      // in

	_arg0 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_stroke_get_dash(_arg0, &_arg1)
	runtime.KeepAlive(self)

	var _gfloats []float32 // out

	if _cret != nil {
		_gfloats = make([]float32, _arg1)
		copy(_gfloats, unsafe.Slice((*float32)(unsafe.Pointer(_cret)), _arg1))
	}

	return _gfloats
}

// DashOffset returns the dash_offset of a GskStroke.
func (self *Stroke) DashOffset() float32 {
	var _arg0 *C.GskStroke // out
	var _cret C.float      // in

	_arg0 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_stroke_get_dash_offset(_arg0)
	runtime.KeepAlive(self)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// LineCap gets the line cap used.
//
// See gsk.LineCap for details.
//
// The function returns the following values:
//
//   - lineCap: line cap.
func (self *Stroke) LineCap() LineCap {
	var _arg0 *C.GskStroke // out
	var _cret C.GskLineCap // in

	_arg0 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_stroke_get_line_cap(_arg0)
	runtime.KeepAlive(self)

	var _lineCap LineCap // out

	_lineCap = LineCap(_cret)

	return _lineCap
}

// LineJoin gets the line join used.
//
// See gsk.LineJoin for details.
//
// The function returns the following values:
//
//   - lineJoin: line join.
func (self *Stroke) LineJoin() LineJoin {
	var _arg0 *C.GskStroke  // out
	var _cret C.GskLineJoin // in

	_arg0 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_stroke_get_line_join(_arg0)
	runtime.KeepAlive(self)

	var _lineJoin LineJoin // out

	_lineJoin = LineJoin(_cret)

	return _lineJoin
}

// LineWidth gets the line width used.
//
// The function returns the following values:
//
//   - gfloat: line width.
func (self *Stroke) LineWidth() float32 {
	var _arg0 *C.GskStroke // out
	var _cret C.float      // in

	_arg0 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_stroke_get_line_width(_arg0)
	runtime.KeepAlive(self)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// MiterLimit returns the miter limit of a GskStroke.
func (self *Stroke) MiterLimit() float32 {
	var _arg0 *C.GskStroke // out
	var _cret C.float      // in

	_arg0 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(self)))

	_cret = C.gsk_stroke_get_miter_limit(_arg0)
	runtime.KeepAlive(self)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// SetDash sets the dash pattern to use by this stroke.
//
// A dash pattern is specified by an array of alternating non-negative values.
// Each value provides the length of alternate "on" and "off" portions of the
// stroke.
//
// Each "on" segment will have caps applied as if the segment were a separate
// contour. In particular, it is valid to use an "on" length of 0 with
// GSK_LINE_CAP_ROUND or GSK_LINE_CAP_SQUARE to draw dots or squares along a
// path.
//
// If n_dash is 0, if all elements in dash are 0, or if there are negative
// values in dash, then dashing is disabled.
//
// If n_dash is 1, an alternating "on" and "off" pattern with the single dash
// length provided is assumed.
//
// If n_dash is uneven, the dash array will be used with the first element in
// dash defining an "on" or "off" in alternating passes through the array.
//
// You can specify a starting offset into the dash with
// gsk.Stroke.SetDashOffset().
//
// The function takes the following parameters:
//
//   - dash (optional): the array of dashes.
func (self *Stroke) SetDash(dash []float32) {
	var _arg0 *C.GskStroke // out
	var _arg1 *C.float     // out
	var _arg2 C.gsize

	_arg0 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(self)))
	_arg2 = (C.gsize)(len(dash))
	if len(dash) > 0 {
		_arg1 = (*C.float)(unsafe.Pointer(&dash[0]))
	}

	C.gsk_stroke_set_dash(_arg0, _arg1, _arg2)
	runtime.KeepAlive(self)
	runtime.KeepAlive(dash)
}

// SetDashOffset sets the offset into the dash pattern where dashing should
// begin.
//
// This is an offset into the length of the path, not an index into the array
// values of the dash array.
//
// See gsk.Stroke.SetDash() for more details on dashing.
//
// The function takes the following parameters:
//
//   - offset into the dash pattern.
func (self *Stroke) SetDashOffset(offset float32) {
	var _arg0 *C.GskStroke // out
	var _arg1 C.float      // out

	_arg0 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(offset)

	C.gsk_stroke_set_dash_offset(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(offset)
}

// SetLineCap sets the line cap to be used when stroking.
//
// See gsk.LineCap for details.
//
// The function takes the following parameters:
//
//   - lineCap: GskLineCap.
func (self *Stroke) SetLineCap(lineCap LineCap) {
	var _arg0 *C.GskStroke // out
	var _arg1 C.GskLineCap // out

	_arg0 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.GskLineCap(lineCap)

	C.gsk_stroke_set_line_cap(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(lineCap)
}

// SetLineJoin sets the line join to be used when stroking.
//
// See gsk.LineJoin for details.
//
// The function takes the following parameters:
//
//   - lineJoin: line join to use.
func (self *Stroke) SetLineJoin(lineJoin LineJoin) {
	var _arg0 *C.GskStroke  // out
	var _arg1 C.GskLineJoin // out

	_arg0 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.GskLineJoin(lineJoin)

	C.gsk_stroke_set_line_join(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(lineJoin)
}

// SetLineWidth sets the line width to be used when stroking.
//
// The line width must be > 0.
//
// The function takes the following parameters:
//
//   - lineWidth: width of the line in pixels.
func (self *Stroke) SetLineWidth(lineWidth float32) {
	var _arg0 *C.GskStroke // out
	var _arg1 C.float      // out

	_arg0 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(lineWidth)

	C.gsk_stroke_set_line_width(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(lineWidth)
}

// SetMiterLimit sets the limit for the distance from the corner where sharp
// turns of joins get cut off.
//
// The miter limit is in units of line width and must be non-negative.
//
// For joins of type GSK_LINE_JOIN_MITER that exceed the miter limit, the join
// gets rendered as if it was of type GSK_LINE_JOIN_BEVEL.
//
// The function takes the following parameters:
//
//   - limit: miter limit.
func (self *Stroke) SetMiterLimit(limit float32) {
	var _arg0 *C.GskStroke // out
	var _arg1 C.float      // out

	_arg0 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = C.float(limit)

	C.gsk_stroke_set_miter_limit(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(limit)
}

// ToCairo: helper function that sets the stroke parameters of cr from the
// values found in self.
//
// The function takes the following parameters:
//
//   - cr: cairo context to configure.
func (self *Stroke) ToCairo(cr *cairo.Context) {
	var _arg0 *C.GskStroke // out
	var _arg1 *C.cairo_t   // out

	_arg0 = (*C.GskStroke)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.cairo_t)(unsafe.Pointer(cr.Native()))

	C.gsk_stroke_to_cairo(_arg0, _arg1)
	runtime.KeepAlive(self)
	runtime.KeepAlive(cr)
}

// StrokeEqual checks if 2 strokes are identical.
//
// The function takes the following parameters:
//
//   - stroke1 (optional): first GskStroke.
//   - stroke2 (optional): second GskStroke.
//
// The function returns the following values:
//
//   - ok: TRUE if the 2 strokes are equal, FALSE otherwise.
func StrokeEqual(stroke1, stroke2 unsafe.Pointer) bool {
	var _arg1 C.gconstpointer // out
	var _arg2 C.gconstpointer // out
	var _cret C.gboolean      // in

	_arg1 = (C.gconstpointer)(unsafe.Pointer(stroke1))
	_arg2 = (C.gconstpointer)(unsafe.Pointer(stroke2))

	_cret = C.gsk_stroke_equal(_arg1, _arg2)
	runtime.KeepAlive(stroke1)
	runtime.KeepAlive(stroke2)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// Transform: GskTransform is an object to describe transform matrices.
//
// Unlike graphene_matrix_t, GskTransform retains the steps in how a transform
// was constructed, and allows inspecting them. It is modeled after the way CSS
// describes transforms.
//
// GskTransform objects are immutable and cannot be changed after creation.
// This means code can safely expose them as properties of objects without
// having to worry about others changing them.
//
// An instance of this type is always passed by reference.
type Transform struct {
	*transform
}

// transform is the struct that's finalized.
type transform struct {
	native *C.GskTransform
}

func marshalTransform(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Transform{&transform{(*C.GskTransform)(b)}}, nil
}

// NewTransform constructs a struct Transform.
func NewTransform() *Transform {
	var _cret *C.GskTransform // in

	_cret = C.gsk_transform_new()

	var _transform *Transform // out

	_transform = (*Transform)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_transform)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_transform_unref((*C.GskTransform)(intern.C))
		},
	)

	return _transform
}

// Equal checks two transforms for equality.
//
// The function takes the following parameters:
//
//   - second (optional) transform.
//
// The function returns the following values:
//
//   - ok: TRUE if the two transforms perform the same operation.
func (first *Transform) Equal(second *Transform) bool {
	var _arg0 *C.GskTransform // out
	var _arg1 *C.GskTransform // out
	var _cret C.gboolean      // in

	if first != nil {
		_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(first)))
	}
	if second != nil {
		_arg1 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(second)))
	}

	_cret = C.gsk_transform_equal(_arg0, _arg1)
	runtime.KeepAlive(first)
	runtime.KeepAlive(second)

	var _ok bool // out

	if _cret != 0 {
		_ok = true
	}

	return _ok
}

// Category returns the category this transform belongs to.
//
// The function returns the following values:
//
//   - transformCategory: category of the transform.
func (self *Transform) Category() TransformCategory {
	var _arg0 *C.GskTransform        // out
	var _cret C.GskTransformCategory // in

	if self != nil {
		_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(self)))
	}

	_cret = C.gsk_transform_get_category(_arg0)
	runtime.KeepAlive(self)

	var _transformCategory TransformCategory // out

	_transformCategory = TransformCategory(_cret)

	return _transformCategory
}

// Invert inverts the given transform.
//
// If self is not invertible, NULL is returned. Note that inverting NULL
// also returns NULL, which is the correct inverse of NULL. If you need to
// differentiate between those cases, you should check self is not NULL before
// calling this function.
//
// The function returns the following values:
//
//   - transform (optional): inverted transform.
func (self *Transform) Invert() *Transform {
	var _arg0 *C.GskTransform // out
	var _cret *C.GskTransform // in

	if self != nil {
		_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(self)))
	}

	_cret = C.gsk_transform_invert(_arg0)
	runtime.KeepAlive(self)

	var _transform *Transform // out

	if _cret != nil {
		_transform = (*Transform)(gextras.NewStructNative(unsafe.Pointer(_cret)))
		runtime.SetFinalizer(
			gextras.StructIntern(unsafe.Pointer(_transform)),
			func(intern *struct{ C unsafe.Pointer }) {
				C.gsk_transform_unref((*C.GskTransform)(intern.C))
			},
		)
	}

	return _transform
}

// Matrix multiplies next with the given matrix.
//
// The function takes the following parameters:
//
//   - matrix to multiply next with.
//
// The function returns the following values:
//
//   - transform: new transform.
func (next *Transform) Matrix(matrix *graphene.Matrix) *Transform {
	var _arg0 *C.GskTransform      // out
	var _arg1 *C.graphene_matrix_t // out
	var _cret *C.GskTransform      // in

	if next != nil {
		_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(next)))
	}
	_arg1 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(matrix)))

	_cret = C.gsk_transform_matrix(_arg0, _arg1)
	runtime.KeepAlive(next)
	runtime.KeepAlive(matrix)

	var _transform *Transform // out

	_transform = (*Transform)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_transform)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_transform_unref((*C.GskTransform)(intern.C))
		},
	)

	return _transform
}

// Perspective applies a perspective projection transform.
//
// This transform scales points in X and Y based on their Z value, scaling
// points with positive Z values away from the origin, and those with negative Z
// values towards the origin. Points on the z=0 plane are unchanged.
//
// The function takes the following parameters:
//
//   - depth: distance of the z=0 plane. Lower values give a more flattened
//     pyramid and therefore a more pronounced perspective effect.
//
// The function returns the following values:
//
//   - transform: new transform.
func (next *Transform) Perspective(depth float32) *Transform {
	var _arg0 *C.GskTransform // out
	var _arg1 C.float         // out
	var _cret *C.GskTransform // in

	if next != nil {
		_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(next)))
	}
	_arg1 = C.float(depth)

	_cret = C.gsk_transform_perspective(_arg0, _arg1)
	runtime.KeepAlive(next)
	runtime.KeepAlive(depth)

	var _transform *Transform // out

	_transform = (*Transform)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_transform)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_transform_unref((*C.GskTransform)(intern.C))
		},
	)

	return _transform
}

// Rotate rotates next angle degrees in 2D - or in 3D-speak, around the Z axis.
// The rotation happens around the origin point of (0, 0).
//
// The function takes the following parameters:
//
//   - angle: rotation angle, in degrees (clockwise).
//
// The function returns the following values:
//
//   - transform (optional): new transform.
func (next *Transform) Rotate(angle float32) *Transform {
	var _arg0 *C.GskTransform // out
	var _arg1 C.float         // out
	var _cret *C.GskTransform // in

	if next != nil {
		_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(next)))
	}
	_arg1 = C.float(angle)

	_cret = C.gsk_transform_rotate(_arg0, _arg1)
	runtime.KeepAlive(next)
	runtime.KeepAlive(angle)

	var _transform *Transform // out

	if _cret != nil {
		_transform = (*Transform)(gextras.NewStructNative(unsafe.Pointer(_cret)))
		runtime.SetFinalizer(
			gextras.StructIntern(unsafe.Pointer(_transform)),
			func(intern *struct{ C unsafe.Pointer }) {
				C.gsk_transform_unref((*C.GskTransform)(intern.C))
			},
		)
	}

	return _transform
}

// Rotate3D rotates next angle degrees around axis.
//
// For a rotation in 2D space, use gsk.Transform.Rotate().
//
// The function takes the following parameters:
//
//   - angle: rotation angle, in degrees (clockwise).
//   - axis: rotation axis.
//
// The function returns the following values:
//
//   - transform (optional): new transform.
func (next *Transform) Rotate3D(angle float32, axis *graphene.Vec3) *Transform {
	var _arg0 *C.GskTransform    // out
	var _arg1 C.float            // out
	var _arg2 *C.graphene_vec3_t // out
	var _cret *C.GskTransform    // in

	if next != nil {
		_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(next)))
	}
	_arg1 = C.float(angle)
	_arg2 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(axis)))

	_cret = C.gsk_transform_rotate_3d(_arg0, _arg1, _arg2)
	runtime.KeepAlive(next)
	runtime.KeepAlive(angle)
	runtime.KeepAlive(axis)

	var _transform *Transform // out

	if _cret != nil {
		_transform = (*Transform)(gextras.NewStructNative(unsafe.Pointer(_cret)))
		runtime.SetFinalizer(
			gextras.StructIntern(unsafe.Pointer(_transform)),
			func(intern *struct{ C unsafe.Pointer }) {
				C.gsk_transform_unref((*C.GskTransform)(intern.C))
			},
		)
	}

	return _transform
}

// Scale scales next in 2-dimensional space by the given factors.
//
// Use gsk.Transform.Scale3D() to scale in all 3 dimensions.
//
// The function takes the following parameters:
//
//   - factorX: scaling factor on the X axis.
//   - factorY: scaling factor on the Y axis.
//
// The function returns the following values:
//
//   - transform (optional): new transform.
func (next *Transform) Scale(factorX float32, factorY float32) *Transform {
	var _arg0 *C.GskTransform // out
	var _arg1 C.float         // out
	var _arg2 C.float         // out
	var _cret *C.GskTransform // in

	if next != nil {
		_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(next)))
	}
	_arg1 = C.float(factorX)
	_arg2 = C.float(factorY)

	_cret = C.gsk_transform_scale(_arg0, _arg1, _arg2)
	runtime.KeepAlive(next)
	runtime.KeepAlive(factorX)
	runtime.KeepAlive(factorY)

	var _transform *Transform // out

	if _cret != nil {
		_transform = (*Transform)(gextras.NewStructNative(unsafe.Pointer(_cret)))
		runtime.SetFinalizer(
			gextras.StructIntern(unsafe.Pointer(_transform)),
			func(intern *struct{ C unsafe.Pointer }) {
				C.gsk_transform_unref((*C.GskTransform)(intern.C))
			},
		)
	}

	return _transform
}

// Scale3D scales next by the given factors.
//
// The function takes the following parameters:
//
//   - factorX: scaling factor on the X axis.
//   - factorY: scaling factor on the Y axis.
//   - factorZ: scaling factor on the Z axis.
//
// The function returns the following values:
//
//   - transform (optional): new transform.
func (next *Transform) Scale3D(factorX float32, factorY float32, factorZ float32) *Transform {
	var _arg0 *C.GskTransform // out
	var _arg1 C.float         // out
	var _arg2 C.float         // out
	var _arg3 C.float         // out
	var _cret *C.GskTransform // in

	if next != nil {
		_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(next)))
	}
	_arg1 = C.float(factorX)
	_arg2 = C.float(factorY)
	_arg3 = C.float(factorZ)

	_cret = C.gsk_transform_scale_3d(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(next)
	runtime.KeepAlive(factorX)
	runtime.KeepAlive(factorY)
	runtime.KeepAlive(factorZ)

	var _transform *Transform // out

	if _cret != nil {
		_transform = (*Transform)(gextras.NewStructNative(unsafe.Pointer(_cret)))
		runtime.SetFinalizer(
			gextras.StructIntern(unsafe.Pointer(_transform)),
			func(intern *struct{ C unsafe.Pointer }) {
				C.gsk_transform_unref((*C.GskTransform)(intern.C))
			},
		)
	}

	return _transform
}

// Skew applies a skew transform.
//
// The function takes the following parameters:
//
//   - skewX: skew factor, in degrees, on the X axis.
//   - skewY: skew factor, in degrees, on the Y axis.
//
// The function returns the following values:
//
//   - transform (optional): new transform.
func (next *Transform) Skew(skewX float32, skewY float32) *Transform {
	var _arg0 *C.GskTransform // out
	var _arg1 C.float         // out
	var _arg2 C.float         // out
	var _cret *C.GskTransform // in

	if next != nil {
		_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(next)))
	}
	_arg1 = C.float(skewX)
	_arg2 = C.float(skewY)

	_cret = C.gsk_transform_skew(_arg0, _arg1, _arg2)
	runtime.KeepAlive(next)
	runtime.KeepAlive(skewX)
	runtime.KeepAlive(skewY)

	var _transform *Transform // out

	if _cret != nil {
		_transform = (*Transform)(gextras.NewStructNative(unsafe.Pointer(_cret)))
		runtime.SetFinalizer(
			gextras.StructIntern(unsafe.Pointer(_transform)),
			func(intern *struct{ C unsafe.Pointer }) {
				C.gsk_transform_unref((*C.GskTransform)(intern.C))
			},
		)
	}

	return _transform
}

// To2D converts a GskTransform to a 2D transformation matrix.
//
// self must be a 2D transformation. If you are not sure, use
// gsk_transform_get_category() >= GSK_TRANSFORM_CATEGORY_2D to check.
//
// The returned values have the following layout:
//
//	| xx yx |   |  a  b  0 |
//	| xy yy | = |  c  d  0 |
//	| dx dy |   | tx ty  1 |
//
// This function can be used to convert between a GskTransform and a matrix type
// from other 2D drawing libraries, in particular Cairo.
//
// The function returns the following values:
//
//   - outXx: return location for the xx member.
//   - outYx: return location for the yx member.
//   - outXy: return location for the xy member.
//   - outYy: return location for the yy member.
//   - outDx: return location for the x0 member.
//   - outDy: return location for the y0 member.
func (self *Transform) To2D() (outXx float32, outYx float32, outXy float32, outYy float32, outDx float32, outDy float32) {
	var _arg0 *C.GskTransform // out
	var _arg1 C.float         // in
	var _arg2 C.float         // in
	var _arg3 C.float         // in
	var _arg4 C.float         // in
	var _arg5 C.float         // in
	var _arg6 C.float         // in

	_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(self)))

	C.gsk_transform_to_2d(_arg0, &_arg1, &_arg2, &_arg3, &_arg4, &_arg5, &_arg6)
	runtime.KeepAlive(self)

	var _outXx float32 // out
	var _outYx float32 // out
	var _outXy float32 // out
	var _outYy float32 // out
	var _outDx float32 // out
	var _outDy float32 // out

	_outXx = float32(_arg1)
	_outYx = float32(_arg2)
	_outXy = float32(_arg3)
	_outYy = float32(_arg4)
	_outDx = float32(_arg5)
	_outDy = float32(_arg6)

	return _outXx, _outYx, _outXy, _outYy, _outDx, _outDy
}

// To2DComponents converts a GskTransform to 2D transformation factors.
//
// To recreate an equivalent transform from the factors returned by this
// function, use
//
//	gsk_transform_skew (
//	    gsk_transform_scale (
//	        gsk_transform_rotate (
//	            gsk_transform_translate (NULL, &GRAPHENE_POINT_T (dx, dy)),
//	            angle),
//	        scale_x, scale_y),
//	    skew_x, skew_y)
//
// self must be a 2D transformation. If you are not sure, use
//
//	gsk_transform_get_category() >= GSK_TRANSFORM_CATEGORY_2D
//
// to check.
//
// The function returns the following values:
//
//   - outSkewX: return location for the skew factor in the x direction.
//   - outSkewY: return location for the skew factor in the y direction.
//   - outScaleX: return location for the scale factor in the x direction.
//   - outScaleY: return location for the scale factor in the y direction.
//   - outAngle: return location for the rotation angle.
//   - outDx: return location for the translation in the x direction.
//   - outDy: return location for the translation in the y direction.
func (self *Transform) To2DComponents() (outSkewX float32, outSkewY float32, outScaleX float32, outScaleY float32, outAngle float32, outDx float32, outDy float32) {
	var _arg0 *C.GskTransform // out
	var _arg1 C.float         // in
	var _arg2 C.float         // in
	var _arg3 C.float         // in
	var _arg4 C.float         // in
	var _arg5 C.float         // in
	var _arg6 C.float         // in
	var _arg7 C.float         // in

	_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(self)))

	C.gsk_transform_to_2d_components(_arg0, &_arg1, &_arg2, &_arg3, &_arg4, &_arg5, &_arg6, &_arg7)
	runtime.KeepAlive(self)

	var _outSkewX float32  // out
	var _outSkewY float32  // out
	var _outScaleX float32 // out
	var _outScaleY float32 // out
	var _outAngle float32  // out
	var _outDx float32     // out
	var _outDy float32     // out

	_outSkewX = float32(_arg1)
	_outSkewY = float32(_arg2)
	_outScaleX = float32(_arg3)
	_outScaleY = float32(_arg4)
	_outAngle = float32(_arg5)
	_outDx = float32(_arg6)
	_outDy = float32(_arg7)

	return _outSkewX, _outSkewY, _outScaleX, _outScaleY, _outAngle, _outDx, _outDy
}

// ToAffine converts a GskTransform to 2D affine transformation factors.
//
// To recreate an equivalent transform from the factors returned by this
// function, use
//
//	gsk_transform_scale (gsk_transform_translate (NULL,
//	                                              &GRAPHENE_POINT_T (dx, dy)),
//	                     sx, sy)
//
// self must be a 2D affine transformation. If you are not sure, use
//
//	gsk_transform_get_category() >= GSK_TRANSFORM_CATEGORY_2D_AFFINE
//
// to check.
//
// The function returns the following values:
//
//   - outScaleX: return location for the scale factor in the x direction.
//   - outScaleY: return location for the scale factor in the y direction.
//   - outDx: return location for the translation in the x direction.
//   - outDy: return location for the translation in the y direction.
func (self *Transform) ToAffine() (outScaleX float32, outScaleY float32, outDx float32, outDy float32) {
	var _arg0 *C.GskTransform // out
	var _arg1 C.float         // in
	var _arg2 C.float         // in
	var _arg3 C.float         // in
	var _arg4 C.float         // in

	_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(self)))

	C.gsk_transform_to_affine(_arg0, &_arg1, &_arg2, &_arg3, &_arg4)
	runtime.KeepAlive(self)

	var _outScaleX float32 // out
	var _outScaleY float32 // out
	var _outDx float32     // out
	var _outDy float32     // out

	_outScaleX = float32(_arg1)
	_outScaleY = float32(_arg2)
	_outDx = float32(_arg3)
	_outDy = float32(_arg4)

	return _outScaleX, _outScaleY, _outDx, _outDy
}

// ToMatrix computes the actual value of self and stores it in out_matrix.
//
// The previous value of out_matrix will be ignored.
//
// The function returns the following values:
//
//   - outMatrix: matrix to set.
func (self *Transform) ToMatrix() *graphene.Matrix {
	var _arg0 *C.GskTransform     // out
	var _arg1 C.graphene_matrix_t // in

	if self != nil {
		_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(self)))
	}

	C.gsk_transform_to_matrix(_arg0, &_arg1)
	runtime.KeepAlive(self)

	var _outMatrix *graphene.Matrix // out

	_outMatrix = (*graphene.Matrix)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _outMatrix
}

// String converts a matrix into a string that is suitable for printing.
//
// The resulting string can be parsed with gsk.Transform().Parse.
//
// This is a wrapper around gsk.Transform.Print().
//
// The function returns the following values:
//
//   - utf8: new string for self.
func (self *Transform) String() string {
	var _arg0 *C.GskTransform // out
	var _cret *C.char         // in

	if self != nil {
		_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(self)))
	}

	_cret = C.gsk_transform_to_string(_arg0)
	runtime.KeepAlive(self)

	var _utf8 string // out

	_utf8 = C.GoString((*C.gchar)(unsafe.Pointer(_cret)))
	defer C.free(unsafe.Pointer(_cret))

	return _utf8
}

// ToTranslate converts a GskTransform to a translation operation.
//
// self must be a 2D transformation. If you are not sure, use
//
//	gsk_transform_get_category() >= GSK_TRANSFORM_CATEGORY_2D_TRANSLATE
//
// to check.
//
// The function returns the following values:
//
//   - outDx: return location for the translation in the x direction.
//   - outDy: return location for the translation in the y direction.
func (self *Transform) ToTranslate() (outDx float32, outDy float32) {
	var _arg0 *C.GskTransform // out
	var _arg1 C.float         // in
	var _arg2 C.float         // in

	_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(self)))

	C.gsk_transform_to_translate(_arg0, &_arg1, &_arg2)
	runtime.KeepAlive(self)

	var _outDx float32 // out
	var _outDy float32 // out

	_outDx = float32(_arg1)
	_outDy = float32(_arg2)

	return _outDx, _outDy
}

// Transform applies all the operations from other to next.
//
// The function takes the following parameters:
//
//   - other (optional): transform to apply.
//
// The function returns the following values:
//
//   - transform (optional): new transform.
func (next *Transform) Transform(other *Transform) *Transform {
	var _arg0 *C.GskTransform // out
	var _arg1 *C.GskTransform // out
	var _cret *C.GskTransform // in

	if next != nil {
		_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(next)))
	}
	if other != nil {
		_arg1 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(other)))
	}

	_cret = C.gsk_transform_transform(_arg0, _arg1)
	runtime.KeepAlive(next)
	runtime.KeepAlive(other)

	var _transform *Transform // out

	if _cret != nil {
		_transform = (*Transform)(gextras.NewStructNative(unsafe.Pointer(_cret)))
		runtime.SetFinalizer(
			gextras.StructIntern(unsafe.Pointer(_transform)),
			func(intern *struct{ C unsafe.Pointer }) {
				C.gsk_transform_unref((*C.GskTransform)(intern.C))
			},
		)
	}

	return _transform
}

// TransformBounds transforms a graphene_rect_t using the given transform self.
//
// The result is the bounding box containing the coplanar quad.
//
// The function takes the following parameters:
//
//   - rect: graphene_rect_t.
//
// The function returns the following values:
//
//   - outRect: return location for the bounds of the transformed rectangle.
func (self *Transform) TransformBounds(rect *graphene.Rect) *graphene.Rect {
	var _arg0 *C.GskTransform    // out
	var _arg1 *C.graphene_rect_t // out
	var _arg2 C.graphene_rect_t  // in

	_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(rect)))

	C.gsk_transform_transform_bounds(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(self)
	runtime.KeepAlive(rect)

	var _outRect *graphene.Rect // out

	_outRect = (*graphene.Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _outRect
}

// TransformPoint transforms a graphene_point_t using the given transform self.
//
// The function takes the following parameters:
//
//   - point: graphene_point_t.
//
// The function returns the following values:
//
//   - outPoint: return location for the transformed point.
func (self *Transform) TransformPoint(point *graphene.Point) *graphene.Point {
	var _arg0 *C.GskTransform     // out
	var _arg1 *C.graphene_point_t // out
	var _arg2 C.graphene_point_t  // in

	_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(self)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(point)))

	C.gsk_transform_transform_point(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(self)
	runtime.KeepAlive(point)

	var _outPoint *graphene.Point // out

	_outPoint = (*graphene.Point)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _outPoint
}

// Translate translates next in 2-dimensional space by point.
//
// The function takes the following parameters:
//
//   - point to translate the transform by.
//
// The function returns the following values:
//
//   - transform (optional): new transform.
func (next *Transform) Translate(point *graphene.Point) *Transform {
	var _arg0 *C.GskTransform     // out
	var _arg1 *C.graphene_point_t // out
	var _cret *C.GskTransform     // in

	if next != nil {
		_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(next)))
	}
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(point)))

	_cret = C.gsk_transform_translate(_arg0, _arg1)
	runtime.KeepAlive(next)
	runtime.KeepAlive(point)

	var _transform *Transform // out

	if _cret != nil {
		_transform = (*Transform)(gextras.NewStructNative(unsafe.Pointer(_cret)))
		runtime.SetFinalizer(
			gextras.StructIntern(unsafe.Pointer(_transform)),
			func(intern *struct{ C unsafe.Pointer }) {
				C.gsk_transform_unref((*C.GskTransform)(intern.C))
			},
		)
	}

	return _transform
}

// Translate3D translates next by point.
//
// The function takes the following parameters:
//
//   - point to translate the transform by.
//
// The function returns the following values:
//
//   - transform (optional): new transform.
func (next *Transform) Translate3D(point *graphene.Point3D) *Transform {
	var _arg0 *C.GskTransform       // out
	var _arg1 *C.graphene_point3d_t // out
	var _cret *C.GskTransform       // in

	if next != nil {
		_arg0 = (*C.GskTransform)(gextras.StructNative(unsafe.Pointer(next)))
	}
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(point)))

	_cret = C.gsk_transform_translate_3d(_arg0, _arg1)
	runtime.KeepAlive(next)
	runtime.KeepAlive(point)

	var _transform *Transform // out

	if _cret != nil {
		_transform = (*Transform)(gextras.NewStructNative(unsafe.Pointer(_cret)))
		runtime.SetFinalizer(
			gextras.StructIntern(unsafe.Pointer(_transform)),
			func(intern *struct{ C unsafe.Pointer }) {
				C.gsk_transform_unref((*C.GskTransform)(intern.C))
			},
		)
	}

	return _transform
}

// TransformParse parses the given string into a transform and puts it in
// out_transform.
//
// Strings printed via gsk.Transform.ToString() can be read in again
// successfully using this function.
//
// If string does not describe a valid transform, FALSE is returned and NULL is
// put in out_transform.
//
// The function takes the following parameters:
//
//   - str: string to parse.
//
// The function returns the following values:
//
//   - outTransform: location to put the transform in.
//   - ok: TRUE if string described a valid transform.
func TransformParse(str string) (*Transform, bool) {
	var _arg1 *C.char         // out
	var _arg2 *C.GskTransform // in
	var _cret C.gboolean      // in

	_arg1 = (*C.char)(unsafe.Pointer(C.CString(str)))
	defer C.free(unsafe.Pointer(_arg1))

	_cret = C.gsk_transform_parse(_arg1, &_arg2)
	runtime.KeepAlive(str)

	var _outTransform *Transform // out
	var _ok bool                 // out

	_outTransform = (*Transform)(gextras.NewStructNative(unsafe.Pointer(_arg2)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_outTransform)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gsk_transform_unref((*C.GskTransform)(intern.C))
		},
	)
	if _cret != 0 {
		_ok = true
	}

	return _outTransform, _ok
}
