// Code generated by girgen. DO NOT EDIT.

package gtk

import (
	"runtime"
	"unsafe"

	"github.com/diamondburned/gotk4/pkg/cairo"
	"github.com/diamondburned/gotk4/pkg/core/gbox"
	"github.com/diamondburned/gotk4/pkg/core/gerror"
	"github.com/diamondburned/gotk4/pkg/core/gextras"
	coreglib "github.com/diamondburned/gotk4/pkg/core/glib"
	"github.com/diamondburned/gotk4/pkg/gdk/v4"
	"github.com/diamondburned/gotk4/pkg/gio/v2"
	"github.com/diamondburned/gotk4/pkg/glib/v2"
	"github.com/diamondburned/gotk4/pkg/pango"
)

// #include <stdlib.h>
// #include <glib-object.h>
// #include <gtk/gtk.h>
import "C"

//export _gotk4_gtk4_AssistantPageFunc
func _gotk4_gtk4_AssistantPageFunc(arg1 C.int, arg2 C.gpointer) (cret C.int) {
	var fn AssistantPageFunc
	{
		v := gbox.Get(uintptr(arg2))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(AssistantPageFunc)
	}

	var _currentPage int // out

	_currentPage = int(arg1)

	gint := fn(_currentPage)

	var _ int

	cret = C.int(gint)

	return cret
}

//export _gotk4_gtk4_CellAllocCallback
func _gotk4_gtk4_CellAllocCallback(arg1 *C.GtkCellRenderer, arg2 *C.GdkRectangle, arg3 *C.GdkRectangle, arg4 C.gpointer) (cret C.gboolean) {
	var fn CellAllocCallback
	{
		v := gbox.Get(uintptr(arg4))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(CellAllocCallback)
	}

	var _renderer CellRendererer       // out
	var _cellArea *gdk.Rectangle       // out
	var _cellBackground *gdk.Rectangle // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.CellRendererer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(CellRendererer)
			return ok
		})
		rv, ok := casted.(CellRendererer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.CellRendererer")
		}
		_renderer = rv
	}
	_cellArea = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_cellBackground = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg3)))

	ok := fn(_renderer, _cellArea, _cellBackground)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_CellCallback
func _gotk4_gtk4_CellCallback(arg1 *C.GtkCellRenderer, arg2 C.gpointer) (cret C.gboolean) {
	var fn CellCallback
	{
		v := gbox.Get(uintptr(arg2))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(CellCallback)
	}

	var _renderer CellRendererer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.CellRendererer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(CellRendererer)
			return ok
		})
		rv, ok := casted.(CellRendererer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.CellRendererer")
		}
		_renderer = rv
	}

	ok := fn(_renderer)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_CellLayoutDataFunc
func _gotk4_gtk4_CellLayoutDataFunc(arg1 *C.GtkCellLayout, arg2 *C.GtkCellRenderer, arg3 *C.GtkTreeModel, arg4 *C.GtkTreeIter, arg5 C.gpointer) {
	var fn CellLayoutDataFunc
	{
		v := gbox.Get(uintptr(arg5))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(CellLayoutDataFunc)
	}

	var _cellLayout CellLayouter // out
	var _cell CellRendererer     // out
	var _treeModel TreeModeller  // out
	var _iter *TreeIter          // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.CellLayouter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(CellLayouter)
			return ok
		})
		rv, ok := casted.(CellLayouter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.CellLayouter")
		}
		_cellLayout = rv
	}
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.CellRendererer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(CellRendererer)
			return ok
		})
		rv, ok := casted.(CellRendererer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.CellRendererer")
		}
		_cell = rv
	}
	{
		objptr := unsafe.Pointer(arg3)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_treeModel = rv
	}
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg4)))

	fn(_cellLayout, _cell, _treeModel, _iter)
}

//export _gotk4_gtk4_CustomFilterFunc
func _gotk4_gtk4_CustomFilterFunc(arg1 C.gpointer, arg2 C.gpointer) (cret C.gboolean) {
	var fn CustomFilterFunc
	{
		v := gbox.Get(uintptr(arg2))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(CustomFilterFunc)
	}

	var _item *coreglib.Object // out

	_item = coreglib.Take(unsafe.Pointer(arg1))

	ok := fn(_item)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_DrawingAreaDrawFunc
func _gotk4_gtk4_DrawingAreaDrawFunc(arg1 *C.GtkDrawingArea, arg2 *C.cairo_t, arg3 C.int, arg4 C.int, arg5 C.gpointer) {
	var fn DrawingAreaDrawFunc
	{
		v := gbox.Get(uintptr(arg5))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(DrawingAreaDrawFunc)
	}

	var _drawingArea *DrawingArea // out
	var _cr *cairo.Context        // out
	var _width int                // out
	var _height int               // out

	_drawingArea = wrapDrawingArea(coreglib.Take(unsafe.Pointer(arg1)))
	_cr = cairo.WrapContext(uintptr(unsafe.Pointer(arg2)))
	C.cairo_reference(arg2)
	runtime.SetFinalizer(_cr, func(v *cairo.Context) {
		C.cairo_destroy((*C.cairo_t)(unsafe.Pointer(v.Native())))
	})
	_width = int(arg3)
	_height = int(arg4)

	fn(_drawingArea, _cr, _width, _height)
}

//export _gotk4_gtk4_EntryCompletionMatchFunc
func _gotk4_gtk4_EntryCompletionMatchFunc(arg1 *C.GtkEntryCompletion, arg2 *C.char, arg3 *C.GtkTreeIter, arg4 C.gpointer) (cret C.gboolean) {
	var fn EntryCompletionMatchFunc
	{
		v := gbox.Get(uintptr(arg4))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(EntryCompletionMatchFunc)
	}

	var _completion *EntryCompletion // out
	var _key string                  // out
	var _iter *TreeIter              // out

	_completion = wrapEntryCompletion(coreglib.Take(unsafe.Pointer(arg1)))
	_key = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg3)))

	ok := fn(_completion, _key, _iter)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_ExpressionNotify
func _gotk4_gtk4_ExpressionNotify(arg1 C.gpointer) {
	var fn ExpressionNotify
	{
		v := gbox.Get(uintptr(arg1))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(ExpressionNotify)
	}

	fn()
}

//export _gotk4_gtk4_FlowBoxCreateWidgetFunc
func _gotk4_gtk4_FlowBoxCreateWidgetFunc(arg1 C.gpointer, arg2 C.gpointer) (cret *C.GtkWidget) {
	var fn FlowBoxCreateWidgetFunc
	{
		v := gbox.Get(uintptr(arg2))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(FlowBoxCreateWidgetFunc)
	}

	var _item *coreglib.Object // out

	_item = coreglib.Take(unsafe.Pointer(arg1))

	widget := fn(_item)

	var _ Widgetter

	cret = (*C.GtkWidget)(unsafe.Pointer(coreglib.InternObject(widget).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(widget).Native()))

	return cret
}

//export _gotk4_gtk4_FlowBoxFilterFunc
func _gotk4_gtk4_FlowBoxFilterFunc(arg1 *C.GtkFlowBoxChild, arg2 C.gpointer) (cret C.gboolean) {
	var fn FlowBoxFilterFunc
	{
		v := gbox.Get(uintptr(arg2))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(FlowBoxFilterFunc)
	}

	var _child *FlowBoxChild // out

	_child = wrapFlowBoxChild(coreglib.Take(unsafe.Pointer(arg1)))

	ok := fn(_child)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_FlowBoxForEachFunc
func _gotk4_gtk4_FlowBoxForEachFunc(arg1 *C.GtkFlowBox, arg2 *C.GtkFlowBoxChild, arg3 C.gpointer) {
	var fn FlowBoxForEachFunc
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(FlowBoxForEachFunc)
	}

	var _box *FlowBox        // out
	var _child *FlowBoxChild // out

	_box = wrapFlowBox(coreglib.Take(unsafe.Pointer(arg1)))
	_child = wrapFlowBoxChild(coreglib.Take(unsafe.Pointer(arg2)))

	fn(_box, _child)
}

//export _gotk4_gtk4_FlowBoxSortFunc
func _gotk4_gtk4_FlowBoxSortFunc(arg1 *C.GtkFlowBoxChild, arg2 *C.GtkFlowBoxChild, arg3 C.gpointer) (cret C.int) {
	var fn FlowBoxSortFunc
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(FlowBoxSortFunc)
	}

	var _child1 *FlowBoxChild // out
	var _child2 *FlowBoxChild // out

	_child1 = wrapFlowBoxChild(coreglib.Take(unsafe.Pointer(arg1)))
	_child2 = wrapFlowBoxChild(coreglib.Take(unsafe.Pointer(arg2)))

	gint := fn(_child1, _child2)

	var _ int

	cret = C.int(gint)

	return cret
}

//export _gotk4_gtk4_FontFilterFunc
func _gotk4_gtk4_FontFilterFunc(arg1 *C.PangoFontFamily, arg2 *C.PangoFontFace, arg3 C.gpointer) (cret C.gboolean) {
	var fn FontFilterFunc
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(FontFilterFunc)
	}

	var _family pango.FontFamilier // out
	var _face pango.FontFacer      // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type pango.FontFamilier is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(pango.FontFamilier)
			return ok
		})
		rv, ok := casted.(pango.FontFamilier)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching pango.FontFamilier")
		}
		_family = rv
	}
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type pango.FontFacer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(pango.FontFacer)
			return ok
		})
		rv, ok := casted.(pango.FontFacer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching pango.FontFacer")
		}
		_face = rv
	}

	ok := fn(_family, _face)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_IconViewForEachFunc
func _gotk4_gtk4_IconViewForEachFunc(arg1 *C.GtkIconView, arg2 *C.GtkTreePath, arg3 C.gpointer) {
	var fn IconViewForEachFunc
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(IconViewForEachFunc)
	}

	var _iconView *IconView // out
	var _path *TreePath     // out

	_iconView = wrapIconView(coreglib.Take(unsafe.Pointer(arg1)))
	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	fn(_iconView, _path)
}

//export _gotk4_gtk4_ListBoxCreateWidgetFunc
func _gotk4_gtk4_ListBoxCreateWidgetFunc(arg1 C.gpointer, arg2 C.gpointer) (cret *C.GtkWidget) {
	var fn ListBoxCreateWidgetFunc
	{
		v := gbox.Get(uintptr(arg2))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(ListBoxCreateWidgetFunc)
	}

	var _item *coreglib.Object // out

	_item = coreglib.Take(unsafe.Pointer(arg1))

	widget := fn(_item)

	var _ Widgetter

	cret = (*C.GtkWidget)(unsafe.Pointer(coreglib.InternObject(widget).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(widget).Native()))

	return cret
}

//export _gotk4_gtk4_ListBoxFilterFunc
func _gotk4_gtk4_ListBoxFilterFunc(arg1 *C.GtkListBoxRow, arg2 C.gpointer) (cret C.gboolean) {
	var fn ListBoxFilterFunc
	{
		v := gbox.Get(uintptr(arg2))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(ListBoxFilterFunc)
	}

	var _row *ListBoxRow // out

	_row = wrapListBoxRow(coreglib.Take(unsafe.Pointer(arg1)))

	ok := fn(_row)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_ListBoxForEachFunc
func _gotk4_gtk4_ListBoxForEachFunc(arg1 *C.GtkListBox, arg2 *C.GtkListBoxRow, arg3 C.gpointer) {
	var fn ListBoxForEachFunc
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(ListBoxForEachFunc)
	}

	var _box *ListBox    // out
	var _row *ListBoxRow // out

	_box = wrapListBox(coreglib.Take(unsafe.Pointer(arg1)))
	_row = wrapListBoxRow(coreglib.Take(unsafe.Pointer(arg2)))

	fn(_box, _row)
}

//export _gotk4_gtk4_ListBoxSortFunc
func _gotk4_gtk4_ListBoxSortFunc(arg1 *C.GtkListBoxRow, arg2 *C.GtkListBoxRow, arg3 C.gpointer) (cret C.int) {
	var fn ListBoxSortFunc
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(ListBoxSortFunc)
	}

	var _row1 *ListBoxRow // out
	var _row2 *ListBoxRow // out

	_row1 = wrapListBoxRow(coreglib.Take(unsafe.Pointer(arg1)))
	_row2 = wrapListBoxRow(coreglib.Take(unsafe.Pointer(arg2)))

	gint := fn(_row1, _row2)

	var _ int

	cret = C.int(gint)

	return cret
}

//export _gotk4_gtk4_ListBoxUpdateHeaderFunc
func _gotk4_gtk4_ListBoxUpdateHeaderFunc(arg1 *C.GtkListBoxRow, arg2 *C.GtkListBoxRow, arg3 C.gpointer) {
	var fn ListBoxUpdateHeaderFunc
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(ListBoxUpdateHeaderFunc)
	}

	var _row *ListBoxRow    // out
	var _before *ListBoxRow // out

	_row = wrapListBoxRow(coreglib.Take(unsafe.Pointer(arg1)))
	if arg2 != nil {
		_before = wrapListBoxRow(coreglib.Take(unsafe.Pointer(arg2)))
	}

	fn(_row, _before)
}

//export _gotk4_gtk4_MapListModelMapFunc
func _gotk4_gtk4_MapListModelMapFunc(arg1 C.gpointer, arg2 C.gpointer) (cret C.gpointer) {
	var fn MapListModelMapFunc
	{
		v := gbox.Get(uintptr(arg2))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(MapListModelMapFunc)
	}

	var _item *coreglib.Object // out

	_item = coreglib.AssumeOwnership(unsafe.Pointer(arg1))

	object := fn(_item)

	var _ *coreglib.Object

	cret = C.gpointer(unsafe.Pointer(object.Native()))
	C.g_object_ref(C.gpointer(object.Native()))

	return cret
}

//export _gotk4_gtk4_MenuButtonCreatePopupFunc
func _gotk4_gtk4_MenuButtonCreatePopupFunc(arg1 *C.GtkMenuButton, arg2 C.gpointer) {
	var fn MenuButtonCreatePopupFunc
	{
		v := gbox.Get(uintptr(arg2))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(MenuButtonCreatePopupFunc)
	}

	var _menuButton *MenuButton // out

	_menuButton = wrapMenuButton(coreglib.Take(unsafe.Pointer(arg1)))

	fn(_menuButton)
}

//export _gotk4_gtk4_PageSetupDoneFunc
func _gotk4_gtk4_PageSetupDoneFunc(arg1 *C.GtkPageSetup, arg2 C.gpointer) {
	var fn PageSetupDoneFunc
	{
		v := gbox.Get(uintptr(arg2))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(PageSetupDoneFunc)
	}

	var _pageSetup *PageSetup // out

	_pageSetup = wrapPageSetup(coreglib.Take(unsafe.Pointer(arg1)))

	fn(_pageSetup)
}

//export _gotk4_gtk4_PrintSettingsFunc
func _gotk4_gtk4_PrintSettingsFunc(arg1 *C.char, arg2 *C.char, arg3 C.gpointer) {
	var fn PrintSettingsFunc
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(PrintSettingsFunc)
	}

	var _key string   // out
	var _value string // out

	_key = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	_value = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	fn(_key, _value)
}

//export _gotk4_gtk4_ScaleFormatValueFunc
func _gotk4_gtk4_ScaleFormatValueFunc(arg1 *C.GtkScale, arg2 C.double, arg3 C.gpointer) (cret *C.char) {
	var fn ScaleFormatValueFunc
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(ScaleFormatValueFunc)
	}

	var _scale *Scale  // out
	var _value float64 // out

	_scale = wrapScale(coreglib.Take(unsafe.Pointer(arg1)))
	_value = float64(arg2)

	utf8 := fn(_scale, _value)

	var _ string

	cret = (*C.char)(unsafe.Pointer(C.CString(utf8)))

	return cret
}

//export _gotk4_gtk4_ShortcutFunc
func _gotk4_gtk4_ShortcutFunc(arg1 *C.GtkWidget, arg2 *C.GVariant, arg3 C.gpointer) (cret C.gboolean) {
	var fn ShortcutFunc
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(ShortcutFunc)
	}

	var _widget Widgetter   // out
	var _args *glib.Variant // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	if arg2 != nil {
		_args = (*glib.Variant)(gextras.NewStructNative(unsafe.Pointer(arg2)))
		C.g_variant_ref(arg2)
		runtime.SetFinalizer(
			gextras.StructIntern(unsafe.Pointer(_args)),
			func(intern *struct{ C unsafe.Pointer }) {
				C.g_variant_unref((*C.GVariant)(intern.C))
			},
		)
	}

	ok := fn(_widget, _args)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TextCharPredicate
func _gotk4_gtk4_TextCharPredicate(arg1 C.gunichar, arg2 C.gpointer) (cret C.gboolean) {
	var fn TextCharPredicate
	{
		v := gbox.Get(uintptr(arg2))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(TextCharPredicate)
	}

	var _ch uint32 // out

	_ch = uint32(arg1)

	ok := fn(_ch)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TextTagTableForEach
func _gotk4_gtk4_TextTagTableForEach(arg1 *C.GtkTextTag, arg2 C.gpointer) {
	var fn TextTagTableForEach
	{
		v := gbox.Get(uintptr(arg2))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(TextTagTableForEach)
	}

	var _tag *TextTag // out

	_tag = wrapTextTag(coreglib.Take(unsafe.Pointer(arg1)))

	fn(_tag)
}

//export _gotk4_gtk4_TickCallback
func _gotk4_gtk4_TickCallback(arg1 *C.GtkWidget, arg2 *C.GdkFrameClock, arg3 C.gpointer) (cret C.gboolean) {
	var fn TickCallback
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(TickCallback)
	}

	var _widget Widgetter            // out
	var _frameClock gdk.FrameClocker // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gdk.FrameClocker is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.FrameClocker)
			return ok
		})
		rv, ok := casted.(gdk.FrameClocker)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.FrameClocker")
		}
		_frameClock = rv
	}

	ok := fn(_widget, _frameClock)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeCellDataFunc
func _gotk4_gtk4_TreeCellDataFunc(arg1 *C.GtkTreeViewColumn, arg2 *C.GtkCellRenderer, arg3 *C.GtkTreeModel, arg4 *C.GtkTreeIter, arg5 C.gpointer) {
	var fn TreeCellDataFunc
	{
		v := gbox.Get(uintptr(arg5))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(TreeCellDataFunc)
	}

	var _treeColumn *TreeViewColumn // out
	var _cell CellRendererer        // out
	var _treeModel TreeModeller     // out
	var _iter *TreeIter             // out

	_treeColumn = wrapTreeViewColumn(coreglib.Take(unsafe.Pointer(arg1)))
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.CellRendererer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(CellRendererer)
			return ok
		})
		rv, ok := casted.(CellRendererer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.CellRendererer")
		}
		_cell = rv
	}
	{
		objptr := unsafe.Pointer(arg3)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_treeModel = rv
	}
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg4)))

	fn(_treeColumn, _cell, _treeModel, _iter)
}

//export _gotk4_gtk4_TreeIterCompareFunc
func _gotk4_gtk4_TreeIterCompareFunc(arg1 *C.GtkTreeModel, arg2 *C.GtkTreeIter, arg3 *C.GtkTreeIter, arg4 C.gpointer) (cret C.int) {
	var fn TreeIterCompareFunc
	{
		v := gbox.Get(uintptr(arg4))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(TreeIterCompareFunc)
	}

	var _model TreeModeller // out
	var _a *TreeIter        // out
	var _b *TreeIter        // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_model = rv
	}
	_a = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_b = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg3)))

	gint := fn(_model, _a, _b)

	var _ int

	cret = C.int(gint)

	return cret
}

//export _gotk4_gtk4_TreeListModelCreateModelFunc
func _gotk4_gtk4_TreeListModelCreateModelFunc(arg1 C.gpointer, arg2 C.gpointer) (cret *C.GListModel) {
	var fn TreeListModelCreateModelFunc
	{
		v := gbox.Get(uintptr(arg2))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(TreeListModelCreateModelFunc)
	}

	var _item *coreglib.Object // out

	_item = coreglib.Take(unsafe.Pointer(arg1))

	listModel := fn(_item)

	var _ *gio.ListModel

	if listModel != nil {
		cret = (*C.GListModel)(unsafe.Pointer(coreglib.InternObject(listModel).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(listModel).Native()))
	}

	return cret
}

//export _gotk4_gtk4_TreeModelFilterModifyFunc
func _gotk4_gtk4_TreeModelFilterModifyFunc(arg1 *C.GtkTreeModel, arg2 *C.GtkTreeIter, arg3 *C.GValue, arg4 C.int, arg5 C.gpointer) {
	var fn TreeModelFilterModifyFunc
	{
		v := gbox.Get(uintptr(arg5))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(TreeModelFilterModifyFunc)
	}

	var _model TreeModeller // out
	var _iter *TreeIter     // out
	var _column int         // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_model = rv
	}
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_column = int(arg4)

	value := fn(_model, _iter, _column)

	var _ coreglib.Value

	*arg3 = *(*C.GValue)(unsafe.Pointer((&value).Native()))
}

//export _gotk4_gtk4_TreeModelFilterVisibleFunc
func _gotk4_gtk4_TreeModelFilterVisibleFunc(arg1 *C.GtkTreeModel, arg2 *C.GtkTreeIter, arg3 C.gpointer) (cret C.gboolean) {
	var fn TreeModelFilterVisibleFunc
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(TreeModelFilterVisibleFunc)
	}

	var _model TreeModeller // out
	var _iter *TreeIter     // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_model = rv
	}
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	ok := fn(_model, _iter)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeModelForEachFunc
func _gotk4_gtk4_TreeModelForEachFunc(arg1 *C.GtkTreeModel, arg2 *C.GtkTreePath, arg3 *C.GtkTreeIter, arg4 C.gpointer) (cret C.gboolean) {
	var fn TreeModelForEachFunc
	{
		v := gbox.Get(uintptr(arg4))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(TreeModelForEachFunc)
	}

	var _model TreeModeller // out
	var _path *TreePath     // out
	var _iter *TreeIter     // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_model = rv
	}
	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg3)))

	ok := fn(_model, _path, _iter)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeSelectionForEachFunc
func _gotk4_gtk4_TreeSelectionForEachFunc(arg1 *C.GtkTreeModel, arg2 *C.GtkTreePath, arg3 *C.GtkTreeIter, arg4 C.gpointer) {
	var fn TreeSelectionForEachFunc
	{
		v := gbox.Get(uintptr(arg4))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(TreeSelectionForEachFunc)
	}

	var _model TreeModeller // out
	var _path *TreePath     // out
	var _iter *TreeIter     // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_model = rv
	}
	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg3)))

	fn(_model, _path, _iter)
}

//export _gotk4_gtk4_TreeSelectionFunc
func _gotk4_gtk4_TreeSelectionFunc(arg1 *C.GtkTreeSelection, arg2 *C.GtkTreeModel, arg3 *C.GtkTreePath, arg4 C.gboolean, arg5 C.gpointer) (cret C.gboolean) {
	var fn TreeSelectionFunc
	{
		v := gbox.Get(uintptr(arg5))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(TreeSelectionFunc)
	}

	var _selection *TreeSelection   // out
	var _model TreeModeller         // out
	var _path *TreePath             // out
	var _pathCurrentlySelected bool // out

	_selection = wrapTreeSelection(coreglib.Take(unsafe.Pointer(arg1)))
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_model = rv
	}
	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg3)))
	if arg4 != 0 {
		_pathCurrentlySelected = true
	}

	ok := fn(_selection, _model, _path, _pathCurrentlySelected)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeViewColumnDropFunc
func _gotk4_gtk4_TreeViewColumnDropFunc(arg1 *C.GtkTreeView, arg2 *C.GtkTreeViewColumn, arg3 *C.GtkTreeViewColumn, arg4 *C.GtkTreeViewColumn, arg5 C.gpointer) (cret C.gboolean) {
	var fn TreeViewColumnDropFunc
	{
		v := gbox.Get(uintptr(arg5))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(TreeViewColumnDropFunc)
	}

	var _treeView *TreeView         // out
	var _column *TreeViewColumn     // out
	var _prevColumn *TreeViewColumn // out
	var _nextColumn *TreeViewColumn // out

	_treeView = wrapTreeView(coreglib.Take(unsafe.Pointer(arg1)))
	_column = wrapTreeViewColumn(coreglib.Take(unsafe.Pointer(arg2)))
	_prevColumn = wrapTreeViewColumn(coreglib.Take(unsafe.Pointer(arg3)))
	_nextColumn = wrapTreeViewColumn(coreglib.Take(unsafe.Pointer(arg4)))

	ok := fn(_treeView, _column, _prevColumn, _nextColumn)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeViewMappingFunc
func _gotk4_gtk4_TreeViewMappingFunc(arg1 *C.GtkTreeView, arg2 *C.GtkTreePath, arg3 C.gpointer) {
	var fn TreeViewMappingFunc
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(TreeViewMappingFunc)
	}

	var _treeView *TreeView // out
	var _path *TreePath     // out

	_treeView = wrapTreeView(coreglib.Take(unsafe.Pointer(arg1)))
	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	fn(_treeView, _path)
}

//export _gotk4_gtk4_TreeViewRowSeparatorFunc
func _gotk4_gtk4_TreeViewRowSeparatorFunc(arg1 *C.GtkTreeModel, arg2 *C.GtkTreeIter, arg3 C.gpointer) (cret C.gboolean) {
	var fn TreeViewRowSeparatorFunc
	{
		v := gbox.Get(uintptr(arg3))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(TreeViewRowSeparatorFunc)
	}

	var _model TreeModeller // out
	var _iter *TreeIter     // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_model = rv
	}
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	ok := fn(_model, _iter)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeViewSearchEqualFunc
func _gotk4_gtk4_TreeViewSearchEqualFunc(arg1 *C.GtkTreeModel, arg2 C.int, arg3 *C.char, arg4 *C.GtkTreeIter, arg5 C.gpointer) (cret C.gboolean) {
	var fn TreeViewSearchEqualFunc
	{
		v := gbox.Get(uintptr(arg5))
		if v == nil {
			panic(`callback not found`)
		}
		fn = v.(TreeViewSearchEqualFunc)
	}

	var _model TreeModeller // out
	var _column int         // out
	var _key string         // out
	var _iter *TreeIter     // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_model = rv
	}
	_column = int(arg2)
	_key = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg4)))

	ok := fn(_model, _column, _key, _iter)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_CellEditable_ConnectEditingDone
func _gotk4_gtk4_CellEditable_ConnectEditingDone(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_CellEditable_ConnectRemoveWidget
func _gotk4_gtk4_CellEditable_ConnectRemoveWidget(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ColorChooser_ConnectColorActivated
func _gotk4_gtk4_ColorChooser_ConnectColorActivated(arg0 C.gpointer, arg1 *C.GdkRGBA, arg2 C.guintptr) {
	var f func(color *gdk.RGBA)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(color *gdk.RGBA))
	}

	var _color *gdk.RGBA // out

	_color = (*gdk.RGBA)(gextras.NewStructNative(unsafe.Pointer(arg1)))

	f(_color)
}

//export _gotk4_gtk4_EditableTextWidget_ConnectChanged
func _gotk4_gtk4_EditableTextWidget_ConnectChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_EditableTextWidget_ConnectDeleteText
func _gotk4_gtk4_EditableTextWidget_ConnectDeleteText(arg0 C.gpointer, arg1 C.gint, arg2 C.gint, arg3 C.guintptr) {
	var f func(startPos, endPos int)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(startPos, endPos int))
	}

	var _startPos int // out
	var _endPos int   // out

	_startPos = int(arg1)
	_endPos = int(arg2)

	f(_startPos, _endPos)
}

//export _gotk4_gtk4_FontChooser_ConnectFontActivated
func _gotk4_gtk4_FontChooser_ConnectFontActivated(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(fontname string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(fontname string))
	}

	var _fontname string // out

	_fontname = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_fontname)
}

//export _gotk4_gtk4_PrintOperationPreview_ConnectGotPageSize
func _gotk4_gtk4_PrintOperationPreview_ConnectGotPageSize(arg0 C.gpointer, arg1 *C.GtkPrintContext, arg2 *C.GtkPageSetup, arg3 C.guintptr) {
	var f func(context *PrintContext, pageSetup *PageSetup)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(context *PrintContext, pageSetup *PageSetup))
	}

	var _context *PrintContext // out
	var _pageSetup *PageSetup  // out

	_context = wrapPrintContext(coreglib.Take(unsafe.Pointer(arg1)))
	_pageSetup = wrapPageSetup(coreglib.Take(unsafe.Pointer(arg2)))

	f(_context, _pageSetup)
}

//export _gotk4_gtk4_PrintOperationPreview_ConnectReady
func _gotk4_gtk4_PrintOperationPreview_ConnectReady(arg0 C.gpointer, arg1 *C.GtkPrintContext, arg2 C.guintptr) {
	var f func(context *PrintContext)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(context *PrintContext))
	}

	var _context *PrintContext // out

	_context = wrapPrintContext(coreglib.Take(unsafe.Pointer(arg1)))

	f(_context)
}

//export _gotk4_gtk4_SectionModel_ConnectSectionsChanged
func _gotk4_gtk4_SectionModel_ConnectSectionsChanged(arg0 C.gpointer, arg1 C.guint, arg2 C.guint, arg3 C.guintptr) {
	var f func(position, nItems uint)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(position, nItems uint))
	}

	var _position uint // out
	var _nItems uint   // out

	_position = uint(arg1)
	_nItems = uint(arg2)

	f(_position, _nItems)
}

//export _gotk4_gtk4_SelectionModel_ConnectSelectionChanged
func _gotk4_gtk4_SelectionModel_ConnectSelectionChanged(arg0 C.gpointer, arg1 C.guint, arg2 C.guint, arg3 C.guintptr) {
	var f func(position, nItems uint)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(position, nItems uint))
	}

	var _position uint // out
	var _nItems uint   // out

	_position = uint(arg1)
	_nItems = uint(arg2)

	f(_position, _nItems)
}

//export _gotk4_gtk4_StyleProvider_ConnectGTKPrivateChanged
func _gotk4_gtk4_StyleProvider_ConnectGTKPrivateChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TreeModel_ConnectRowChanged
func _gotk4_gtk4_TreeModel_ConnectRowChanged(arg0 C.gpointer, arg1 *C.GtkTreePath, arg2 *C.GtkTreeIter, arg3 C.guintptr) {
	var f func(path *TreePath, iter *TreeIter)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(path *TreePath, iter *TreeIter))
	}

	var _path *TreePath // out
	var _iter *TreeIter // out

	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	f(_path, _iter)
}

//export _gotk4_gtk4_TreeModel_ConnectRowDeleted
func _gotk4_gtk4_TreeModel_ConnectRowDeleted(arg0 C.gpointer, arg1 *C.GtkTreePath, arg2 C.guintptr) {
	var f func(path *TreePath)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(path *TreePath))
	}

	var _path *TreePath // out

	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg1)))

	f(_path)
}

//export _gotk4_gtk4_TreeModel_ConnectRowHasChildToggled
func _gotk4_gtk4_TreeModel_ConnectRowHasChildToggled(arg0 C.gpointer, arg1 *C.GtkTreePath, arg2 *C.GtkTreeIter, arg3 C.guintptr) {
	var f func(path *TreePath, iter *TreeIter)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(path *TreePath, iter *TreeIter))
	}

	var _path *TreePath // out
	var _iter *TreeIter // out

	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	f(_path, _iter)
}

//export _gotk4_gtk4_TreeModel_ConnectRowInserted
func _gotk4_gtk4_TreeModel_ConnectRowInserted(arg0 C.gpointer, arg1 *C.GtkTreePath, arg2 *C.GtkTreeIter, arg3 C.guintptr) {
	var f func(path *TreePath, iter *TreeIter)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(path *TreePath, iter *TreeIter))
	}

	var _path *TreePath // out
	var _iter *TreeIter // out

	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	f(_path, _iter)
}

//export _gotk4_gtk4_TreeModel_ConnectRowsReordered
func _gotk4_gtk4_TreeModel_ConnectRowsReordered(arg0 C.gpointer, arg1 *C.GtkTreePath, arg2 *C.GtkTreeIter, arg3 C.gpointer, arg4 C.guintptr) {
	var f func(path *TreePath, iter *TreeIter, newOrder unsafe.Pointer)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(path *TreePath, iter *TreeIter, newOrder unsafe.Pointer))
	}

	var _path *TreePath          // out
	var _iter *TreeIter          // out
	var _newOrder unsafe.Pointer // out

	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_newOrder = (unsafe.Pointer)(unsafe.Pointer(arg3))

	f(_path, _iter, _newOrder)
}

//export _gotk4_gtk4_TreeSortable_ConnectSortColumnChanged
func _gotk4_gtk4_TreeSortable_ConnectSortColumnChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ATContext_ConnectStateChange
func _gotk4_gtk4_ATContext_ConnectStateChange(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_AboutDialog_ConnectActivateLink
func _gotk4_gtk4_AboutDialog_ConnectActivateLink(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) (cret C.gboolean) {
	var f func(uri string) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(uri string) (ok bool))
	}

	var _uri string // out

	_uri = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	ok := f(_uri)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_AdjustmentClass_changed
func _gotk4_gtk4_AdjustmentClass_changed(arg0 *C.GtkAdjustment) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[AdjustmentOverrides](instance0)
	if overrides.Changed == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected AdjustmentOverrides.Changed, got none")
	}

	overrides.Changed()
}

//export _gotk4_gtk4_AdjustmentClass_value_changed
func _gotk4_gtk4_AdjustmentClass_value_changed(arg0 *C.GtkAdjustment) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[AdjustmentOverrides](instance0)
	if overrides.ValueChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected AdjustmentOverrides.ValueChanged, got none")
	}

	overrides.ValueChanged()
}

//export _gotk4_gtk4_Adjustment_ConnectChanged
func _gotk4_gtk4_Adjustment_ConnectChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Adjustment_ConnectValueChanged
func _gotk4_gtk4_Adjustment_ConnectValueChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_AppChooserButton_ConnectActivate
func _gotk4_gtk4_AppChooserButton_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_AppChooserButton_ConnectChanged
func _gotk4_gtk4_AppChooserButton_ConnectChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_AppChooserButton_ConnectCustomItemActivated
func _gotk4_gtk4_AppChooserButton_ConnectCustomItemActivated(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(itemName string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(itemName string))
	}

	var _itemName string // out

	_itemName = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_itemName)
}

//export _gotk4_gtk4_AppChooserWidget_ConnectApplicationActivated
func _gotk4_gtk4_AppChooserWidget_ConnectApplicationActivated(arg0 C.gpointer, arg1 *C.GAppInfo, arg2 C.guintptr) {
	var f func(application gio.AppInfor)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(application gio.AppInfor))
	}

	var _application gio.AppInfor // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AppInfor is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gio.AppInfor)
			return ok
		})
		rv, ok := casted.(gio.AppInfor)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AppInfor")
		}
		_application = rv
	}

	f(_application)
}

//export _gotk4_gtk4_AppChooserWidget_ConnectApplicationSelected
func _gotk4_gtk4_AppChooserWidget_ConnectApplicationSelected(arg0 C.gpointer, arg1 *C.GAppInfo, arg2 C.guintptr) {
	var f func(application gio.AppInfor)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(application gio.AppInfor))
	}

	var _application gio.AppInfor // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gio.AppInfor is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gio.AppInfor)
			return ok
		})
		rv, ok := casted.(gio.AppInfor)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gio.AppInfor")
		}
		_application = rv
	}

	f(_application)
}

//export _gotk4_gtk4_ApplicationClass_window_added
func _gotk4_gtk4_ApplicationClass_window_added(arg0 *C.GtkApplication, arg1 *C.GtkWindow) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.WindowAdded == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.WindowAdded, got none")
	}

	var _window *Window // out

	_window = wrapWindow(coreglib.Take(unsafe.Pointer(arg1)))

	overrides.WindowAdded(_window)
}

//export _gotk4_gtk4_ApplicationClass_window_removed
func _gotk4_gtk4_ApplicationClass_window_removed(arg0 *C.GtkApplication, arg1 *C.GtkWindow) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ApplicationOverrides](instance0)
	if overrides.WindowRemoved == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ApplicationOverrides.WindowRemoved, got none")
	}

	var _window *Window // out

	_window = wrapWindow(coreglib.Take(unsafe.Pointer(arg1)))

	overrides.WindowRemoved(_window)
}

//export _gotk4_gtk4_Application_ConnectQueryEnd
func _gotk4_gtk4_Application_ConnectQueryEnd(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Application_ConnectWindowAdded
func _gotk4_gtk4_Application_ConnectWindowAdded(arg0 C.gpointer, arg1 *C.GtkWindow, arg2 C.guintptr) {
	var f func(window *Window)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(window *Window))
	}

	var _window *Window // out

	_window = wrapWindow(coreglib.Take(unsafe.Pointer(arg1)))

	f(_window)
}

//export _gotk4_gtk4_Application_ConnectWindowRemoved
func _gotk4_gtk4_Application_ConnectWindowRemoved(arg0 C.gpointer, arg1 *C.GtkWindow, arg2 C.guintptr) {
	var f func(window *Window)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(window *Window))
	}

	var _window *Window // out

	_window = wrapWindow(coreglib.Take(unsafe.Pointer(arg1)))

	f(_window)
}

//export _gotk4_gtk4_Assistant_ConnectApply
func _gotk4_gtk4_Assistant_ConnectApply(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Assistant_ConnectCancel
func _gotk4_gtk4_Assistant_ConnectCancel(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Assistant_ConnectClose
func _gotk4_gtk4_Assistant_ConnectClose(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Assistant_ConnectEscape
func _gotk4_gtk4_Assistant_ConnectEscape(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Assistant_ConnectPrepare
func _gotk4_gtk4_Assistant_ConnectPrepare(arg0 C.gpointer, arg1 *C.GtkWidget, arg2 C.guintptr) {
	var f func(page Widgetter)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(page Widgetter))
	}

	var _page Widgetter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_page = rv
	}

	f(_page)
}

//export _gotk4_gtk4_ButtonClass_activate
func _gotk4_gtk4_ButtonClass_activate(arg0 *C.GtkButton) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ButtonOverrides](instance0)
	if overrides.Activate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ButtonOverrides.Activate, got none")
	}

	overrides.Activate()
}

//export _gotk4_gtk4_ButtonClass_clicked
func _gotk4_gtk4_ButtonClass_clicked(arg0 *C.GtkButton) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ButtonOverrides](instance0)
	if overrides.Clicked == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ButtonOverrides.Clicked, got none")
	}

	overrides.Clicked()
}

//export _gotk4_gtk4_Button_ConnectActivate
func _gotk4_gtk4_Button_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Button_ConnectClicked
func _gotk4_gtk4_Button_ConnectClicked(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Calendar_ConnectDaySelected
func _gotk4_gtk4_Calendar_ConnectDaySelected(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Calendar_ConnectNextMonth
func _gotk4_gtk4_Calendar_ConnectNextMonth(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Calendar_ConnectNextYear
func _gotk4_gtk4_Calendar_ConnectNextYear(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Calendar_ConnectPrevMonth
func _gotk4_gtk4_Calendar_ConnectPrevMonth(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Calendar_ConnectPrevYear
func _gotk4_gtk4_Calendar_ConnectPrevYear(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_CellAreaClass_activate
func _gotk4_gtk4_CellAreaClass_activate(arg0 *C.GtkCellArea, arg1 *C.GtkCellAreaContext, arg2 *C.GtkWidget, arg3 *C.GdkRectangle, arg4 C.GtkCellRendererState, arg5 C.gboolean) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaOverrides](instance0)
	if overrides.Activate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaOverrides.Activate, got none")
	}

	var _context *CellAreaContext // out
	var _widget Widgetter         // out
	var _cellArea *gdk.Rectangle  // out
	var _flags CellRendererState  // out
	var _editOnly bool            // out

	_context = wrapCellAreaContext(coreglib.Take(unsafe.Pointer(arg1)))
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	_cellArea = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg3)))
	_flags = CellRendererState(arg4)
	if arg5 != 0 {
		_editOnly = true
	}

	ok := overrides.Activate(_context, _widget, _cellArea, _flags, _editOnly)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_CellAreaClass_add
func _gotk4_gtk4_CellAreaClass_add(arg0 *C.GtkCellArea, arg1 *C.GtkCellRenderer) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaOverrides](instance0)
	if overrides.Add == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaOverrides.Add, got none")
	}

	var _renderer CellRendererer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.CellRendererer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(CellRendererer)
			return ok
		})
		rv, ok := casted.(CellRendererer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.CellRendererer")
		}
		_renderer = rv
	}

	overrides.Add(_renderer)
}

//export _gotk4_gtk4_CellAreaClass_apply_attributes
func _gotk4_gtk4_CellAreaClass_apply_attributes(arg0 *C.GtkCellArea, arg1 *C.GtkTreeModel, arg2 *C.GtkTreeIter, arg3 C.gboolean, arg4 C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaOverrides](instance0)
	if overrides.ApplyAttributes == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaOverrides.ApplyAttributes, got none")
	}

	var _treeModel TreeModeller // out
	var _iter *TreeIter         // out
	var _isExpander bool        // out
	var _isExpanded bool        // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_treeModel = rv
	}
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	if arg3 != 0 {
		_isExpander = true
	}
	if arg4 != 0 {
		_isExpanded = true
	}

	overrides.ApplyAttributes(_treeModel, _iter, _isExpander, _isExpanded)
}

//export _gotk4_gtk4_CellAreaClass_copy_context
func _gotk4_gtk4_CellAreaClass_copy_context(arg0 *C.GtkCellArea, arg1 *C.GtkCellAreaContext) (cret *C.GtkCellAreaContext) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaOverrides](instance0)
	if overrides.CopyContext == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaOverrides.CopyContext, got none")
	}

	var _context *CellAreaContext // out

	_context = wrapCellAreaContext(coreglib.Take(unsafe.Pointer(arg1)))

	cellAreaContext := overrides.CopyContext(_context)

	var _ *CellAreaContext

	cret = (*C.GtkCellAreaContext)(unsafe.Pointer(coreglib.InternObject(cellAreaContext).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(cellAreaContext).Native()))

	return cret
}

//export _gotk4_gtk4_CellAreaClass_create_context
func _gotk4_gtk4_CellAreaClass_create_context(arg0 *C.GtkCellArea) (cret *C.GtkCellAreaContext) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaOverrides](instance0)
	if overrides.CreateContext == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaOverrides.CreateContext, got none")
	}

	cellAreaContext := overrides.CreateContext()

	var _ *CellAreaContext

	cret = (*C.GtkCellAreaContext)(unsafe.Pointer(coreglib.InternObject(cellAreaContext).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(cellAreaContext).Native()))

	return cret
}

//export _gotk4_gtk4_CellAreaClass_event
func _gotk4_gtk4_CellAreaClass_event(arg0 *C.GtkCellArea, arg1 *C.GtkCellAreaContext, arg2 *C.GtkWidget, arg3 *C.GdkEvent, arg4 *C.GdkRectangle, arg5 C.GtkCellRendererState) (cret C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaOverrides](instance0)
	if overrides.Event == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaOverrides.Event, got none")
	}

	var _context *CellAreaContext // out
	var _widget Widgetter         // out
	var _event gdk.Eventer        // out
	var _cellArea *gdk.Rectangle  // out
	var _flags CellRendererState  // out

	_context = wrapCellAreaContext(coreglib.Take(unsafe.Pointer(arg1)))
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	{
		objptr := unsafe.Pointer(arg3)
		if objptr == nil {
			panic("object of type gdk.Eventer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Eventer)
			return ok
		})
		rv, ok := casted.(gdk.Eventer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Eventer")
		}
		_event = rv
	}
	_cellArea = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg4)))
	_flags = CellRendererState(arg5)

	gint := overrides.Event(_context, _widget, _event, _cellArea, _flags)

	var _ int

	cret = C.int(gint)

	return cret
}

//export _gotk4_gtk4_CellAreaClass_focus
func _gotk4_gtk4_CellAreaClass_focus(arg0 *C.GtkCellArea, arg1 C.GtkDirectionType) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaOverrides](instance0)
	if overrides.Focus == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaOverrides.Focus, got none")
	}

	var _direction DirectionType // out

	_direction = DirectionType(arg1)

	ok := overrides.Focus(_direction)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_CellAreaClass_get_preferred_height
func _gotk4_gtk4_CellAreaClass_get_preferred_height(arg0 *C.GtkCellArea, arg1 *C.GtkCellAreaContext, arg2 *C.GtkWidget, arg3 *C.int, arg4 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaOverrides](instance0)
	if overrides.PreferredHeight == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaOverrides.PreferredHeight, got none")
	}

	var _context *CellAreaContext // out
	var _widget Widgetter         // out

	_context = wrapCellAreaContext(coreglib.Take(unsafe.Pointer(arg1)))
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}

	minimumHeight, naturalHeight := overrides.PreferredHeight(_context, _widget)

	var _ int
	var _ int

	*arg3 = C.int(minimumHeight)
	*arg4 = C.int(naturalHeight)
}

//export _gotk4_gtk4_CellAreaClass_get_preferred_height_for_width
func _gotk4_gtk4_CellAreaClass_get_preferred_height_for_width(arg0 *C.GtkCellArea, arg1 *C.GtkCellAreaContext, arg2 *C.GtkWidget, arg3 C.int, arg4 *C.int, arg5 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaOverrides](instance0)
	if overrides.PreferredHeightForWidth == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaOverrides.PreferredHeightForWidth, got none")
	}

	var _context *CellAreaContext // out
	var _widget Widgetter         // out
	var _width int                // out

	_context = wrapCellAreaContext(coreglib.Take(unsafe.Pointer(arg1)))
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	_width = int(arg3)

	minimumHeight, naturalHeight := overrides.PreferredHeightForWidth(_context, _widget, _width)

	var _ int
	var _ int

	*arg4 = C.int(minimumHeight)
	*arg5 = C.int(naturalHeight)
}

//export _gotk4_gtk4_CellAreaClass_get_preferred_width
func _gotk4_gtk4_CellAreaClass_get_preferred_width(arg0 *C.GtkCellArea, arg1 *C.GtkCellAreaContext, arg2 *C.GtkWidget, arg3 *C.int, arg4 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaOverrides](instance0)
	if overrides.PreferredWidth == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaOverrides.PreferredWidth, got none")
	}

	var _context *CellAreaContext // out
	var _widget Widgetter         // out

	_context = wrapCellAreaContext(coreglib.Take(unsafe.Pointer(arg1)))
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}

	minimumWidth, naturalWidth := overrides.PreferredWidth(_context, _widget)

	var _ int
	var _ int

	*arg3 = C.int(minimumWidth)
	*arg4 = C.int(naturalWidth)
}

//export _gotk4_gtk4_CellAreaClass_get_preferred_width_for_height
func _gotk4_gtk4_CellAreaClass_get_preferred_width_for_height(arg0 *C.GtkCellArea, arg1 *C.GtkCellAreaContext, arg2 *C.GtkWidget, arg3 C.int, arg4 *C.int, arg5 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaOverrides](instance0)
	if overrides.PreferredWidthForHeight == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaOverrides.PreferredWidthForHeight, got none")
	}

	var _context *CellAreaContext // out
	var _widget Widgetter         // out
	var _height int               // out

	_context = wrapCellAreaContext(coreglib.Take(unsafe.Pointer(arg1)))
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	_height = int(arg3)

	minimumWidth, naturalWidth := overrides.PreferredWidthForHeight(_context, _widget, _height)

	var _ int
	var _ int

	*arg4 = C.int(minimumWidth)
	*arg5 = C.int(naturalWidth)
}

//export _gotk4_gtk4_CellAreaClass_get_request_mode
func _gotk4_gtk4_CellAreaClass_get_request_mode(arg0 *C.GtkCellArea) (cret C.GtkSizeRequestMode) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaOverrides](instance0)
	if overrides.RequestMode == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaOverrides.RequestMode, got none")
	}

	sizeRequestMode := overrides.RequestMode()

	var _ SizeRequestMode

	cret = C.GtkSizeRequestMode(sizeRequestMode)

	return cret
}

//export _gotk4_gtk4_CellAreaClass_is_activatable
func _gotk4_gtk4_CellAreaClass_is_activatable(arg0 *C.GtkCellArea) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaOverrides](instance0)
	if overrides.IsActivatable == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaOverrides.IsActivatable, got none")
	}

	ok := overrides.IsActivatable()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_CellAreaClass_remove
func _gotk4_gtk4_CellAreaClass_remove(arg0 *C.GtkCellArea, arg1 *C.GtkCellRenderer) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaOverrides](instance0)
	if overrides.Remove == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaOverrides.Remove, got none")
	}

	var _renderer CellRendererer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.CellRendererer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(CellRendererer)
			return ok
		})
		rv, ok := casted.(CellRendererer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.CellRendererer")
		}
		_renderer = rv
	}

	overrides.Remove(_renderer)
}

//export _gotk4_gtk4_CellAreaClass_snapshot
func _gotk4_gtk4_CellAreaClass_snapshot(arg0 *C.GtkCellArea, arg1 *C.GtkCellAreaContext, arg2 *C.GtkWidget, arg3 *C.GtkSnapshot, arg4 *C.GdkRectangle, arg5 *C.GdkRectangle, arg6 C.GtkCellRendererState, arg7 C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaOverrides](instance0)
	if overrides.Snapshot == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaOverrides.Snapshot, got none")
	}

	var _context *CellAreaContext      // out
	var _widget Widgetter              // out
	var _snapshot *Snapshot            // out
	var _backgroundArea *gdk.Rectangle // out
	var _cellArea *gdk.Rectangle       // out
	var _flags CellRendererState       // out
	var _paintFocus bool               // out

	_context = wrapCellAreaContext(coreglib.Take(unsafe.Pointer(arg1)))
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	_snapshot = wrapSnapshot(coreglib.Take(unsafe.Pointer(arg3)))
	_backgroundArea = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg4)))
	_cellArea = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg5)))
	_flags = CellRendererState(arg6)
	if arg7 != 0 {
		_paintFocus = true
	}

	overrides.Snapshot(_context, _widget, _snapshot, _backgroundArea, _cellArea, _flags, _paintFocus)
}

//export _gotk4_gtk4_CellArea_ConnectAddEditable
func _gotk4_gtk4_CellArea_ConnectAddEditable(arg0 C.gpointer, arg1 *C.GtkCellRenderer, arg2 *C.GtkCellEditable, arg3 *C.GdkRectangle, arg4 *C.gchar, arg5 C.guintptr) {
	var f func(renderer CellRendererer, editable CellEditabler, cellArea *gdk.Rectangle, path string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg5))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(renderer CellRendererer, editable CellEditabler, cellArea *gdk.Rectangle, path string))
	}

	var _renderer CellRendererer // out
	var _editable CellEditabler  // out
	var _cellArea *gdk.Rectangle // out
	var _path string             // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.CellRendererer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(CellRendererer)
			return ok
		})
		rv, ok := casted.(CellRendererer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.CellRendererer")
		}
		_renderer = rv
	}
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.CellEditabler is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(CellEditabler)
			return ok
		})
		rv, ok := casted.(CellEditabler)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.CellEditabler")
		}
		_editable = rv
	}
	_cellArea = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg3)))
	_path = C.GoString((*C.gchar)(unsafe.Pointer(arg4)))

	f(_renderer, _editable, _cellArea, _path)
}

//export _gotk4_gtk4_CellArea_ConnectApplyAttributes
func _gotk4_gtk4_CellArea_ConnectApplyAttributes(arg0 C.gpointer, arg1 *C.GtkTreeModel, arg2 *C.GtkTreeIter, arg3 C.gboolean, arg4 C.gboolean, arg5 C.guintptr) {
	var f func(model TreeModeller, iter *TreeIter, isExpander, isExpanded bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg5))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(model TreeModeller, iter *TreeIter, isExpander, isExpanded bool))
	}

	var _model TreeModeller // out
	var _iter *TreeIter     // out
	var _isExpander bool    // out
	var _isExpanded bool    // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_model = rv
	}
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	if arg3 != 0 {
		_isExpander = true
	}
	if arg4 != 0 {
		_isExpanded = true
	}

	f(_model, _iter, _isExpander, _isExpanded)
}

//export _gotk4_gtk4_CellArea_ConnectFocusChanged
func _gotk4_gtk4_CellArea_ConnectFocusChanged(arg0 C.gpointer, arg1 *C.GtkCellRenderer, arg2 *C.gchar, arg3 C.guintptr) {
	var f func(renderer CellRendererer, path string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(renderer CellRendererer, path string))
	}

	var _renderer CellRendererer // out
	var _path string             // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.CellRendererer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(CellRendererer)
			return ok
		})
		rv, ok := casted.(CellRendererer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.CellRendererer")
		}
		_renderer = rv
	}
	_path = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	f(_renderer, _path)
}

//export _gotk4_gtk4_CellArea_ConnectRemoveEditable
func _gotk4_gtk4_CellArea_ConnectRemoveEditable(arg0 C.gpointer, arg1 *C.GtkCellRenderer, arg2 *C.GtkCellEditable, arg3 C.guintptr) {
	var f func(renderer CellRendererer, editable CellEditabler)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(renderer CellRendererer, editable CellEditabler))
	}

	var _renderer CellRendererer // out
	var _editable CellEditabler  // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.CellRendererer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(CellRendererer)
			return ok
		})
		rv, ok := casted.(CellRendererer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.CellRendererer")
		}
		_renderer = rv
	}
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.CellEditabler is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(CellEditabler)
			return ok
		})
		rv, ok := casted.(CellEditabler)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.CellEditabler")
		}
		_editable = rv
	}

	f(_renderer, _editable)
}

//export _gotk4_gtk4_CellAreaContextClass_allocate
func _gotk4_gtk4_CellAreaContextClass_allocate(arg0 *C.GtkCellAreaContext, arg1 C.int, arg2 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaContextOverrides](instance0)
	if overrides.Allocate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaContextOverrides.Allocate, got none")
	}

	var _width int  // out
	var _height int // out

	_width = int(arg1)
	_height = int(arg2)

	overrides.Allocate(_width, _height)
}

//export _gotk4_gtk4_CellAreaContextClass_get_preferred_height_for_width
func _gotk4_gtk4_CellAreaContextClass_get_preferred_height_for_width(arg0 *C.GtkCellAreaContext, arg1 C.int, arg2 *C.int, arg3 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaContextOverrides](instance0)
	if overrides.PreferredHeightForWidth == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaContextOverrides.PreferredHeightForWidth, got none")
	}

	var _width int // out

	_width = int(arg1)

	minimumHeight, naturalHeight := overrides.PreferredHeightForWidth(_width)

	var _ int
	var _ int

	*arg2 = C.int(minimumHeight)
	*arg3 = C.int(naturalHeight)
}

//export _gotk4_gtk4_CellAreaContextClass_get_preferred_width_for_height
func _gotk4_gtk4_CellAreaContextClass_get_preferred_width_for_height(arg0 *C.GtkCellAreaContext, arg1 C.int, arg2 *C.int, arg3 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaContextOverrides](instance0)
	if overrides.PreferredWidthForHeight == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaContextOverrides.PreferredWidthForHeight, got none")
	}

	var _height int // out

	_height = int(arg1)

	minimumWidth, naturalWidth := overrides.PreferredWidthForHeight(_height)

	var _ int
	var _ int

	*arg2 = C.int(minimumWidth)
	*arg3 = C.int(naturalWidth)
}

//export _gotk4_gtk4_CellAreaContextClass_reset
func _gotk4_gtk4_CellAreaContextClass_reset(arg0 *C.GtkCellAreaContext) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellAreaContextOverrides](instance0)
	if overrides.Reset == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellAreaContextOverrides.Reset, got none")
	}

	overrides.Reset()
}

//export _gotk4_gtk4_CellRendererClass_activate
func _gotk4_gtk4_CellRendererClass_activate(arg0 *C.GtkCellRenderer, arg1 *C.GdkEvent, arg2 *C.GtkWidget, arg3 *C.char, arg4 *C.GdkRectangle, arg5 *C.GdkRectangle, arg6 C.GtkCellRendererState) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellRendererOverrides](instance0)
	if overrides.Activate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellRendererOverrides.Activate, got none")
	}

	var _event gdk.Eventer             // out
	var _widget Widgetter              // out
	var _path string                   // out
	var _backgroundArea *gdk.Rectangle // out
	var _cellArea *gdk.Rectangle       // out
	var _flags CellRendererState       // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.Eventer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Eventer)
			return ok
		})
		rv, ok := casted.(gdk.Eventer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Eventer")
		}
		_event = rv
	}
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	_path = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))
	_backgroundArea = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg4)))
	_cellArea = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg5)))
	_flags = CellRendererState(arg6)

	ok := overrides.Activate(_event, _widget, _path, _backgroundArea, _cellArea, _flags)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_CellRendererClass_editing_canceled
func _gotk4_gtk4_CellRendererClass_editing_canceled(arg0 *C.GtkCellRenderer) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellRendererOverrides](instance0)
	if overrides.EditingCanceled == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellRendererOverrides.EditingCanceled, got none")
	}

	overrides.EditingCanceled()
}

//export _gotk4_gtk4_CellRendererClass_editing_started
func _gotk4_gtk4_CellRendererClass_editing_started(arg0 *C.GtkCellRenderer, arg1 *C.GtkCellEditable, arg2 *C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellRendererOverrides](instance0)
	if overrides.EditingStarted == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellRendererOverrides.EditingStarted, got none")
	}

	var _editable CellEditabler // out
	var _path string            // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.CellEditabler is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(CellEditabler)
			return ok
		})
		rv, ok := casted.(CellEditabler)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.CellEditabler")
		}
		_editable = rv
	}
	_path = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	overrides.EditingStarted(_editable, _path)
}

//export _gotk4_gtk4_CellRendererClass_get_aligned_area
func _gotk4_gtk4_CellRendererClass_get_aligned_area(arg0 *C.GtkCellRenderer, arg1 *C.GtkWidget, arg2 C.GtkCellRendererState, arg3 *C.GdkRectangle, arg4 *C.GdkRectangle) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellRendererOverrides](instance0)
	if overrides.AlignedArea == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellRendererOverrides.AlignedArea, got none")
	}

	var _widget Widgetter        // out
	var _flags CellRendererState // out
	var _cellArea *gdk.Rectangle // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	_flags = CellRendererState(arg2)
	_cellArea = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg3)))

	alignedArea := overrides.AlignedArea(_widget, _flags, _cellArea)

	var _ *gdk.Rectangle

	*arg4 = *(*C.GdkRectangle)(gextras.StructNative(unsafe.Pointer(alignedArea)))
}

//export _gotk4_gtk4_CellRendererClass_get_preferred_height
func _gotk4_gtk4_CellRendererClass_get_preferred_height(arg0 *C.GtkCellRenderer, arg1 *C.GtkWidget, arg2 *C.int, arg3 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellRendererOverrides](instance0)
	if overrides.PreferredHeight == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellRendererOverrides.PreferredHeight, got none")
	}

	var _widget Widgetter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}

	minimumSize, naturalSize := overrides.PreferredHeight(_widget)

	var _ int
	var _ int

	*arg2 = C.int(minimumSize)
	*arg3 = C.int(naturalSize)
}

//export _gotk4_gtk4_CellRendererClass_get_preferred_height_for_width
func _gotk4_gtk4_CellRendererClass_get_preferred_height_for_width(arg0 *C.GtkCellRenderer, arg1 *C.GtkWidget, arg2 C.int, arg3 *C.int, arg4 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellRendererOverrides](instance0)
	if overrides.PreferredHeightForWidth == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellRendererOverrides.PreferredHeightForWidth, got none")
	}

	var _widget Widgetter // out
	var _width int        // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	_width = int(arg2)

	minimumHeight, naturalHeight := overrides.PreferredHeightForWidth(_widget, _width)

	var _ int
	var _ int

	*arg3 = C.int(minimumHeight)
	*arg4 = C.int(naturalHeight)
}

//export _gotk4_gtk4_CellRendererClass_get_preferred_width
func _gotk4_gtk4_CellRendererClass_get_preferred_width(arg0 *C.GtkCellRenderer, arg1 *C.GtkWidget, arg2 *C.int, arg3 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellRendererOverrides](instance0)
	if overrides.PreferredWidth == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellRendererOverrides.PreferredWidth, got none")
	}

	var _widget Widgetter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}

	minimumSize, naturalSize := overrides.PreferredWidth(_widget)

	var _ int
	var _ int

	*arg2 = C.int(minimumSize)
	*arg3 = C.int(naturalSize)
}

//export _gotk4_gtk4_CellRendererClass_get_preferred_width_for_height
func _gotk4_gtk4_CellRendererClass_get_preferred_width_for_height(arg0 *C.GtkCellRenderer, arg1 *C.GtkWidget, arg2 C.int, arg3 *C.int, arg4 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellRendererOverrides](instance0)
	if overrides.PreferredWidthForHeight == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellRendererOverrides.PreferredWidthForHeight, got none")
	}

	var _widget Widgetter // out
	var _height int       // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	_height = int(arg2)

	minimumWidth, naturalWidth := overrides.PreferredWidthForHeight(_widget, _height)

	var _ int
	var _ int

	*arg3 = C.int(minimumWidth)
	*arg4 = C.int(naturalWidth)
}

//export _gotk4_gtk4_CellRendererClass_get_request_mode
func _gotk4_gtk4_CellRendererClass_get_request_mode(arg0 *C.GtkCellRenderer) (cret C.GtkSizeRequestMode) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellRendererOverrides](instance0)
	if overrides.RequestMode == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellRendererOverrides.RequestMode, got none")
	}

	sizeRequestMode := overrides.RequestMode()

	var _ SizeRequestMode

	cret = C.GtkSizeRequestMode(sizeRequestMode)

	return cret
}

//export _gotk4_gtk4_CellRendererClass_snapshot
func _gotk4_gtk4_CellRendererClass_snapshot(arg0 *C.GtkCellRenderer, arg1 *C.GtkSnapshot, arg2 *C.GtkWidget, arg3 *C.GdkRectangle, arg4 *C.GdkRectangle, arg5 C.GtkCellRendererState) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellRendererOverrides](instance0)
	if overrides.Snapshot == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellRendererOverrides.Snapshot, got none")
	}

	var _snapshot *Snapshot            // out
	var _widget Widgetter              // out
	var _backgroundArea *gdk.Rectangle // out
	var _cellArea *gdk.Rectangle       // out
	var _flags CellRendererState       // out

	_snapshot = wrapSnapshot(coreglib.Take(unsafe.Pointer(arg1)))
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	_backgroundArea = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg3)))
	_cellArea = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg4)))
	_flags = CellRendererState(arg5)

	overrides.Snapshot(_snapshot, _widget, _backgroundArea, _cellArea, _flags)
}

//export _gotk4_gtk4_CellRendererClass_start_editing
func _gotk4_gtk4_CellRendererClass_start_editing(arg0 *C.GtkCellRenderer, arg1 *C.GdkEvent, arg2 *C.GtkWidget, arg3 *C.char, arg4 *C.GdkRectangle, arg5 *C.GdkRectangle, arg6 C.GtkCellRendererState) (cret *C.GtkCellEditable) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellRendererOverrides](instance0)
	if overrides.StartEditing == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellRendererOverrides.StartEditing, got none")
	}

	var _event gdk.Eventer             // out
	var _widget Widgetter              // out
	var _path string                   // out
	var _backgroundArea *gdk.Rectangle // out
	var _cellArea *gdk.Rectangle       // out
	var _flags CellRendererState       // out

	if arg1 != nil {
		{
			objptr := unsafe.Pointer(arg1)

			object := coreglib.Take(objptr)
			casted := object.WalkCast(func(obj coreglib.Objector) bool {
				_, ok := obj.(gdk.Eventer)
				return ok
			})
			rv, ok := casted.(gdk.Eventer)
			if !ok {
				panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Eventer")
			}
			_event = rv
		}
	}
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	_path = C.GoString((*C.gchar)(unsafe.Pointer(arg3)))
	_backgroundArea = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg4)))
	_cellArea = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg5)))
	_flags = CellRendererState(arg6)

	cellEditable := overrides.StartEditing(_event, _widget, _path, _backgroundArea, _cellArea, _flags)

	var _ *CellEditable

	if cellEditable != nil {
		cret = (*C.GtkCellEditable)(unsafe.Pointer(coreglib.InternObject(cellEditable).Native()))
	}

	return cret
}

//export _gotk4_gtk4_CellRenderer_ConnectEditingCanceled
func _gotk4_gtk4_CellRenderer_ConnectEditingCanceled(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_CellRenderer_ConnectEditingStarted
func _gotk4_gtk4_CellRenderer_ConnectEditingStarted(arg0 C.gpointer, arg1 *C.GtkCellEditable, arg2 *C.gchar, arg3 C.guintptr) {
	var f func(editable CellEditabler, path string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(editable CellEditabler, path string))
	}

	var _editable CellEditabler // out
	var _path string            // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.CellEditabler is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(CellEditabler)
			return ok
		})
		rv, ok := casted.(CellEditabler)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.CellEditabler")
		}
		_editable = rv
	}
	_path = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	f(_editable, _path)
}

//export _gotk4_gtk4_CellRendererAccel_ConnectAccelCleared
func _gotk4_gtk4_CellRendererAccel_ConnectAccelCleared(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(pathString string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(pathString string))
	}

	var _pathString string // out

	_pathString = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_pathString)
}

//export _gotk4_gtk4_CellRendererAccel_ConnectAccelEdited
func _gotk4_gtk4_CellRendererAccel_ConnectAccelEdited(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guint, arg3 C.GdkModifierType, arg4 C.guint, arg5 C.guintptr) {
	var f func(pathString string, accelKey uint, accelMods gdk.ModifierType, hardwareKeycode uint)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg5))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(pathString string, accelKey uint, accelMods gdk.ModifierType, hardwareKeycode uint))
	}

	var _pathString string          // out
	var _accelKey uint              // out
	var _accelMods gdk.ModifierType // out
	var _hardwareKeycode uint       // out

	_pathString = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	_accelKey = uint(arg2)
	_accelMods = gdk.ModifierType(arg3)
	_hardwareKeycode = uint(arg4)

	f(_pathString, _accelKey, _accelMods, _hardwareKeycode)
}

//export _gotk4_gtk4_CellRendererCombo_ConnectChanged
func _gotk4_gtk4_CellRendererCombo_ConnectChanged(arg0 C.gpointer, arg1 *C.gchar, arg2 *C.GtkTreeIter, arg3 C.guintptr) {
	var f func(pathString string, newIter *TreeIter)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(pathString string, newIter *TreeIter))
	}

	var _pathString string // out
	var _newIter *TreeIter // out

	_pathString = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	_newIter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	f(_pathString, _newIter)
}

//export _gotk4_gtk4_CellRendererTextClass_edited
func _gotk4_gtk4_CellRendererTextClass_edited(arg0 *C.GtkCellRendererText, arg1 *C.char, arg2 *C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CellRendererTextOverrides](instance0)
	if overrides.Edited == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CellRendererTextOverrides.Edited, got none")
	}

	var _path string    // out
	var _newText string // out

	_path = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	_newText = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	overrides.Edited(_path, _newText)
}

//export _gotk4_gtk4_CellRendererText_ConnectEdited
func _gotk4_gtk4_CellRendererText_ConnectEdited(arg0 C.gpointer, arg1 *C.gchar, arg2 *C.gchar, arg3 C.guintptr) {
	var f func(path, newText string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(path, newText string))
	}

	var _path string    // out
	var _newText string // out

	_path = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))
	_newText = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	f(_path, _newText)
}

//export _gotk4_gtk4_CellRendererToggle_ConnectToggled
func _gotk4_gtk4_CellRendererToggle_ConnectToggled(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(path string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(path string))
	}

	var _path string // out

	_path = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_path)
}

//export _gotk4_gtk4_CheckButtonClass_activate
func _gotk4_gtk4_CheckButtonClass_activate(arg0 *C.GtkCheckButton) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CheckButtonOverrides](instance0)
	if overrides.Activate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CheckButtonOverrides.Activate, got none")
	}

	overrides.Activate()
}

//export _gotk4_gtk4_CheckButtonClass_toggled
func _gotk4_gtk4_CheckButtonClass_toggled(arg0 *C.GtkCheckButton) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[CheckButtonOverrides](instance0)
	if overrides.Toggled == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected CheckButtonOverrides.Toggled, got none")
	}

	overrides.Toggled()
}

//export _gotk4_gtk4_CheckButton_ConnectActivate
func _gotk4_gtk4_CheckButton_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_CheckButton_ConnectToggled
func _gotk4_gtk4_CheckButton_ConnectToggled(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ColorButton_ConnectActivate
func _gotk4_gtk4_ColorButton_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ColorButton_ConnectColorSet
func _gotk4_gtk4_ColorButton_ConnectColorSet(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ColorDialogButton_ConnectActivate
func _gotk4_gtk4_ColorDialogButton_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ColumnView_ConnectActivate
func _gotk4_gtk4_ColumnView_ConnectActivate(arg0 C.gpointer, arg1 C.guint, arg2 C.guintptr) {
	var f func(position uint)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(position uint))
	}

	var _position uint // out

	_position = uint(arg1)

	f(_position)
}

//export _gotk4_gtk4_ComboBoxClass_activate
func _gotk4_gtk4_ComboBoxClass_activate(arg0 *C.GtkComboBox) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ComboBoxOverrides](instance0)
	if overrides.Activate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ComboBoxOverrides.Activate, got none")
	}

	overrides.Activate()
}

//export _gotk4_gtk4_ComboBoxClass_changed
func _gotk4_gtk4_ComboBoxClass_changed(arg0 *C.GtkComboBox) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ComboBoxOverrides](instance0)
	if overrides.Changed == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ComboBoxOverrides.Changed, got none")
	}

	overrides.Changed()
}

//export _gotk4_gtk4_ComboBoxClass_format_entry_text
func _gotk4_gtk4_ComboBoxClass_format_entry_text(arg0 *C.GtkComboBox, arg1 *C.char) (cret *C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ComboBoxOverrides](instance0)
	if overrides.FormatEntryText == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ComboBoxOverrides.FormatEntryText, got none")
	}

	var _path string // out

	_path = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	utf8 := overrides.FormatEntryText(_path)

	var _ string

	cret = (*C.char)(unsafe.Pointer(C.CString(utf8)))

	return cret
}

//export _gotk4_gtk4_ComboBox_ConnectActivate
func _gotk4_gtk4_ComboBox_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ComboBox_ConnectChanged
func _gotk4_gtk4_ComboBox_ConnectChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ComboBox_ConnectFormatEntryText
func _gotk4_gtk4_ComboBox_ConnectFormatEntryText(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) (cret *C.gchar) {
	var f func(path string) (utf8 string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(path string) (utf8 string))
	}

	var _path string // out

	_path = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	utf8 := f(_path)

	var _ string

	cret = (*C.gchar)(unsafe.Pointer(C.CString(utf8)))

	return cret
}

//export _gotk4_gtk4_ComboBox_ConnectMoveActive
func _gotk4_gtk4_ComboBox_ConnectMoveActive(arg0 C.gpointer, arg1 C.GtkScrollType, arg2 C.guintptr) {
	var f func(scrollType ScrollType)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(scrollType ScrollType))
	}

	var _scrollType ScrollType // out

	_scrollType = ScrollType(arg1)

	f(_scrollType)
}

//export _gotk4_gtk4_ComboBox_ConnectPopdown
func _gotk4_gtk4_ComboBox_ConnectPopdown(arg0 C.gpointer, arg1 C.guintptr) (cret C.gboolean) {
	var f func() (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (ok bool))
	}

	ok := f()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_ComboBox_ConnectPopup
func _gotk4_gtk4_ComboBox_ConnectPopup(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_CssProvider_ConnectParsingError
func _gotk4_gtk4_CssProvider_ConnectParsingError(arg0 C.gpointer, arg1 *C.GtkCssSection, arg2 *C.GError, arg3 C.guintptr) {
	var f func(section *CSSSection, err error)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(section *CSSSection, err error))
	}

	var _section *CSSSection // out
	var _err error           // out

	_section = (*CSSSection)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	C.gtk_css_section_ref(arg1)
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_section)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.gtk_css_section_unref((*C.GtkCssSection)(intern.C))
		},
	)
	_err = gerror.Take(unsafe.Pointer(arg2))

	f(_section, _err)
}

//export _gotk4_gtk4_DialogClass_close
func _gotk4_gtk4_DialogClass_close(arg0 *C.GtkDialog) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[DialogOverrides](instance0)
	if overrides.Close == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected DialogOverrides.Close, got none")
	}

	overrides.Close()
}

//export _gotk4_gtk4_DialogClass_response
func _gotk4_gtk4_DialogClass_response(arg0 *C.GtkDialog, arg1 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[DialogOverrides](instance0)
	if overrides.Response == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected DialogOverrides.Response, got none")
	}

	var _responseId int // out

	_responseId = int(arg1)

	overrides.Response(_responseId)
}

//export _gotk4_gtk4_Dialog_ConnectClose
func _gotk4_gtk4_Dialog_ConnectClose(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Dialog_ConnectResponse
func _gotk4_gtk4_Dialog_ConnectResponse(arg0 C.gpointer, arg1 C.gint, arg2 C.guintptr) {
	var f func(responseId int)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(responseId int))
	}

	var _responseId int // out

	_responseId = int(arg1)

	f(_responseId)
}

//export _gotk4_gtk4_DragSource_ConnectDragBegin
func _gotk4_gtk4_DragSource_ConnectDragBegin(arg0 C.gpointer, arg1 *C.GdkDrag, arg2 C.guintptr) {
	var f func(drag gdk.Dragger)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(drag gdk.Dragger))
	}

	var _drag gdk.Dragger // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.Dragger is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Dragger)
			return ok
		})
		rv, ok := casted.(gdk.Dragger)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Dragger")
		}
		_drag = rv
	}

	f(_drag)
}

//export _gotk4_gtk4_DragSource_ConnectDragCancel
func _gotk4_gtk4_DragSource_ConnectDragCancel(arg0 C.gpointer, arg1 *C.GdkDrag, arg2 C.GdkDragCancelReason, arg3 C.guintptr) (cret C.gboolean) {
	var f func(drag gdk.Dragger, reason gdk.DragCancelReason) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(drag gdk.Dragger, reason gdk.DragCancelReason) (ok bool))
	}

	var _drag gdk.Dragger            // out
	var _reason gdk.DragCancelReason // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.Dragger is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Dragger)
			return ok
		})
		rv, ok := casted.(gdk.Dragger)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Dragger")
		}
		_drag = rv
	}
	_reason = gdk.DragCancelReason(arg2)

	ok := f(_drag, _reason)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_DragSource_ConnectDragEnd
func _gotk4_gtk4_DragSource_ConnectDragEnd(arg0 C.gpointer, arg1 *C.GdkDrag, arg2 C.gboolean, arg3 C.guintptr) {
	var f func(drag gdk.Dragger, deleteData bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(drag gdk.Dragger, deleteData bool))
	}

	var _drag gdk.Dragger // out
	var _deleteData bool  // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.Dragger is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Dragger)
			return ok
		})
		rv, ok := casted.(gdk.Dragger)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Dragger")
		}
		_drag = rv
	}
	if arg2 != 0 {
		_deleteData = true
	}

	f(_drag, _deleteData)
}

//export _gotk4_gtk4_DragSource_ConnectPrepare
func _gotk4_gtk4_DragSource_ConnectPrepare(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) (cret *C.GdkContentProvider) {
	var f func(x, y float64) (contentProvider *gdk.ContentProvider)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(x, y float64) (contentProvider *gdk.ContentProvider))
	}

	var _x float64 // out
	var _y float64 // out

	_x = float64(arg1)
	_y = float64(arg2)

	contentProvider := f(_x, _y)

	var _ *gdk.ContentProvider

	if contentProvider != nil {
		cret = (*C.GdkContentProvider)(unsafe.Pointer(coreglib.InternObject(contentProvider).Native()))
		C.g_object_ref(C.gpointer(coreglib.InternObject(contentProvider).Native()))
	}

	return cret
}

//export _gotk4_gtk4_DrawingAreaClass_resize
func _gotk4_gtk4_DrawingAreaClass_resize(arg0 *C.GtkDrawingArea, arg1 C.int, arg2 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[DrawingAreaOverrides](instance0)
	if overrides.Resize == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected DrawingAreaOverrides.Resize, got none")
	}

	var _width int  // out
	var _height int // out

	_width = int(arg1)
	_height = int(arg2)

	overrides.Resize(_width, _height)
}

//export _gotk4_gtk4_DrawingArea_ConnectResize
func _gotk4_gtk4_DrawingArea_ConnectResize(arg0 C.gpointer, arg1 C.gint, arg2 C.gint, arg3 C.guintptr) {
	var f func(width, height int)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(width, height int))
	}

	var _width int  // out
	var _height int // out

	_width = int(arg1)
	_height = int(arg2)

	f(_width, _height)
}

//export _gotk4_gtk4_DropControllerMotion_ConnectEnter
func _gotk4_gtk4_DropControllerMotion_ConnectEnter(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(x, y float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(x, y float64))
	}

	var _x float64 // out
	var _y float64 // out

	_x = float64(arg1)
	_y = float64(arg2)

	f(_x, _y)
}

//export _gotk4_gtk4_DropControllerMotion_ConnectLeave
func _gotk4_gtk4_DropControllerMotion_ConnectLeave(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_DropControllerMotion_ConnectMotion
func _gotk4_gtk4_DropControllerMotion_ConnectMotion(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(x, y float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(x, y float64))
	}

	var _x float64 // out
	var _y float64 // out

	_x = float64(arg1)
	_y = float64(arg2)

	f(_x, _y)
}

//export _gotk4_gtk4_DropDown_ConnectActivate
func _gotk4_gtk4_DropDown_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_DropTarget_ConnectAccept
func _gotk4_gtk4_DropTarget_ConnectAccept(arg0 C.gpointer, arg1 *C.GdkDrop, arg2 C.guintptr) (cret C.gboolean) {
	var f func(drop gdk.Dropper) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(drop gdk.Dropper) (ok bool))
	}

	var _drop gdk.Dropper // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.Dropper is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Dropper)
			return ok
		})
		rv, ok := casted.(gdk.Dropper)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Dropper")
		}
		_drop = rv
	}

	ok := f(_drop)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_DropTarget_ConnectDrop
func _gotk4_gtk4_DropTarget_ConnectDrop(arg0 C.gpointer, arg1 *C.GValue, arg2 C.gdouble, arg3 C.gdouble, arg4 C.guintptr) (cret C.gboolean) {
	var f func(value *coreglib.Value, x, y float64) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(value *coreglib.Value, x, y float64) (ok bool))
	}

	var _value *coreglib.Value // out
	var _x float64             // out
	var _y float64             // out

	_value = coreglib.ValueFromNative(unsafe.Pointer(arg1))
	_x = float64(arg2)
	_y = float64(arg3)

	ok := f(_value, _x, _y)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_DropTarget_ConnectEnter
func _gotk4_gtk4_DropTarget_ConnectEnter(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) (cret C.GdkDragAction) {
	var f func(x, y float64) (dragAction gdk.DragAction)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(x, y float64) (dragAction gdk.DragAction))
	}

	var _x float64 // out
	var _y float64 // out

	_x = float64(arg1)
	_y = float64(arg2)

	dragAction := f(_x, _y)

	var _ gdk.DragAction

	cret = C.GdkDragAction(dragAction)

	return cret
}

//export _gotk4_gtk4_DropTarget_ConnectLeave
func _gotk4_gtk4_DropTarget_ConnectLeave(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_DropTarget_ConnectMotion
func _gotk4_gtk4_DropTarget_ConnectMotion(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) (cret C.GdkDragAction) {
	var f func(x, y float64) (dragAction gdk.DragAction)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(x, y float64) (dragAction gdk.DragAction))
	}

	var _x float64 // out
	var _y float64 // out

	_x = float64(arg1)
	_y = float64(arg2)

	dragAction := f(_x, _y)

	var _ gdk.DragAction

	cret = C.GdkDragAction(dragAction)

	return cret
}

//export _gotk4_gtk4_DropTargetAsync_ConnectAccept
func _gotk4_gtk4_DropTargetAsync_ConnectAccept(arg0 C.gpointer, arg1 *C.GdkDrop, arg2 C.guintptr) (cret C.gboolean) {
	var f func(drop gdk.Dropper) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(drop gdk.Dropper) (ok bool))
	}

	var _drop gdk.Dropper // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.Dropper is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Dropper)
			return ok
		})
		rv, ok := casted.(gdk.Dropper)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Dropper")
		}
		_drop = rv
	}

	ok := f(_drop)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_DropTargetAsync_ConnectDragEnter
func _gotk4_gtk4_DropTargetAsync_ConnectDragEnter(arg0 C.gpointer, arg1 *C.GdkDrop, arg2 C.gdouble, arg3 C.gdouble, arg4 C.guintptr) (cret C.GdkDragAction) {
	var f func(drop gdk.Dropper, x, y float64) (dragAction gdk.DragAction)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(drop gdk.Dropper, x, y float64) (dragAction gdk.DragAction))
	}

	var _drop gdk.Dropper // out
	var _x float64        // out
	var _y float64        // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.Dropper is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Dropper)
			return ok
		})
		rv, ok := casted.(gdk.Dropper)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Dropper")
		}
		_drop = rv
	}
	_x = float64(arg2)
	_y = float64(arg3)

	dragAction := f(_drop, _x, _y)

	var _ gdk.DragAction

	cret = C.GdkDragAction(dragAction)

	return cret
}

//export _gotk4_gtk4_DropTargetAsync_ConnectDragLeave
func _gotk4_gtk4_DropTargetAsync_ConnectDragLeave(arg0 C.gpointer, arg1 *C.GdkDrop, arg2 C.guintptr) {
	var f func(drop gdk.Dropper)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(drop gdk.Dropper))
	}

	var _drop gdk.Dropper // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.Dropper is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Dropper)
			return ok
		})
		rv, ok := casted.(gdk.Dropper)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Dropper")
		}
		_drop = rv
	}

	f(_drop)
}

//export _gotk4_gtk4_DropTargetAsync_ConnectDragMotion
func _gotk4_gtk4_DropTargetAsync_ConnectDragMotion(arg0 C.gpointer, arg1 *C.GdkDrop, arg2 C.gdouble, arg3 C.gdouble, arg4 C.guintptr) (cret C.GdkDragAction) {
	var f func(drop gdk.Dropper, x, y float64) (dragAction gdk.DragAction)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(drop gdk.Dropper, x, y float64) (dragAction gdk.DragAction))
	}

	var _drop gdk.Dropper // out
	var _x float64        // out
	var _y float64        // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.Dropper is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Dropper)
			return ok
		})
		rv, ok := casted.(gdk.Dropper)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Dropper")
		}
		_drop = rv
	}
	_x = float64(arg2)
	_y = float64(arg3)

	dragAction := f(_drop, _x, _y)

	var _ gdk.DragAction

	cret = C.GdkDragAction(dragAction)

	return cret
}

//export _gotk4_gtk4_DropTargetAsync_ConnectDrop
func _gotk4_gtk4_DropTargetAsync_ConnectDrop(arg0 C.gpointer, arg1 *C.GdkDrop, arg2 C.gdouble, arg3 C.gdouble, arg4 C.guintptr) (cret C.gboolean) {
	var f func(drop gdk.Dropper, x, y float64) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(drop gdk.Dropper, x, y float64) (ok bool))
	}

	var _drop gdk.Dropper // out
	var _x float64        // out
	var _y float64        // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.Dropper is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Dropper)
			return ok
		})
		rv, ok := casted.(gdk.Dropper)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Dropper")
		}
		_drop = rv
	}
	_x = float64(arg2)
	_y = float64(arg3)

	ok := f(_drop, _x, _y)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_EmojiChooser_ConnectEmojiPicked
func _gotk4_gtk4_EmojiChooser_ConnectEmojiPicked(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(text string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(text string))
	}

	var _text string // out

	_text = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_text)
}

//export _gotk4_gtk4_EntryClass_activate
func _gotk4_gtk4_EntryClass_activate(arg0 *C.GtkEntry) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[EntryOverrides](instance0)
	if overrides.Activate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected EntryOverrides.Activate, got none")
	}

	overrides.Activate()
}

//export _gotk4_gtk4_Entry_ConnectActivate
func _gotk4_gtk4_Entry_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Entry_ConnectIconPress
func _gotk4_gtk4_Entry_ConnectIconPress(arg0 C.gpointer, arg1 C.GtkEntryIconPosition, arg2 C.guintptr) {
	var f func(iconPos EntryIconPosition)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(iconPos EntryIconPosition))
	}

	var _iconPos EntryIconPosition // out

	_iconPos = EntryIconPosition(arg1)

	f(_iconPos)
}

//export _gotk4_gtk4_Entry_ConnectIconRelease
func _gotk4_gtk4_Entry_ConnectIconRelease(arg0 C.gpointer, arg1 C.GtkEntryIconPosition, arg2 C.guintptr) {
	var f func(iconPos EntryIconPosition)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(iconPos EntryIconPosition))
	}

	var _iconPos EntryIconPosition // out

	_iconPos = EntryIconPosition(arg1)

	f(_iconPos)
}

//export _gotk4_gtk4_EntryBufferClass_delete_text
func _gotk4_gtk4_EntryBufferClass_delete_text(arg0 *C.GtkEntryBuffer, arg1 C.guint, arg2 C.guint) (cret C.guint) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[EntryBufferOverrides](instance0)
	if overrides.DeleteText == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected EntryBufferOverrides.DeleteText, got none")
	}

	var _position uint // out
	var _nChars uint   // out

	_position = uint(arg1)
	_nChars = uint(arg2)

	guint := overrides.DeleteText(_position, _nChars)

	var _ uint

	cret = C.guint(guint)

	return cret
}

//export _gotk4_gtk4_EntryBufferClass_deleted_text
func _gotk4_gtk4_EntryBufferClass_deleted_text(arg0 *C.GtkEntryBuffer, arg1 C.guint, arg2 C.guint) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[EntryBufferOverrides](instance0)
	if overrides.DeletedText == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected EntryBufferOverrides.DeletedText, got none")
	}

	var _position uint // out
	var _nChars uint   // out

	_position = uint(arg1)
	_nChars = uint(arg2)

	overrides.DeletedText(_position, _nChars)
}

//export _gotk4_gtk4_EntryBufferClass_get_length
func _gotk4_gtk4_EntryBufferClass_get_length(arg0 *C.GtkEntryBuffer) (cret C.guint) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[EntryBufferOverrides](instance0)
	if overrides.Length == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected EntryBufferOverrides.Length, got none")
	}

	guint := overrides.Length()

	var _ uint

	cret = C.guint(guint)

	return cret
}

//export _gotk4_gtk4_EntryBufferClass_get_text
func _gotk4_gtk4_EntryBufferClass_get_text(arg0 *C.GtkEntryBuffer, arg1 *C.gsize) (cret *C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[EntryBufferOverrides](instance0)
	if overrides.Text == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected EntryBufferOverrides.Text, got none")
	}

	var _nBytes *uint // out

	_nBytes = (*uint)(unsafe.Pointer(arg1))

	utf8 := overrides.Text(_nBytes)

	var _ string

	cret = (*C.char)(unsafe.Pointer(C.CString(utf8)))
	defer C.free(unsafe.Pointer(cret))

	return cret
}

//export _gotk4_gtk4_EntryBufferClass_insert_text
func _gotk4_gtk4_EntryBufferClass_insert_text(arg0 *C.GtkEntryBuffer, arg1 C.guint, arg2 *C.char, arg3 C.guint) (cret C.guint) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[EntryBufferOverrides](instance0)
	if overrides.InsertText == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected EntryBufferOverrides.InsertText, got none")
	}

	var _position uint // out
	var _chars string  // out
	var _nChars uint   // out

	_position = uint(arg1)
	_chars = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_nChars = uint(arg3)

	guint := overrides.InsertText(_position, _chars, _nChars)

	var _ uint

	cret = C.guint(guint)

	return cret
}

//export _gotk4_gtk4_EntryBufferClass_inserted_text
func _gotk4_gtk4_EntryBufferClass_inserted_text(arg0 *C.GtkEntryBuffer, arg1 C.guint, arg2 *C.char, arg3 C.guint) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[EntryBufferOverrides](instance0)
	if overrides.InsertedText == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected EntryBufferOverrides.InsertedText, got none")
	}

	var _position uint // out
	var _chars string  // out
	var _nChars uint   // out

	_position = uint(arg1)
	_chars = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_nChars = uint(arg3)

	overrides.InsertedText(_position, _chars, _nChars)
}

//export _gotk4_gtk4_EntryBuffer_ConnectDeletedText
func _gotk4_gtk4_EntryBuffer_ConnectDeletedText(arg0 C.gpointer, arg1 C.guint, arg2 C.guint, arg3 C.guintptr) {
	var f func(position, nChars uint)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(position, nChars uint))
	}

	var _position uint // out
	var _nChars uint   // out

	_position = uint(arg1)
	_nChars = uint(arg2)

	f(_position, _nChars)
}

//export _gotk4_gtk4_EntryBuffer_ConnectInsertedText
func _gotk4_gtk4_EntryBuffer_ConnectInsertedText(arg0 C.gpointer, arg1 C.guint, arg2 *C.gchar, arg3 C.guint, arg4 C.guintptr) {
	var f func(position uint, chars string, nChars uint)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(position uint, chars string, nChars uint))
	}

	var _position uint // out
	var _chars string  // out
	var _nChars uint   // out

	_position = uint(arg1)
	_chars = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_nChars = uint(arg3)

	f(_position, _chars, _nChars)
}

//export _gotk4_gtk4_EntryCompletion_ConnectCursorOnMatch
func _gotk4_gtk4_EntryCompletion_ConnectCursorOnMatch(arg0 C.gpointer, arg1 *C.GtkTreeModel, arg2 *C.GtkTreeIter, arg3 C.guintptr) (cret C.gboolean) {
	var f func(model TreeModeller, iter *TreeIter) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(model TreeModeller, iter *TreeIter) (ok bool))
	}

	var _model TreeModeller // out
	var _iter *TreeIter     // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_model = rv
	}
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	ok := f(_model, _iter)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_EntryCompletion_ConnectInsertPrefix
func _gotk4_gtk4_EntryCompletion_ConnectInsertPrefix(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) (cret C.gboolean) {
	var f func(prefix string) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(prefix string) (ok bool))
	}

	var _prefix string // out

	_prefix = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	ok := f(_prefix)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_EntryCompletion_ConnectMatchSelected
func _gotk4_gtk4_EntryCompletion_ConnectMatchSelected(arg0 C.gpointer, arg1 *C.GtkTreeModel, arg2 *C.GtkTreeIter, arg3 C.guintptr) (cret C.gboolean) {
	var f func(model TreeModeller, iter *TreeIter) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(model TreeModeller, iter *TreeIter) (ok bool))
	}

	var _model TreeModeller // out
	var _iter *TreeIter     // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_model = rv
	}
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	ok := f(_model, _iter)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_EntryCompletion_ConnectNoMatches
func _gotk4_gtk4_EntryCompletion_ConnectNoMatches(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_EventControllerFocus_ConnectEnter
func _gotk4_gtk4_EventControllerFocus_ConnectEnter(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_EventControllerFocus_ConnectLeave
func _gotk4_gtk4_EventControllerFocus_ConnectLeave(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_EventControllerKey_ConnectIMUpdate
func _gotk4_gtk4_EventControllerKey_ConnectIMUpdate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_EventControllerKey_ConnectKeyPressed
func _gotk4_gtk4_EventControllerKey_ConnectKeyPressed(arg0 C.gpointer, arg1 C.guint, arg2 C.guint, arg3 C.GdkModifierType, arg4 C.guintptr) (cret C.gboolean) {
	var f func(keyval, keycode uint, state gdk.ModifierType) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(keyval, keycode uint, state gdk.ModifierType) (ok bool))
	}

	var _keyval uint            // out
	var _keycode uint           // out
	var _state gdk.ModifierType // out

	_keyval = uint(arg1)
	_keycode = uint(arg2)
	_state = gdk.ModifierType(arg3)

	ok := f(_keyval, _keycode, _state)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_EventControllerKey_ConnectKeyReleased
func _gotk4_gtk4_EventControllerKey_ConnectKeyReleased(arg0 C.gpointer, arg1 C.guint, arg2 C.guint, arg3 C.GdkModifierType, arg4 C.guintptr) {
	var f func(keyval, keycode uint, state gdk.ModifierType)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(keyval, keycode uint, state gdk.ModifierType))
	}

	var _keyval uint            // out
	var _keycode uint           // out
	var _state gdk.ModifierType // out

	_keyval = uint(arg1)
	_keycode = uint(arg2)
	_state = gdk.ModifierType(arg3)

	f(_keyval, _keycode, _state)
}

//export _gotk4_gtk4_EventControllerKey_ConnectModifiers
func _gotk4_gtk4_EventControllerKey_ConnectModifiers(arg0 C.gpointer, arg1 C.GdkModifierType, arg2 C.guintptr) (cret C.gboolean) {
	var f func(state gdk.ModifierType) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(state gdk.ModifierType) (ok bool))
	}

	var _state gdk.ModifierType // out

	_state = gdk.ModifierType(arg1)

	ok := f(_state)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_EventControllerLegacy_ConnectEvent
func _gotk4_gtk4_EventControllerLegacy_ConnectEvent(arg0 C.gpointer, arg1 *C.GdkEvent, arg2 C.guintptr) (cret C.gboolean) {
	var f func(event gdk.Eventer) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(event gdk.Eventer) (ok bool))
	}

	var _event gdk.Eventer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.Eventer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Eventer)
			return ok
		})
		rv, ok := casted.(gdk.Eventer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Eventer")
		}
		_event = rv
	}

	ok := f(_event)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_EventControllerMotion_ConnectEnter
func _gotk4_gtk4_EventControllerMotion_ConnectEnter(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(x, y float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(x, y float64))
	}

	var _x float64 // out
	var _y float64 // out

	_x = float64(arg1)
	_y = float64(arg2)

	f(_x, _y)
}

//export _gotk4_gtk4_EventControllerMotion_ConnectLeave
func _gotk4_gtk4_EventControllerMotion_ConnectLeave(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_EventControllerMotion_ConnectMotion
func _gotk4_gtk4_EventControllerMotion_ConnectMotion(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(x, y float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(x, y float64))
	}

	var _x float64 // out
	var _y float64 // out

	_x = float64(arg1)
	_y = float64(arg2)

	f(_x, _y)
}

//export _gotk4_gtk4_EventControllerScroll_ConnectDecelerate
func _gotk4_gtk4_EventControllerScroll_ConnectDecelerate(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(velX, velY float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(velX, velY float64))
	}

	var _velX float64 // out
	var _velY float64 // out

	_velX = float64(arg1)
	_velY = float64(arg2)

	f(_velX, _velY)
}

//export _gotk4_gtk4_EventControllerScroll_ConnectScroll
func _gotk4_gtk4_EventControllerScroll_ConnectScroll(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) (cret C.gboolean) {
	var f func(dx, dy float64) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(dx, dy float64) (ok bool))
	}

	var _dx float64 // out
	var _dy float64 // out

	_dx = float64(arg1)
	_dy = float64(arg2)

	ok := f(_dx, _dy)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_EventControllerScroll_ConnectScrollBegin
func _gotk4_gtk4_EventControllerScroll_ConnectScrollBegin(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_EventControllerScroll_ConnectScrollEnd
func _gotk4_gtk4_EventControllerScroll_ConnectScrollEnd(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Expander_ConnectActivate
func _gotk4_gtk4_Expander_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FileChooserWidget_ConnectDesktopFolder
func _gotk4_gtk4_FileChooserWidget_ConnectDesktopFolder(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FileChooserWidget_ConnectDownFolder
func _gotk4_gtk4_FileChooserWidget_ConnectDownFolder(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FileChooserWidget_ConnectHomeFolder
func _gotk4_gtk4_FileChooserWidget_ConnectHomeFolder(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FileChooserWidget_ConnectLocationPopup
func _gotk4_gtk4_FileChooserWidget_ConnectLocationPopup(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(path string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(path string))
	}

	var _path string // out

	_path = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_path)
}

//export _gotk4_gtk4_FileChooserWidget_ConnectLocationPopupOnPaste
func _gotk4_gtk4_FileChooserWidget_ConnectLocationPopupOnPaste(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FileChooserWidget_ConnectLocationTogglePopup
func _gotk4_gtk4_FileChooserWidget_ConnectLocationTogglePopup(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FileChooserWidget_ConnectPlacesShortcut
func _gotk4_gtk4_FileChooserWidget_ConnectPlacesShortcut(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FileChooserWidget_ConnectQuickBookmark
func _gotk4_gtk4_FileChooserWidget_ConnectQuickBookmark(arg0 C.gpointer, arg1 C.gint, arg2 C.guintptr) {
	var f func(bookmarkIndex int)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(bookmarkIndex int))
	}

	var _bookmarkIndex int // out

	_bookmarkIndex = int(arg1)

	f(_bookmarkIndex)
}

//export _gotk4_gtk4_FileChooserWidget_ConnectRecentShortcut
func _gotk4_gtk4_FileChooserWidget_ConnectRecentShortcut(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FileChooserWidget_ConnectSearchShortcut
func _gotk4_gtk4_FileChooserWidget_ConnectSearchShortcut(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FileChooserWidget_ConnectShowHidden
func _gotk4_gtk4_FileChooserWidget_ConnectShowHidden(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FileChooserWidget_ConnectUpFolder
func _gotk4_gtk4_FileChooserWidget_ConnectUpFolder(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FilterClass_get_strictness
func _gotk4_gtk4_FilterClass_get_strictness(arg0 *C.GtkFilter) (cret C.GtkFilterMatch) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FilterOverrides](instance0)
	if overrides.Strictness == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FilterOverrides.Strictness, got none")
	}

	filterMatch := overrides.Strictness()

	var _ FilterMatch

	cret = C.GtkFilterMatch(filterMatch)

	return cret
}

//export _gotk4_gtk4_FilterClass_match
func _gotk4_gtk4_FilterClass_match(arg0 *C.GtkFilter, arg1 C.gpointer) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FilterOverrides](instance0)
	if overrides.Match == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FilterOverrides.Match, got none")
	}

	var _item *coreglib.Object // out

	_item = coreglib.Take(unsafe.Pointer(arg1))

	ok := overrides.Match(_item)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Filter_ConnectChanged
func _gotk4_gtk4_Filter_ConnectChanged(arg0 C.gpointer, arg1 C.GtkFilterChange, arg2 C.guintptr) {
	var f func(change FilterChange)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(change FilterChange))
	}

	var _change FilterChange // out

	_change = FilterChange(arg1)

	f(_change)
}

//export _gotk4_gtk4_FlowBox_ConnectActivateCursorChild
func _gotk4_gtk4_FlowBox_ConnectActivateCursorChild(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FlowBox_ConnectChildActivated
func _gotk4_gtk4_FlowBox_ConnectChildActivated(arg0 C.gpointer, arg1 *C.GtkFlowBoxChild, arg2 C.guintptr) {
	var f func(child *FlowBoxChild)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(child *FlowBoxChild))
	}

	var _child *FlowBoxChild // out

	_child = wrapFlowBoxChild(coreglib.Take(unsafe.Pointer(arg1)))

	f(_child)
}

//export _gotk4_gtk4_FlowBox_ConnectMoveCursor
func _gotk4_gtk4_FlowBox_ConnectMoveCursor(arg0 C.gpointer, arg1 C.GtkMovementStep, arg2 C.gint, arg3 C.gboolean, arg4 C.gboolean, arg5 C.guintptr) (cret C.gboolean) {
	var f func(step MovementStep, count int, extend, modify bool) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg5))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(step MovementStep, count int, extend, modify bool) (ok bool))
	}

	var _step MovementStep // out
	var _count int         // out
	var _extend bool       // out
	var _modify bool       // out

	_step = MovementStep(arg1)
	_count = int(arg2)
	if arg3 != 0 {
		_extend = true
	}
	if arg4 != 0 {
		_modify = true
	}

	ok := f(_step, _count, _extend, _modify)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_FlowBox_ConnectSelectAll
func _gotk4_gtk4_FlowBox_ConnectSelectAll(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FlowBox_ConnectSelectedChildrenChanged
func _gotk4_gtk4_FlowBox_ConnectSelectedChildrenChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FlowBox_ConnectToggleCursorChild
func _gotk4_gtk4_FlowBox_ConnectToggleCursorChild(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FlowBox_ConnectUnselectAll
func _gotk4_gtk4_FlowBox_ConnectUnselectAll(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FlowBoxChildClass_activate
func _gotk4_gtk4_FlowBoxChildClass_activate(arg0 *C.GtkFlowBoxChild) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FlowBoxChildOverrides](instance0)
	if overrides.Activate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FlowBoxChildOverrides.Activate, got none")
	}

	overrides.Activate()
}

//export _gotk4_gtk4_FlowBoxChild_ConnectActivate
func _gotk4_gtk4_FlowBoxChild_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FontButton_ConnectActivate
func _gotk4_gtk4_FontButton_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FontButton_ConnectFontSet
func _gotk4_gtk4_FontButton_ConnectFontSet(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FontDialogButton_ConnectActivate
func _gotk4_gtk4_FontDialogButton_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_FrameClass_compute_child_allocation
func _gotk4_gtk4_FrameClass_compute_child_allocation(arg0 *C.GtkFrame, arg1 *C.GtkAllocation) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[FrameOverrides](instance0)
	if overrides.ComputeChildAllocation == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected FrameOverrides.ComputeChildAllocation, got none")
	}

	var _allocation *Allocation // out

	_allocation = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	type _ = *Allocation
	type _ = *gdk.Rectangle

	overrides.ComputeChildAllocation(_allocation)
}

//export _gotk4_gtk4_GLAreaClass_render
func _gotk4_gtk4_GLAreaClass_render(arg0 *C.GtkGLArea, arg1 *C.GdkGLContext) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[GLAreaOverrides](instance0)
	if overrides.Render == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected GLAreaOverrides.Render, got none")
	}

	var _context gdk.GLContexter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.GLContexter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.GLContexter)
			return ok
		})
		rv, ok := casted.(gdk.GLContexter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.GLContexter")
		}
		_context = rv
	}

	ok := overrides.Render(_context)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_GLAreaClass_resize
func _gotk4_gtk4_GLAreaClass_resize(arg0 *C.GtkGLArea, arg1 C.int, arg2 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[GLAreaOverrides](instance0)
	if overrides.Resize == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected GLAreaOverrides.Resize, got none")
	}

	var _width int  // out
	var _height int // out

	_width = int(arg1)
	_height = int(arg2)

	overrides.Resize(_width, _height)
}

//export _gotk4_gtk4_GLArea_ConnectCreateContext
func _gotk4_gtk4_GLArea_ConnectCreateContext(arg0 C.gpointer, arg1 C.guintptr) (cret *C.GdkGLContext) {
	var f func() (glContext gdk.GLContexter)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (glContext gdk.GLContexter))
	}

	glContext := f()

	var _ gdk.GLContexter

	cret = (*C.GdkGLContext)(unsafe.Pointer(coreglib.InternObject(glContext).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(glContext).Native()))

	return cret
}

//export _gotk4_gtk4_GLArea_ConnectRender
func _gotk4_gtk4_GLArea_ConnectRender(arg0 C.gpointer, arg1 *C.GdkGLContext, arg2 C.guintptr) (cret C.gboolean) {
	var f func(context gdk.GLContexter) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(context gdk.GLContexter) (ok bool))
	}

	var _context gdk.GLContexter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.GLContexter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.GLContexter)
			return ok
		})
		rv, ok := casted.(gdk.GLContexter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.GLContexter")
		}
		_context = rv
	}

	ok := f(_context)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_GLArea_ConnectResize
func _gotk4_gtk4_GLArea_ConnectResize(arg0 C.gpointer, arg1 C.gint, arg2 C.gint, arg3 C.guintptr) {
	var f func(width, height int)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(width, height int))
	}

	var _width int  // out
	var _height int // out

	_width = int(arg1)
	_height = int(arg2)

	f(_width, _height)
}

//export _gotk4_gtk4_Gesture_ConnectBegin
func _gotk4_gtk4_Gesture_ConnectBegin(arg0 C.gpointer, arg1 *C.GdkEventSequence, arg2 C.guintptr) {
	var f func(sequence *gdk.EventSequence)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(sequence *gdk.EventSequence))
	}

	var _sequence *gdk.EventSequence // out

	if arg1 != nil {
		_sequence = (*gdk.EventSequence)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	}

	f(_sequence)
}

//export _gotk4_gtk4_Gesture_ConnectCancel
func _gotk4_gtk4_Gesture_ConnectCancel(arg0 C.gpointer, arg1 *C.GdkEventSequence, arg2 C.guintptr) {
	var f func(sequence *gdk.EventSequence)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(sequence *gdk.EventSequence))
	}

	var _sequence *gdk.EventSequence // out

	if arg1 != nil {
		_sequence = (*gdk.EventSequence)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	}

	f(_sequence)
}

//export _gotk4_gtk4_Gesture_ConnectEnd
func _gotk4_gtk4_Gesture_ConnectEnd(arg0 C.gpointer, arg1 *C.GdkEventSequence, arg2 C.guintptr) {
	var f func(sequence *gdk.EventSequence)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(sequence *gdk.EventSequence))
	}

	var _sequence *gdk.EventSequence // out

	if arg1 != nil {
		_sequence = (*gdk.EventSequence)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	}

	f(_sequence)
}

//export _gotk4_gtk4_Gesture_ConnectSequenceStateChanged
func _gotk4_gtk4_Gesture_ConnectSequenceStateChanged(arg0 C.gpointer, arg1 *C.GdkEventSequence, arg2 C.GtkEventSequenceState, arg3 C.guintptr) {
	var f func(sequence *gdk.EventSequence, state EventSequenceState)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(sequence *gdk.EventSequence, state EventSequenceState))
	}

	var _sequence *gdk.EventSequence // out
	var _state EventSequenceState    // out

	if arg1 != nil {
		_sequence = (*gdk.EventSequence)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	}
	_state = EventSequenceState(arg2)

	f(_sequence, _state)
}

//export _gotk4_gtk4_Gesture_ConnectUpdate
func _gotk4_gtk4_Gesture_ConnectUpdate(arg0 C.gpointer, arg1 *C.GdkEventSequence, arg2 C.guintptr) {
	var f func(sequence *gdk.EventSequence)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(sequence *gdk.EventSequence))
	}

	var _sequence *gdk.EventSequence // out

	if arg1 != nil {
		_sequence = (*gdk.EventSequence)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	}

	f(_sequence)
}

//export _gotk4_gtk4_GestureClick_ConnectPressed
func _gotk4_gtk4_GestureClick_ConnectPressed(arg0 C.gpointer, arg1 C.gint, arg2 C.gdouble, arg3 C.gdouble, arg4 C.guintptr) {
	var f func(nPress int, x, y float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(nPress int, x, y float64))
	}

	var _nPress int // out
	var _x float64  // out
	var _y float64  // out

	_nPress = int(arg1)
	_x = float64(arg2)
	_y = float64(arg3)

	f(_nPress, _x, _y)
}

//export _gotk4_gtk4_GestureClick_ConnectReleased
func _gotk4_gtk4_GestureClick_ConnectReleased(arg0 C.gpointer, arg1 C.gint, arg2 C.gdouble, arg3 C.gdouble, arg4 C.guintptr) {
	var f func(nPress int, x, y float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(nPress int, x, y float64))
	}

	var _nPress int // out
	var _x float64  // out
	var _y float64  // out

	_nPress = int(arg1)
	_x = float64(arg2)
	_y = float64(arg3)

	f(_nPress, _x, _y)
}

//export _gotk4_gtk4_GestureClick_ConnectStopped
func _gotk4_gtk4_GestureClick_ConnectStopped(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_GestureClick_ConnectUnpairedRelease
func _gotk4_gtk4_GestureClick_ConnectUnpairedRelease(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guint, arg4 *C.GdkEventSequence, arg5 C.guintptr) {
	var f func(x, y float64, button uint, sequence *gdk.EventSequence)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg5))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(x, y float64, button uint, sequence *gdk.EventSequence))
	}

	var _x float64                   // out
	var _y float64                   // out
	var _button uint                 // out
	var _sequence *gdk.EventSequence // out

	_x = float64(arg1)
	_y = float64(arg2)
	_button = uint(arg3)
	if arg4 != nil {
		_sequence = (*gdk.EventSequence)(gextras.NewStructNative(unsafe.Pointer(arg4)))
	}

	f(_x, _y, _button, _sequence)
}

//export _gotk4_gtk4_GestureDrag_ConnectDragBegin
func _gotk4_gtk4_GestureDrag_ConnectDragBegin(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(startX, startY float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(startX, startY float64))
	}

	var _startX float64 // out
	var _startY float64 // out

	_startX = float64(arg1)
	_startY = float64(arg2)

	f(_startX, _startY)
}

//export _gotk4_gtk4_GestureDrag_ConnectDragEnd
func _gotk4_gtk4_GestureDrag_ConnectDragEnd(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(offsetX, offsetY float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(offsetX, offsetY float64))
	}

	var _offsetX float64 // out
	var _offsetY float64 // out

	_offsetX = float64(arg1)
	_offsetY = float64(arg2)

	f(_offsetX, _offsetY)
}

//export _gotk4_gtk4_GestureDrag_ConnectDragUpdate
func _gotk4_gtk4_GestureDrag_ConnectDragUpdate(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(offsetX, offsetY float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(offsetX, offsetY float64))
	}

	var _offsetX float64 // out
	var _offsetY float64 // out

	_offsetX = float64(arg1)
	_offsetY = float64(arg2)

	f(_offsetX, _offsetY)
}

//export _gotk4_gtk4_GestureLongPress_ConnectCancelled
func _gotk4_gtk4_GestureLongPress_ConnectCancelled(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_GestureLongPress_ConnectPressed
func _gotk4_gtk4_GestureLongPress_ConnectPressed(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(x, y float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(x, y float64))
	}

	var _x float64 // out
	var _y float64 // out

	_x = float64(arg1)
	_y = float64(arg2)

	f(_x, _y)
}

//export _gotk4_gtk4_GesturePan_ConnectPan
func _gotk4_gtk4_GesturePan_ConnectPan(arg0 C.gpointer, arg1 C.GtkPanDirection, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(direction PanDirection, offset float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(direction PanDirection, offset float64))
	}

	var _direction PanDirection // out
	var _offset float64         // out

	_direction = PanDirection(arg1)
	_offset = float64(arg2)

	f(_direction, _offset)
}

//export _gotk4_gtk4_GestureRotate_ConnectAngleChanged
func _gotk4_gtk4_GestureRotate_ConnectAngleChanged(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(angle, angleDelta float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(angle, angleDelta float64))
	}

	var _angle float64      // out
	var _angleDelta float64 // out

	_angle = float64(arg1)
	_angleDelta = float64(arg2)

	f(_angle, _angleDelta)
}

//export _gotk4_gtk4_GestureStylus_ConnectDown
func _gotk4_gtk4_GestureStylus_ConnectDown(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(x, y float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(x, y float64))
	}

	var _x float64 // out
	var _y float64 // out

	_x = float64(arg1)
	_y = float64(arg2)

	f(_x, _y)
}

//export _gotk4_gtk4_GestureStylus_ConnectMotion
func _gotk4_gtk4_GestureStylus_ConnectMotion(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(x, y float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(x, y float64))
	}

	var _x float64 // out
	var _y float64 // out

	_x = float64(arg1)
	_y = float64(arg2)

	f(_x, _y)
}

//export _gotk4_gtk4_GestureStylus_ConnectProximity
func _gotk4_gtk4_GestureStylus_ConnectProximity(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(x, y float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(x, y float64))
	}

	var _x float64 // out
	var _y float64 // out

	_x = float64(arg1)
	_y = float64(arg2)

	f(_x, _y)
}

//export _gotk4_gtk4_GestureStylus_ConnectUp
func _gotk4_gtk4_GestureStylus_ConnectUp(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(x, y float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(x, y float64))
	}

	var _x float64 // out
	var _y float64 // out

	_x = float64(arg1)
	_y = float64(arg2)

	f(_x, _y)
}

//export _gotk4_gtk4_GestureSwipe_ConnectSwipe
func _gotk4_gtk4_GestureSwipe_ConnectSwipe(arg0 C.gpointer, arg1 C.gdouble, arg2 C.gdouble, arg3 C.guintptr) {
	var f func(velocityX, velocityY float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(velocityX, velocityY float64))
	}

	var _velocityX float64 // out
	var _velocityY float64 // out

	_velocityX = float64(arg1)
	_velocityY = float64(arg2)

	f(_velocityX, _velocityY)
}

//export _gotk4_gtk4_GestureZoom_ConnectScaleChanged
func _gotk4_gtk4_GestureZoom_ConnectScaleChanged(arg0 C.gpointer, arg1 C.gdouble, arg2 C.guintptr) {
	var f func(scale float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(scale float64))
	}

	var _scale float64 // out

	_scale = float64(arg1)

	f(_scale)
}

//export _gotk4_gtk4_GridView_ConnectActivate
func _gotk4_gtk4_GridView_ConnectActivate(arg0 C.gpointer, arg1 C.guint, arg2 C.guintptr) {
	var f func(position uint)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(position uint))
	}

	var _position uint // out

	_position = uint(arg1)

	f(_position)
}

//export _gotk4_gtk4_IMContextClass_activate_osk
func _gotk4_gtk4_IMContextClass_activate_osk(arg0 *C.GtkIMContext) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.ActivateOSK == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.ActivateOSK, got none")
	}

	overrides.ActivateOSK()
}

//export _gotk4_gtk4_IMContextClass_activate_osk_with_event
func _gotk4_gtk4_IMContextClass_activate_osk_with_event(arg0 *C.GtkIMContext, arg1 *C.GdkEvent) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.ActivateOSKWithEvent == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.ActivateOSKWithEvent, got none")
	}

	var _event gdk.Eventer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.Eventer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Eventer)
			return ok
		})
		rv, ok := casted.(gdk.Eventer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Eventer")
		}
		_event = rv
	}

	ok := overrides.ActivateOSKWithEvent(_event)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_IMContextClass_commit
func _gotk4_gtk4_IMContextClass_commit(arg0 *C.GtkIMContext, arg1 *C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.Commit == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.Commit, got none")
	}

	var _str string // out

	_str = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	overrides.Commit(_str)
}

//export _gotk4_gtk4_IMContextClass_delete_surrounding
func _gotk4_gtk4_IMContextClass_delete_surrounding(arg0 *C.GtkIMContext, arg1 C.int, arg2 C.int) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.DeleteSurrounding == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.DeleteSurrounding, got none")
	}

	var _offset int // out
	var _nChars int // out

	_offset = int(arg1)
	_nChars = int(arg2)

	ok := overrides.DeleteSurrounding(_offset, _nChars)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_IMContextClass_filter_keypress
func _gotk4_gtk4_IMContextClass_filter_keypress(arg0 *C.GtkIMContext, arg1 *C.GdkEvent) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.FilterKeypress == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.FilterKeypress, got none")
	}

	var _event gdk.Eventer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.Eventer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Eventer)
			return ok
		})
		rv, ok := casted.(gdk.Eventer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Eventer")
		}
		_event = rv
	}

	ok := overrides.FilterKeypress(_event)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_IMContextClass_focus_in
func _gotk4_gtk4_IMContextClass_focus_in(arg0 *C.GtkIMContext) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.FocusIn == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.FocusIn, got none")
	}

	overrides.FocusIn()
}

//export _gotk4_gtk4_IMContextClass_focus_out
func _gotk4_gtk4_IMContextClass_focus_out(arg0 *C.GtkIMContext) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.FocusOut == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.FocusOut, got none")
	}

	overrides.FocusOut()
}

//export _gotk4_gtk4_IMContextClass_get_preedit_string
func _gotk4_gtk4_IMContextClass_get_preedit_string(arg0 *C.GtkIMContext, arg1 **C.char, arg2 **C.PangoAttrList, arg3 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.PreeditString == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.PreeditString, got none")
	}

	str, attrs, cursorPos := overrides.PreeditString()

	var _ string
	var _ *pango.AttrList
	var _ int

	*arg1 = (*C.char)(unsafe.Pointer(C.CString(str)))
	*arg2 = (*C.PangoAttrList)(gextras.StructNative(unsafe.Pointer(attrs)))
	*arg3 = C.int(cursorPos)
}

//export _gotk4_gtk4_IMContextClass_get_surrounding
func _gotk4_gtk4_IMContextClass_get_surrounding(arg0 *C.GtkIMContext, arg1 **C.char, arg2 *C.int) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.Surrounding == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.Surrounding, got none")
	}

	text, cursorIndex, ok := overrides.Surrounding()

	var _ string
	var _ int
	var _ bool

	*arg1 = (*C.char)(unsafe.Pointer(C.CString(text)))
	*arg2 = C.int(cursorIndex)
	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_IMContextClass_get_surrounding_with_selection
func _gotk4_gtk4_IMContextClass_get_surrounding_with_selection(arg0 *C.GtkIMContext, arg1 **C.char, arg2 *C.int, arg3 *C.int) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.SurroundingWithSelection == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.SurroundingWithSelection, got none")
	}

	text, cursorIndex, anchorIndex, ok := overrides.SurroundingWithSelection()

	var _ string
	var _ int
	var _ int
	var _ bool

	*arg1 = (*C.char)(unsafe.Pointer(C.CString(text)))
	*arg2 = C.int(cursorIndex)
	*arg3 = C.int(anchorIndex)
	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_IMContextClass_preedit_changed
func _gotk4_gtk4_IMContextClass_preedit_changed(arg0 *C.GtkIMContext) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.PreeditChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.PreeditChanged, got none")
	}

	overrides.PreeditChanged()
}

//export _gotk4_gtk4_IMContextClass_preedit_end
func _gotk4_gtk4_IMContextClass_preedit_end(arg0 *C.GtkIMContext) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.PreeditEnd == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.PreeditEnd, got none")
	}

	overrides.PreeditEnd()
}

//export _gotk4_gtk4_IMContextClass_preedit_start
func _gotk4_gtk4_IMContextClass_preedit_start(arg0 *C.GtkIMContext) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.PreeditStart == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.PreeditStart, got none")
	}

	overrides.PreeditStart()
}

//export _gotk4_gtk4_IMContextClass_reset
func _gotk4_gtk4_IMContextClass_reset(arg0 *C.GtkIMContext) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.Reset == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.Reset, got none")
	}

	overrides.Reset()
}

//export _gotk4_gtk4_IMContextClass_retrieve_surrounding
func _gotk4_gtk4_IMContextClass_retrieve_surrounding(arg0 *C.GtkIMContext) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.RetrieveSurrounding == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.RetrieveSurrounding, got none")
	}

	ok := overrides.RetrieveSurrounding()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_IMContextClass_set_client_widget
func _gotk4_gtk4_IMContextClass_set_client_widget(arg0 *C.GtkIMContext, arg1 *C.GtkWidget) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.SetClientWidget == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.SetClientWidget, got none")
	}

	var _widget Widgetter // out

	if arg1 != nil {
		{
			objptr := unsafe.Pointer(arg1)

			object := coreglib.Take(objptr)
			casted := object.WalkCast(func(obj coreglib.Objector) bool {
				_, ok := obj.(Widgetter)
				return ok
			})
			rv, ok := casted.(Widgetter)
			if !ok {
				panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
			}
			_widget = rv
		}
	}

	overrides.SetClientWidget(_widget)
}

//export _gotk4_gtk4_IMContextClass_set_cursor_location
func _gotk4_gtk4_IMContextClass_set_cursor_location(arg0 *C.GtkIMContext, arg1 *C.GdkRectangle) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.SetCursorLocation == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.SetCursorLocation, got none")
	}

	var _area *gdk.Rectangle // out

	_area = (*gdk.Rectangle)(gextras.NewStructNative(unsafe.Pointer(arg1)))

	overrides.SetCursorLocation(_area)
}

//export _gotk4_gtk4_IMContextClass_set_surrounding
func _gotk4_gtk4_IMContextClass_set_surrounding(arg0 *C.GtkIMContext, arg1 *C.char, arg2 C.int, arg3 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.SetSurrounding == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.SetSurrounding, got none")
	}

	var _text string
	var _cursorIndex int // out

	_text = C.GoStringN(arg1, C.int(arg2))
	_cursorIndex = int(arg3)

	overrides.SetSurrounding(_text, _cursorIndex)
}

//export _gotk4_gtk4_IMContextClass_set_surrounding_with_selection
func _gotk4_gtk4_IMContextClass_set_surrounding_with_selection(arg0 *C.GtkIMContext, arg1 *C.char, arg2 C.int, arg3 C.int, arg4 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.SetSurroundingWithSelection == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.SetSurroundingWithSelection, got none")
	}

	var _text string
	var _cursorIndex int // out
	var _anchorIndex int // out

	_text = C.GoStringN(arg1, C.int(arg2))
	_cursorIndex = int(arg3)
	_anchorIndex = int(arg4)

	overrides.SetSurroundingWithSelection(_text, _cursorIndex, _anchorIndex)
}

//export _gotk4_gtk4_IMContextClass_set_use_preedit
func _gotk4_gtk4_IMContextClass_set_use_preedit(arg0 *C.GtkIMContext, arg1 C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[IMContextOverrides](instance0)
	if overrides.SetUsePreedit == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected IMContextOverrides.SetUsePreedit, got none")
	}

	var _usePreedit bool // out

	if arg1 != 0 {
		_usePreedit = true
	}

	overrides.SetUsePreedit(_usePreedit)
}

//export _gotk4_gtk4_IMContext_ConnectCommit
func _gotk4_gtk4_IMContext_ConnectCommit(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(str string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(str string))
	}

	var _str string // out

	_str = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_str)
}

//export _gotk4_gtk4_IMContext_ConnectDeleteSurrounding
func _gotk4_gtk4_IMContext_ConnectDeleteSurrounding(arg0 C.gpointer, arg1 C.gint, arg2 C.gint, arg3 C.guintptr) (cret C.gboolean) {
	var f func(offset, nChars int) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(offset, nChars int) (ok bool))
	}

	var _offset int // out
	var _nChars int // out

	_offset = int(arg1)
	_nChars = int(arg2)

	ok := f(_offset, _nChars)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_IMContext_ConnectPreeditChanged
func _gotk4_gtk4_IMContext_ConnectPreeditChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_IMContext_ConnectPreeditEnd
func _gotk4_gtk4_IMContext_ConnectPreeditEnd(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_IMContext_ConnectPreeditStart
func _gotk4_gtk4_IMContext_ConnectPreeditStart(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_IMContext_ConnectRetrieveSurrounding
func _gotk4_gtk4_IMContext_ConnectRetrieveSurrounding(arg0 C.gpointer, arg1 C.guintptr) (cret C.gboolean) {
	var f func() (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (ok bool))
	}

	ok := f()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_IconTheme_ConnectChanged
func _gotk4_gtk4_IconTheme_ConnectChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_IconView_ConnectActivateCursorItem
func _gotk4_gtk4_IconView_ConnectActivateCursorItem(arg0 C.gpointer, arg1 C.guintptr) (cret C.gboolean) {
	var f func() (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (ok bool))
	}

	ok := f()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_IconView_ConnectItemActivated
func _gotk4_gtk4_IconView_ConnectItemActivated(arg0 C.gpointer, arg1 *C.GtkTreePath, arg2 C.guintptr) {
	var f func(path *TreePath)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(path *TreePath))
	}

	var _path *TreePath // out

	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg1)))

	f(_path)
}

//export _gotk4_gtk4_IconView_ConnectMoveCursor
func _gotk4_gtk4_IconView_ConnectMoveCursor(arg0 C.gpointer, arg1 C.GtkMovementStep, arg2 C.gint, arg3 C.gboolean, arg4 C.gboolean, arg5 C.guintptr) (cret C.gboolean) {
	var f func(step MovementStep, count int, extend, modify bool) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg5))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(step MovementStep, count int, extend, modify bool) (ok bool))
	}

	var _step MovementStep // out
	var _count int         // out
	var _extend bool       // out
	var _modify bool       // out

	_step = MovementStep(arg1)
	_count = int(arg2)
	if arg3 != 0 {
		_extend = true
	}
	if arg4 != 0 {
		_modify = true
	}

	ok := f(_step, _count, _extend, _modify)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_IconView_ConnectSelectAll
func _gotk4_gtk4_IconView_ConnectSelectAll(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_IconView_ConnectSelectCursorItem
func _gotk4_gtk4_IconView_ConnectSelectCursorItem(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_IconView_ConnectSelectionChanged
func _gotk4_gtk4_IconView_ConnectSelectionChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_IconView_ConnectToggleCursorItem
func _gotk4_gtk4_IconView_ConnectToggleCursorItem(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_IconView_ConnectUnselectAll
func _gotk4_gtk4_IconView_ConnectUnselectAll(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_InfoBar_ConnectClose
func _gotk4_gtk4_InfoBar_ConnectClose(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_InfoBar_ConnectResponse
func _gotk4_gtk4_InfoBar_ConnectResponse(arg0 C.gpointer, arg1 C.gint, arg2 C.guintptr) {
	var f func(responseId int)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(responseId int))
	}

	var _responseId int // out

	_responseId = int(arg1)

	f(_responseId)
}

//export _gotk4_gtk4_Label_ConnectActivateCurrentLink
func _gotk4_gtk4_Label_ConnectActivateCurrentLink(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Label_ConnectActivateLink
func _gotk4_gtk4_Label_ConnectActivateLink(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) (cret C.gboolean) {
	var f func(uri string) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(uri string) (ok bool))
	}

	var _uri string // out

	_uri = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	ok := f(_uri)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Label_ConnectCopyClipboard
func _gotk4_gtk4_Label_ConnectCopyClipboard(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Label_ConnectMoveCursor
func _gotk4_gtk4_Label_ConnectMoveCursor(arg0 C.gpointer, arg1 C.GtkMovementStep, arg2 C.gint, arg3 C.gboolean, arg4 C.guintptr) {
	var f func(step MovementStep, count int, extendSelection bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(step MovementStep, count int, extendSelection bool))
	}

	var _step MovementStep    // out
	var _count int            // out
	var _extendSelection bool // out

	_step = MovementStep(arg1)
	_count = int(arg2)
	if arg3 != 0 {
		_extendSelection = true
	}

	f(_step, _count, _extendSelection)
}

//export _gotk4_gtk4_LayoutManagerClass_allocate
func _gotk4_gtk4_LayoutManagerClass_allocate(arg0 *C.GtkLayoutManager, arg1 *C.GtkWidget, arg2 C.int, arg3 C.int, arg4 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[LayoutManagerOverrides](instance0)
	if overrides.Allocate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected LayoutManagerOverrides.Allocate, got none")
	}

	var _widget Widgetter // out
	var _width int        // out
	var _height int       // out
	var _baseline int     // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	_width = int(arg2)
	_height = int(arg3)
	_baseline = int(arg4)

	overrides.Allocate(_widget, _width, _height, _baseline)
}

//export _gotk4_gtk4_LayoutManagerClass_create_layout_child
func _gotk4_gtk4_LayoutManagerClass_create_layout_child(arg0 *C.GtkLayoutManager, arg1 *C.GtkWidget, arg2 *C.GtkWidget) (cret *C.GtkLayoutChild) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[LayoutManagerOverrides](instance0)
	if overrides.CreateLayoutChild == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected LayoutManagerOverrides.CreateLayoutChild, got none")
	}

	var _widget Widgetter   // out
	var _forChild Widgetter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_forChild = rv
	}

	layoutChild := overrides.CreateLayoutChild(_widget, _forChild)

	var _ LayoutChilder

	cret = (*C.GtkLayoutChild)(unsafe.Pointer(coreglib.InternObject(layoutChild).Native()))
	C.g_object_ref(C.gpointer(coreglib.InternObject(layoutChild).Native()))

	return cret
}

//export _gotk4_gtk4_LayoutManagerClass_get_request_mode
func _gotk4_gtk4_LayoutManagerClass_get_request_mode(arg0 *C.GtkLayoutManager, arg1 *C.GtkWidget) (cret C.GtkSizeRequestMode) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[LayoutManagerOverrides](instance0)
	if overrides.RequestMode == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected LayoutManagerOverrides.RequestMode, got none")
	}

	var _widget Widgetter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}

	sizeRequestMode := overrides.RequestMode(_widget)

	var _ SizeRequestMode

	cret = C.GtkSizeRequestMode(sizeRequestMode)

	return cret
}

//export _gotk4_gtk4_LayoutManagerClass_measure
func _gotk4_gtk4_LayoutManagerClass_measure(arg0 *C.GtkLayoutManager, arg1 *C.GtkWidget, arg2 C.GtkOrientation, arg3 C.int, arg4 *C.int, arg5 *C.int, arg6 *C.int, arg7 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[LayoutManagerOverrides](instance0)
	if overrides.Measure == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected LayoutManagerOverrides.Measure, got none")
	}

	var _widget Widgetter        // out
	var _orientation Orientation // out
	var _forSize int             // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	_orientation = Orientation(arg2)
	_forSize = int(arg3)

	minimum, natural, minimumBaseline, naturalBaseline := overrides.Measure(_widget, _orientation, _forSize)

	var _ int
	var _ int
	var _ int
	var _ int

	*arg4 = C.int(minimum)
	*arg5 = C.int(natural)
	*arg6 = C.int(minimumBaseline)
	*arg7 = C.int(naturalBaseline)
}

//export _gotk4_gtk4_LayoutManagerClass_root
func _gotk4_gtk4_LayoutManagerClass_root(arg0 *C.GtkLayoutManager) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[LayoutManagerOverrides](instance0)
	if overrides.Root == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected LayoutManagerOverrides.Root, got none")
	}

	overrides.Root()
}

//export _gotk4_gtk4_LayoutManagerClass_unroot
func _gotk4_gtk4_LayoutManagerClass_unroot(arg0 *C.GtkLayoutManager) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[LayoutManagerOverrides](instance0)
	if overrides.Unroot == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected LayoutManagerOverrides.Unroot, got none")
	}

	overrides.Unroot()
}

//export _gotk4_gtk4_LevelBar_ConnectOffsetChanged
func _gotk4_gtk4_LevelBar_ConnectOffsetChanged(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(name string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(name string))
	}

	var _name string // out

	_name = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_name)
}

//export _gotk4_gtk4_LinkButton_ConnectActivateLink
func _gotk4_gtk4_LinkButton_ConnectActivateLink(arg0 C.gpointer, arg1 C.guintptr) (cret C.gboolean) {
	var f func() (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (ok bool))
	}

	ok := f()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_ListBox_ConnectActivateCursorRow
func _gotk4_gtk4_ListBox_ConnectActivateCursorRow(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ListBox_ConnectMoveCursor
func _gotk4_gtk4_ListBox_ConnectMoveCursor(arg0 C.gpointer, arg1 C.GtkMovementStep, arg2 C.gint, arg3 C.gboolean, arg4 C.gboolean, arg5 C.guintptr) {
	var f func(object MovementStep, p0 int, p1, p2 bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg5))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object MovementStep, p0 int, p1, p2 bool))
	}

	var _object MovementStep // out
	var _p0 int              // out
	var _p1 bool             // out
	var _p2 bool             // out

	_object = MovementStep(arg1)
	_p0 = int(arg2)
	if arg3 != 0 {
		_p1 = true
	}
	if arg4 != 0 {
		_p2 = true
	}

	f(_object, _p0, _p1, _p2)
}

//export _gotk4_gtk4_ListBox_ConnectRowActivated
func _gotk4_gtk4_ListBox_ConnectRowActivated(arg0 C.gpointer, arg1 *C.GtkListBoxRow, arg2 C.guintptr) {
	var f func(row *ListBoxRow)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(row *ListBoxRow))
	}

	var _row *ListBoxRow // out

	_row = wrapListBoxRow(coreglib.Take(unsafe.Pointer(arg1)))

	f(_row)
}

//export _gotk4_gtk4_ListBox_ConnectRowSelected
func _gotk4_gtk4_ListBox_ConnectRowSelected(arg0 C.gpointer, arg1 *C.GtkListBoxRow, arg2 C.guintptr) {
	var f func(row *ListBoxRow)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(row *ListBoxRow))
	}

	var _row *ListBoxRow // out

	if arg1 != nil {
		_row = wrapListBoxRow(coreglib.Take(unsafe.Pointer(arg1)))
	}

	f(_row)
}

//export _gotk4_gtk4_ListBox_ConnectSelectAll
func _gotk4_gtk4_ListBox_ConnectSelectAll(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ListBox_ConnectSelectedRowsChanged
func _gotk4_gtk4_ListBox_ConnectSelectedRowsChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ListBox_ConnectToggleCursorRow
func _gotk4_gtk4_ListBox_ConnectToggleCursorRow(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ListBox_ConnectUnselectAll
func _gotk4_gtk4_ListBox_ConnectUnselectAll(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ListBoxRowClass_activate
func _gotk4_gtk4_ListBoxRowClass_activate(arg0 *C.GtkListBoxRow) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ListBoxRowOverrides](instance0)
	if overrides.Activate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ListBoxRowOverrides.Activate, got none")
	}

	overrides.Activate()
}

//export _gotk4_gtk4_ListBoxRow_ConnectActivate
func _gotk4_gtk4_ListBoxRow_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ListView_ConnectActivate
func _gotk4_gtk4_ListView_ConnectActivate(arg0 C.gpointer, arg1 C.guint, arg2 C.guintptr) {
	var f func(position uint)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(position uint))
	}

	var _position uint // out

	_position = uint(arg1)

	f(_position)
}

//export _gotk4_gtk4_MediaFileClass_close
func _gotk4_gtk4_MediaFileClass_close(arg0 *C.GtkMediaFile) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MediaFileOverrides](instance0)
	if overrides.Close == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MediaFileOverrides.Close, got none")
	}

	overrides.Close()
}

//export _gotk4_gtk4_MediaFileClass_open
func _gotk4_gtk4_MediaFileClass_open(arg0 *C.GtkMediaFile) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MediaFileOverrides](instance0)
	if overrides.Open == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MediaFileOverrides.Open, got none")
	}

	overrides.Open()
}

//export _gotk4_gtk4_MediaStreamClass_pause
func _gotk4_gtk4_MediaStreamClass_pause(arg0 *C.GtkMediaStream) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MediaStreamOverrides](instance0)
	if overrides.Pause == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MediaStreamOverrides.Pause, got none")
	}

	overrides.Pause()
}

//export _gotk4_gtk4_MediaStreamClass_play
func _gotk4_gtk4_MediaStreamClass_play(arg0 *C.GtkMediaStream) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MediaStreamOverrides](instance0)
	if overrides.Play == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MediaStreamOverrides.Play, got none")
	}

	ok := overrides.Play()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_MediaStreamClass_realize
func _gotk4_gtk4_MediaStreamClass_realize(arg0 *C.GtkMediaStream, arg1 *C.GdkSurface) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MediaStreamOverrides](instance0)
	if overrides.Realize == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MediaStreamOverrides.Realize, got none")
	}

	var _surface gdk.Surfacer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.Surfacer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Surfacer)
			return ok
		})
		rv, ok := casted.(gdk.Surfacer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Surfacer")
		}
		_surface = rv
	}

	overrides.Realize(_surface)
}

//export _gotk4_gtk4_MediaStreamClass_seek
func _gotk4_gtk4_MediaStreamClass_seek(arg0 *C.GtkMediaStream, arg1 C.gint64) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MediaStreamOverrides](instance0)
	if overrides.Seek == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MediaStreamOverrides.Seek, got none")
	}

	var _timestamp int64 // out

	_timestamp = int64(arg1)

	overrides.Seek(_timestamp)
}

//export _gotk4_gtk4_MediaStreamClass_unrealize
func _gotk4_gtk4_MediaStreamClass_unrealize(arg0 *C.GtkMediaStream, arg1 *C.GdkSurface) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MediaStreamOverrides](instance0)
	if overrides.Unrealize == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MediaStreamOverrides.Unrealize, got none")
	}

	var _surface gdk.Surfacer // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gdk.Surfacer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Surfacer)
			return ok
		})
		rv, ok := casted.(gdk.Surfacer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Surfacer")
		}
		_surface = rv
	}

	overrides.Unrealize(_surface)
}

//export _gotk4_gtk4_MediaStreamClass_update_audio
func _gotk4_gtk4_MediaStreamClass_update_audio(arg0 *C.GtkMediaStream, arg1 C.gboolean, arg2 C.double) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[MediaStreamOverrides](instance0)
	if overrides.UpdateAudio == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected MediaStreamOverrides.UpdateAudio, got none")
	}

	var _muted bool     // out
	var _volume float64 // out

	if arg1 != 0 {
		_muted = true
	}
	_volume = float64(arg2)

	overrides.UpdateAudio(_muted, _volume)
}

//export _gotk4_gtk4_MenuButton_ConnectActivate
func _gotk4_gtk4_MenuButton_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_NativeDialogClass_hide
func _gotk4_gtk4_NativeDialogClass_hide(arg0 *C.GtkNativeDialog) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[NativeDialogOverrides](instance0)
	if overrides.Hide == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected NativeDialogOverrides.Hide, got none")
	}

	overrides.Hide()
}

//export _gotk4_gtk4_NativeDialogClass_response
func _gotk4_gtk4_NativeDialogClass_response(arg0 *C.GtkNativeDialog, arg1 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[NativeDialogOverrides](instance0)
	if overrides.Response == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected NativeDialogOverrides.Response, got none")
	}

	var _responseId int // out

	_responseId = int(arg1)

	overrides.Response(_responseId)
}

//export _gotk4_gtk4_NativeDialogClass_show
func _gotk4_gtk4_NativeDialogClass_show(arg0 *C.GtkNativeDialog) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[NativeDialogOverrides](instance0)
	if overrides.Show == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected NativeDialogOverrides.Show, got none")
	}

	overrides.Show()
}

//export _gotk4_gtk4_NativeDialog_ConnectResponse
func _gotk4_gtk4_NativeDialog_ConnectResponse(arg0 C.gpointer, arg1 C.gint, arg2 C.guintptr) {
	var f func(responseId int)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(responseId int))
	}

	var _responseId int // out

	_responseId = int(arg1)

	f(_responseId)
}

//export _gotk4_gtk4_Notebook_ConnectChangeCurrentPage
func _gotk4_gtk4_Notebook_ConnectChangeCurrentPage(arg0 C.gpointer, arg1 C.gint, arg2 C.guintptr) (cret C.gboolean) {
	var f func(object int) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object int) (ok bool))
	}

	var _object int // out

	_object = int(arg1)

	ok := f(_object)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Notebook_ConnectCreateWindow
func _gotk4_gtk4_Notebook_ConnectCreateWindow(arg0 C.gpointer, arg1 *C.GtkWidget, arg2 C.guintptr) (cret *C.GtkNotebook) {
	var f func(page Widgetter) (notebook *Notebook)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(page Widgetter) (notebook *Notebook))
	}

	var _page Widgetter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_page = rv
	}

	notebook := f(_page)

	var _ *Notebook

	if notebook != nil {
		cret = (*C.GtkNotebook)(unsafe.Pointer(coreglib.InternObject(notebook).Native()))
	}

	return cret
}

//export _gotk4_gtk4_Notebook_ConnectFocusTab
func _gotk4_gtk4_Notebook_ConnectFocusTab(arg0 C.gpointer, arg1 C.GtkNotebookTab, arg2 C.guintptr) (cret C.gboolean) {
	var f func(object NotebookTab) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object NotebookTab) (ok bool))
	}

	var _object NotebookTab // out

	_object = NotebookTab(arg1)

	ok := f(_object)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Notebook_ConnectMoveFocusOut
func _gotk4_gtk4_Notebook_ConnectMoveFocusOut(arg0 C.gpointer, arg1 C.GtkDirectionType, arg2 C.guintptr) {
	var f func(object DirectionType)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object DirectionType))
	}

	var _object DirectionType // out

	_object = DirectionType(arg1)

	f(_object)
}

//export _gotk4_gtk4_Notebook_ConnectPageAdded
func _gotk4_gtk4_Notebook_ConnectPageAdded(arg0 C.gpointer, arg1 *C.GtkWidget, arg2 C.guint, arg3 C.guintptr) {
	var f func(child Widgetter, pageNum uint)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(child Widgetter, pageNum uint))
	}

	var _child Widgetter // out
	var _pageNum uint    // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_child = rv
	}
	_pageNum = uint(arg2)

	f(_child, _pageNum)
}

//export _gotk4_gtk4_Notebook_ConnectPageRemoved
func _gotk4_gtk4_Notebook_ConnectPageRemoved(arg0 C.gpointer, arg1 *C.GtkWidget, arg2 C.guint, arg3 C.guintptr) {
	var f func(child Widgetter, pageNum uint)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(child Widgetter, pageNum uint))
	}

	var _child Widgetter // out
	var _pageNum uint    // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_child = rv
	}
	_pageNum = uint(arg2)

	f(_child, _pageNum)
}

//export _gotk4_gtk4_Notebook_ConnectPageReordered
func _gotk4_gtk4_Notebook_ConnectPageReordered(arg0 C.gpointer, arg1 *C.GtkWidget, arg2 C.guint, arg3 C.guintptr) {
	var f func(child Widgetter, pageNum uint)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(child Widgetter, pageNum uint))
	}

	var _child Widgetter // out
	var _pageNum uint    // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_child = rv
	}
	_pageNum = uint(arg2)

	f(_child, _pageNum)
}

//export _gotk4_gtk4_Notebook_ConnectReorderTab
func _gotk4_gtk4_Notebook_ConnectReorderTab(arg0 C.gpointer, arg1 C.GtkDirectionType, arg2 C.gboolean, arg3 C.guintptr) (cret C.gboolean) {
	var f func(object DirectionType, p0 bool) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object DirectionType, p0 bool) (ok bool))
	}

	var _object DirectionType // out
	var _p0 bool              // out

	_object = DirectionType(arg1)
	if arg2 != 0 {
		_p0 = true
	}

	ok := f(_object, _p0)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Notebook_ConnectSelectPage
func _gotk4_gtk4_Notebook_ConnectSelectPage(arg0 C.gpointer, arg1 C.gboolean, arg2 C.guintptr) (cret C.gboolean) {
	var f func(object bool) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object bool) (ok bool))
	}

	var _object bool // out

	if arg1 != 0 {
		_object = true
	}

	ok := f(_object)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Notebook_ConnectSwitchPage
func _gotk4_gtk4_Notebook_ConnectSwitchPage(arg0 C.gpointer, arg1 *C.GtkWidget, arg2 C.guint, arg3 C.guintptr) {
	var f func(page Widgetter, pageNum uint)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(page Widgetter, pageNum uint))
	}

	var _page Widgetter // out
	var _pageNum uint   // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_page = rv
	}
	_pageNum = uint(arg2)

	f(_page, _pageNum)
}

//export _gotk4_gtk4_Overlay_ConnectGetChildPosition
func _gotk4_gtk4_Overlay_ConnectGetChildPosition(arg0 C.gpointer, arg1 *C.GtkWidget, arg2 *C.GdkRectangle, arg3 C.guintptr) (cret C.gboolean) {
	var f func(widget Widgetter) (allocation *gdk.Rectangle, ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(widget Widgetter) (allocation *gdk.Rectangle, ok bool))
	}

	var _widget Widgetter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}

	allocation, ok := f(_widget)

	var _ *gdk.Rectangle
	var _ bool

	*arg2 = *(*C.GdkRectangle)(gextras.StructNative(unsafe.Pointer(allocation)))
	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Paned_ConnectAcceptPosition
func _gotk4_gtk4_Paned_ConnectAcceptPosition(arg0 C.gpointer, arg1 C.guintptr) (cret C.gboolean) {
	var f func() (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (ok bool))
	}

	ok := f()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Paned_ConnectCancelPosition
func _gotk4_gtk4_Paned_ConnectCancelPosition(arg0 C.gpointer, arg1 C.guintptr) (cret C.gboolean) {
	var f func() (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (ok bool))
	}

	ok := f()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Paned_ConnectCycleChildFocus
func _gotk4_gtk4_Paned_ConnectCycleChildFocus(arg0 C.gpointer, arg1 C.gboolean, arg2 C.guintptr) (cret C.gboolean) {
	var f func(reversed bool) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(reversed bool) (ok bool))
	}

	var _reversed bool // out

	if arg1 != 0 {
		_reversed = true
	}

	ok := f(_reversed)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Paned_ConnectCycleHandleFocus
func _gotk4_gtk4_Paned_ConnectCycleHandleFocus(arg0 C.gpointer, arg1 C.gboolean, arg2 C.guintptr) (cret C.gboolean) {
	var f func(reversed bool) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(reversed bool) (ok bool))
	}

	var _reversed bool // out

	if arg1 != 0 {
		_reversed = true
	}

	ok := f(_reversed)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Paned_ConnectMoveHandle
func _gotk4_gtk4_Paned_ConnectMoveHandle(arg0 C.gpointer, arg1 C.GtkScrollType, arg2 C.guintptr) (cret C.gboolean) {
	var f func(scrollType ScrollType) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(scrollType ScrollType) (ok bool))
	}

	var _scrollType ScrollType // out

	_scrollType = ScrollType(arg1)

	ok := f(_scrollType)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Paned_ConnectToggleHandleFocus
func _gotk4_gtk4_Paned_ConnectToggleHandleFocus(arg0 C.gpointer, arg1 C.guintptr) (cret C.gboolean) {
	var f func() (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (ok bool))
	}

	ok := f()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_PasswordEntry_ConnectActivate
func _gotk4_gtk4_PasswordEntry_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_PopoverClass_activate_default
func _gotk4_gtk4_PopoverClass_activate_default(arg0 *C.GtkPopover) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PopoverOverrides](instance0)
	if overrides.ActivateDefault == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PopoverOverrides.ActivateDefault, got none")
	}

	overrides.ActivateDefault()
}

//export _gotk4_gtk4_PopoverClass_closed
func _gotk4_gtk4_PopoverClass_closed(arg0 *C.GtkPopover) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PopoverOverrides](instance0)
	if overrides.Closed == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PopoverOverrides.Closed, got none")
	}

	overrides.Closed()
}

//export _gotk4_gtk4_Popover_ConnectActivateDefault
func _gotk4_gtk4_Popover_ConnectActivateDefault(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Popover_ConnectClosed
func _gotk4_gtk4_Popover_ConnectClosed(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_PrintOperationClass_begin_print
func _gotk4_gtk4_PrintOperationClass_begin_print(arg0 *C.GtkPrintOperation, arg1 *C.GtkPrintContext) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PrintOperationOverrides](instance0)
	if overrides.BeginPrint == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PrintOperationOverrides.BeginPrint, got none")
	}

	var _context *PrintContext // out

	_context = wrapPrintContext(coreglib.Take(unsafe.Pointer(arg1)))

	overrides.BeginPrint(_context)
}

//export _gotk4_gtk4_PrintOperationClass_custom_widget_apply
func _gotk4_gtk4_PrintOperationClass_custom_widget_apply(arg0 *C.GtkPrintOperation, arg1 *C.GtkWidget) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PrintOperationOverrides](instance0)
	if overrides.CustomWidgetApply == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PrintOperationOverrides.CustomWidgetApply, got none")
	}

	var _widget Widgetter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}

	overrides.CustomWidgetApply(_widget)
}

//export _gotk4_gtk4_PrintOperationClass_done
func _gotk4_gtk4_PrintOperationClass_done(arg0 *C.GtkPrintOperation, arg1 C.GtkPrintOperationResult) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PrintOperationOverrides](instance0)
	if overrides.Done == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PrintOperationOverrides.Done, got none")
	}

	var _result PrintOperationResult // out

	_result = PrintOperationResult(arg1)

	overrides.Done(_result)
}

//export _gotk4_gtk4_PrintOperationClass_draw_page
func _gotk4_gtk4_PrintOperationClass_draw_page(arg0 *C.GtkPrintOperation, arg1 *C.GtkPrintContext, arg2 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PrintOperationOverrides](instance0)
	if overrides.DrawPage == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PrintOperationOverrides.DrawPage, got none")
	}

	var _context *PrintContext // out
	var _pageNr int            // out

	_context = wrapPrintContext(coreglib.Take(unsafe.Pointer(arg1)))
	_pageNr = int(arg2)

	overrides.DrawPage(_context, _pageNr)
}

//export _gotk4_gtk4_PrintOperationClass_end_print
func _gotk4_gtk4_PrintOperationClass_end_print(arg0 *C.GtkPrintOperation, arg1 *C.GtkPrintContext) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PrintOperationOverrides](instance0)
	if overrides.EndPrint == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PrintOperationOverrides.EndPrint, got none")
	}

	var _context *PrintContext // out

	_context = wrapPrintContext(coreglib.Take(unsafe.Pointer(arg1)))

	overrides.EndPrint(_context)
}

//export _gotk4_gtk4_PrintOperationClass_paginate
func _gotk4_gtk4_PrintOperationClass_paginate(arg0 *C.GtkPrintOperation, arg1 *C.GtkPrintContext) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PrintOperationOverrides](instance0)
	if overrides.Paginate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PrintOperationOverrides.Paginate, got none")
	}

	var _context *PrintContext // out

	_context = wrapPrintContext(coreglib.Take(unsafe.Pointer(arg1)))

	ok := overrides.Paginate(_context)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_PrintOperationClass_preview
func _gotk4_gtk4_PrintOperationClass_preview(arg0 *C.GtkPrintOperation, arg1 *C.GtkPrintOperationPreview, arg2 *C.GtkPrintContext, arg3 *C.GtkWindow) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PrintOperationOverrides](instance0)
	if overrides.Preview == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PrintOperationOverrides.Preview, got none")
	}

	var _preview PrintOperationPreviewer // out
	var _context *PrintContext           // out
	var _parent *Window                  // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.PrintOperationPreviewer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(PrintOperationPreviewer)
			return ok
		})
		rv, ok := casted.(PrintOperationPreviewer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.PrintOperationPreviewer")
		}
		_preview = rv
	}
	_context = wrapPrintContext(coreglib.Take(unsafe.Pointer(arg2)))
	_parent = wrapWindow(coreglib.Take(unsafe.Pointer(arg3)))

	ok := overrides.Preview(_preview, _context, _parent)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_PrintOperationClass_request_page_setup
func _gotk4_gtk4_PrintOperationClass_request_page_setup(arg0 *C.GtkPrintOperation, arg1 *C.GtkPrintContext, arg2 C.int, arg3 *C.GtkPageSetup) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PrintOperationOverrides](instance0)
	if overrides.RequestPageSetup == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PrintOperationOverrides.RequestPageSetup, got none")
	}

	var _context *PrintContext // out
	var _pageNr int            // out
	var _setup *PageSetup      // out

	_context = wrapPrintContext(coreglib.Take(unsafe.Pointer(arg1)))
	_pageNr = int(arg2)
	_setup = wrapPageSetup(coreglib.Take(unsafe.Pointer(arg3)))

	overrides.RequestPageSetup(_context, _pageNr, _setup)
}

//export _gotk4_gtk4_PrintOperationClass_status_changed
func _gotk4_gtk4_PrintOperationClass_status_changed(arg0 *C.GtkPrintOperation) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PrintOperationOverrides](instance0)
	if overrides.StatusChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PrintOperationOverrides.StatusChanged, got none")
	}

	overrides.StatusChanged()
}

//export _gotk4_gtk4_PrintOperationClass_update_custom_widget
func _gotk4_gtk4_PrintOperationClass_update_custom_widget(arg0 *C.GtkPrintOperation, arg1 *C.GtkWidget, arg2 *C.GtkPageSetup, arg3 *C.GtkPrintSettings) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[PrintOperationOverrides](instance0)
	if overrides.UpdateCustomWidget == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected PrintOperationOverrides.UpdateCustomWidget, got none")
	}

	var _widget Widgetter        // out
	var _setup *PageSetup        // out
	var _settings *PrintSettings // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	_setup = wrapPageSetup(coreglib.Take(unsafe.Pointer(arg2)))
	_settings = wrapPrintSettings(coreglib.Take(unsafe.Pointer(arg3)))

	overrides.UpdateCustomWidget(_widget, _setup, _settings)
}

//export _gotk4_gtk4_PrintOperation_ConnectBeginPrint
func _gotk4_gtk4_PrintOperation_ConnectBeginPrint(arg0 C.gpointer, arg1 *C.GtkPrintContext, arg2 C.guintptr) {
	var f func(context *PrintContext)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(context *PrintContext))
	}

	var _context *PrintContext // out

	_context = wrapPrintContext(coreglib.Take(unsafe.Pointer(arg1)))

	f(_context)
}

//export _gotk4_gtk4_PrintOperation_ConnectCreateCustomWidget
func _gotk4_gtk4_PrintOperation_ConnectCreateCustomWidget(arg0 C.gpointer, arg1 C.guintptr) (cret *C.GObject) {
	var f func() (object *coreglib.Object)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (object *coreglib.Object))
	}

	object := f()

	var _ *coreglib.Object

	if object != nil {
		cret = (*C.GObject)(unsafe.Pointer(object.Native()))
	}

	return cret
}

//export _gotk4_gtk4_PrintOperation_ConnectCustomWidgetApply
func _gotk4_gtk4_PrintOperation_ConnectCustomWidgetApply(arg0 C.gpointer, arg1 *C.GtkWidget, arg2 C.guintptr) {
	var f func(widget Widgetter)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(widget Widgetter))
	}

	var _widget Widgetter // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}

	f(_widget)
}

//export _gotk4_gtk4_PrintOperation_ConnectDone
func _gotk4_gtk4_PrintOperation_ConnectDone(arg0 C.gpointer, arg1 C.GtkPrintOperationResult, arg2 C.guintptr) {
	var f func(result PrintOperationResult)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(result PrintOperationResult))
	}

	var _result PrintOperationResult // out

	_result = PrintOperationResult(arg1)

	f(_result)
}

//export _gotk4_gtk4_PrintOperation_ConnectDrawPage
func _gotk4_gtk4_PrintOperation_ConnectDrawPage(arg0 C.gpointer, arg1 *C.GtkPrintContext, arg2 C.gint, arg3 C.guintptr) {
	var f func(context *PrintContext, pageNr int)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(context *PrintContext, pageNr int))
	}

	var _context *PrintContext // out
	var _pageNr int            // out

	_context = wrapPrintContext(coreglib.Take(unsafe.Pointer(arg1)))
	_pageNr = int(arg2)

	f(_context, _pageNr)
}

//export _gotk4_gtk4_PrintOperation_ConnectEndPrint
func _gotk4_gtk4_PrintOperation_ConnectEndPrint(arg0 C.gpointer, arg1 *C.GtkPrintContext, arg2 C.guintptr) {
	var f func(context *PrintContext)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(context *PrintContext))
	}

	var _context *PrintContext // out

	_context = wrapPrintContext(coreglib.Take(unsafe.Pointer(arg1)))

	f(_context)
}

//export _gotk4_gtk4_PrintOperation_ConnectPaginate
func _gotk4_gtk4_PrintOperation_ConnectPaginate(arg0 C.gpointer, arg1 *C.GtkPrintContext, arg2 C.guintptr) (cret C.gboolean) {
	var f func(context *PrintContext) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(context *PrintContext) (ok bool))
	}

	var _context *PrintContext // out

	_context = wrapPrintContext(coreglib.Take(unsafe.Pointer(arg1)))

	ok := f(_context)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_PrintOperation_ConnectPreview
func _gotk4_gtk4_PrintOperation_ConnectPreview(arg0 C.gpointer, arg1 *C.GtkPrintOperationPreview, arg2 *C.GtkPrintContext, arg3 *C.GtkWindow, arg4 C.guintptr) (cret C.gboolean) {
	var f func(preview PrintOperationPreviewer, context *PrintContext, parent *Window) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(preview PrintOperationPreviewer, context *PrintContext, parent *Window) (ok bool))
	}

	var _preview PrintOperationPreviewer // out
	var _context *PrintContext           // out
	var _parent *Window                  // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.PrintOperationPreviewer is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(PrintOperationPreviewer)
			return ok
		})
		rv, ok := casted.(PrintOperationPreviewer)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.PrintOperationPreviewer")
		}
		_preview = rv
	}
	_context = wrapPrintContext(coreglib.Take(unsafe.Pointer(arg2)))
	if arg3 != nil {
		_parent = wrapWindow(coreglib.Take(unsafe.Pointer(arg3)))
	}

	ok := f(_preview, _context, _parent)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_PrintOperation_ConnectRequestPageSetup
func _gotk4_gtk4_PrintOperation_ConnectRequestPageSetup(arg0 C.gpointer, arg1 *C.GtkPrintContext, arg2 C.gint, arg3 *C.GtkPageSetup, arg4 C.guintptr) {
	var f func(context *PrintContext, pageNr int, setup *PageSetup)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(context *PrintContext, pageNr int, setup *PageSetup))
	}

	var _context *PrintContext // out
	var _pageNr int            // out
	var _setup *PageSetup      // out

	_context = wrapPrintContext(coreglib.Take(unsafe.Pointer(arg1)))
	_pageNr = int(arg2)
	_setup = wrapPageSetup(coreglib.Take(unsafe.Pointer(arg3)))

	f(_context, _pageNr, _setup)
}

//export _gotk4_gtk4_PrintOperation_ConnectStatusChanged
func _gotk4_gtk4_PrintOperation_ConnectStatusChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_PrintOperation_ConnectUpdateCustomWidget
func _gotk4_gtk4_PrintOperation_ConnectUpdateCustomWidget(arg0 C.gpointer, arg1 *C.GtkWidget, arg2 *C.GtkPageSetup, arg3 *C.GtkPrintSettings, arg4 C.guintptr) {
	var f func(widget Widgetter, setup *PageSetup, settings *PrintSettings)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(widget Widgetter, setup *PageSetup, settings *PrintSettings))
	}

	var _widget Widgetter        // out
	var _setup *PageSetup        // out
	var _settings *PrintSettings // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.Widgetter is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(Widgetter)
			return ok
		})
		rv, ok := casted.(Widgetter)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
		}
		_widget = rv
	}
	_setup = wrapPageSetup(coreglib.Take(unsafe.Pointer(arg2)))
	_settings = wrapPrintSettings(coreglib.Take(unsafe.Pointer(arg3)))

	f(_widget, _setup, _settings)
}

//export _gotk4_gtk4_RangeClass_adjust_bounds
func _gotk4_gtk4_RangeClass_adjust_bounds(arg0 *C.GtkRange, arg1 C.double) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RangeOverrides](instance0)
	if overrides.AdjustBounds == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RangeOverrides.AdjustBounds, got none")
	}

	var _newValue float64 // out

	_newValue = float64(arg1)

	overrides.AdjustBounds(_newValue)
}

//export _gotk4_gtk4_RangeClass_change_value
func _gotk4_gtk4_RangeClass_change_value(arg0 *C.GtkRange, arg1 C.GtkScrollType, arg2 C.double) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RangeOverrides](instance0)
	if overrides.ChangeValue == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RangeOverrides.ChangeValue, got none")
	}

	var _scroll ScrollType // out
	var _newValue float64  // out

	_scroll = ScrollType(arg1)
	_newValue = float64(arg2)

	ok := overrides.ChangeValue(_scroll, _newValue)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_RangeClass_get_range_border
func _gotk4_gtk4_RangeClass_get_range_border(arg0 *C.GtkRange, arg1 *C.GtkBorder) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RangeOverrides](instance0)
	if overrides.RangeBorder == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RangeOverrides.RangeBorder, got none")
	}

	var _border_ *Border // out

	_border_ = (*Border)(gextras.NewStructNative(unsafe.Pointer(arg1)))

	overrides.RangeBorder(_border_)
}

//export _gotk4_gtk4_RangeClass_move_slider
func _gotk4_gtk4_RangeClass_move_slider(arg0 *C.GtkRange, arg1 C.GtkScrollType) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RangeOverrides](instance0)
	if overrides.MoveSlider == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RangeOverrides.MoveSlider, got none")
	}

	var _scroll ScrollType // out

	_scroll = ScrollType(arg1)

	overrides.MoveSlider(_scroll)
}

//export _gotk4_gtk4_RangeClass_value_changed
func _gotk4_gtk4_RangeClass_value_changed(arg0 *C.GtkRange) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RangeOverrides](instance0)
	if overrides.ValueChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RangeOverrides.ValueChanged, got none")
	}

	overrides.ValueChanged()
}

//export _gotk4_gtk4_Range_ConnectAdjustBounds
func _gotk4_gtk4_Range_ConnectAdjustBounds(arg0 C.gpointer, arg1 C.gdouble, arg2 C.guintptr) {
	var f func(value float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(value float64))
	}

	var _value float64 // out

	_value = float64(arg1)

	f(_value)
}

//export _gotk4_gtk4_Range_ConnectChangeValue
func _gotk4_gtk4_Range_ConnectChangeValue(arg0 C.gpointer, arg1 C.GtkScrollType, arg2 C.gdouble, arg3 C.guintptr) (cret C.gboolean) {
	var f func(scroll ScrollType, value float64) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(scroll ScrollType, value float64) (ok bool))
	}

	var _scroll ScrollType // out
	var _value float64     // out

	_scroll = ScrollType(arg1)
	_value = float64(arg2)

	ok := f(_scroll, _value)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Range_ConnectMoveSlider
func _gotk4_gtk4_Range_ConnectMoveSlider(arg0 C.gpointer, arg1 C.GtkScrollType, arg2 C.guintptr) {
	var f func(step ScrollType)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(step ScrollType))
	}

	var _step ScrollType // out

	_step = ScrollType(arg1)

	f(_step)
}

//export _gotk4_gtk4_Range_ConnectValueChanged
func _gotk4_gtk4_Range_ConnectValueChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_RecentManagerClass_changed
func _gotk4_gtk4_RecentManagerClass_changed(arg0 *C.GtkRecentManager) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[RecentManagerOverrides](instance0)
	if overrides.Changed == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected RecentManagerOverrides.Changed, got none")
	}

	overrides.Changed()
}

//export _gotk4_gtk4_RecentManager_ConnectChanged
func _gotk4_gtk4_RecentManager_ConnectChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ScaleClass_get_layout_offsets
func _gotk4_gtk4_ScaleClass_get_layout_offsets(arg0 *C.GtkScale, arg1 *C.int, arg2 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ScaleOverrides](instance0)
	if overrides.LayoutOffsets == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ScaleOverrides.LayoutOffsets, got none")
	}

	x, y := overrides.LayoutOffsets()

	var _ int
	var _ int

	*arg1 = C.int(x)
	*arg2 = C.int(y)
}

//export _gotk4_gtk4_ScaleButtonClass_value_changed
func _gotk4_gtk4_ScaleButtonClass_value_changed(arg0 *C.GtkScaleButton, arg1 C.double) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ScaleButtonOverrides](instance0)
	if overrides.ValueChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ScaleButtonOverrides.ValueChanged, got none")
	}

	var _value float64 // out

	_value = float64(arg1)

	overrides.ValueChanged(_value)
}

//export _gotk4_gtk4_ScaleButton_ConnectPopdown
func _gotk4_gtk4_ScaleButton_ConnectPopdown(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ScaleButton_ConnectPopup
func _gotk4_gtk4_ScaleButton_ConnectPopup(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ScaleButton_ConnectValueChanged
func _gotk4_gtk4_ScaleButton_ConnectValueChanged(arg0 C.gpointer, arg1 C.gdouble, arg2 C.guintptr) {
	var f func(value float64)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(value float64))
	}

	var _value float64 // out

	_value = float64(arg1)

	f(_value)
}

//export _gotk4_gtk4_ScrolledWindow_ConnectEdgeOvershot
func _gotk4_gtk4_ScrolledWindow_ConnectEdgeOvershot(arg0 C.gpointer, arg1 C.GtkPositionType, arg2 C.guintptr) {
	var f func(pos PositionType)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(pos PositionType))
	}

	var _pos PositionType // out

	_pos = PositionType(arg1)

	f(_pos)
}

//export _gotk4_gtk4_ScrolledWindow_ConnectEdgeReached
func _gotk4_gtk4_ScrolledWindow_ConnectEdgeReached(arg0 C.gpointer, arg1 C.GtkPositionType, arg2 C.guintptr) {
	var f func(pos PositionType)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(pos PositionType))
	}

	var _pos PositionType // out

	_pos = PositionType(arg1)

	f(_pos)
}

//export _gotk4_gtk4_ScrolledWindow_ConnectMoveFocusOut
func _gotk4_gtk4_ScrolledWindow_ConnectMoveFocusOut(arg0 C.gpointer, arg1 C.GtkDirectionType, arg2 C.guintptr) {
	var f func(directionType DirectionType)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(directionType DirectionType))
	}

	var _directionType DirectionType // out

	_directionType = DirectionType(arg1)

	f(_directionType)
}

//export _gotk4_gtk4_ScrolledWindow_ConnectScrollChild
func _gotk4_gtk4_ScrolledWindow_ConnectScrollChild(arg0 C.gpointer, arg1 C.GtkScrollType, arg2 C.gboolean, arg3 C.guintptr) (cret C.gboolean) {
	var f func(scroll ScrollType, horizontal bool) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(scroll ScrollType, horizontal bool) (ok bool))
	}

	var _scroll ScrollType // out
	var _horizontal bool   // out

	_scroll = ScrollType(arg1)
	if arg2 != 0 {
		_horizontal = true
	}

	ok := f(_scroll, _horizontal)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_SearchEntry_ConnectActivate
func _gotk4_gtk4_SearchEntry_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_SearchEntry_ConnectNextMatch
func _gotk4_gtk4_SearchEntry_ConnectNextMatch(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_SearchEntry_ConnectPreviousMatch
func _gotk4_gtk4_SearchEntry_ConnectPreviousMatch(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_SearchEntry_ConnectSearchChanged
func _gotk4_gtk4_SearchEntry_ConnectSearchChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_SearchEntry_ConnectSearchStarted
func _gotk4_gtk4_SearchEntry_ConnectSearchStarted(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_SearchEntry_ConnectStopSearch
func _gotk4_gtk4_SearchEntry_ConnectStopSearch(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ShortcutsSection_ConnectChangeCurrentPage
func _gotk4_gtk4_ShortcutsSection_ConnectChangeCurrentPage(arg0 C.gpointer, arg1 C.gint, arg2 C.guintptr) (cret C.gboolean) {
	var f func(object int) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object int) (ok bool))
	}

	var _object int // out

	_object = int(arg1)

	ok := f(_object)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_ShortcutsWindow_ConnectClose
func _gotk4_gtk4_ShortcutsWindow_ConnectClose(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ShortcutsWindow_ConnectSearch
func _gotk4_gtk4_ShortcutsWindow_ConnectSearch(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_SignalListItemFactory_ConnectBind
func _gotk4_gtk4_SignalListItemFactory_ConnectBind(arg0 C.gpointer, arg1 *C.GObject, arg2 C.guintptr) {
	var f func(object *coreglib.Object)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object *coreglib.Object))
	}

	var _object *coreglib.Object // out

	_object = coreglib.Take(unsafe.Pointer(arg1))

	f(_object)
}

//export _gotk4_gtk4_SignalListItemFactory_ConnectSetup
func _gotk4_gtk4_SignalListItemFactory_ConnectSetup(arg0 C.gpointer, arg1 *C.GObject, arg2 C.guintptr) {
	var f func(object *coreglib.Object)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object *coreglib.Object))
	}

	var _object *coreglib.Object // out

	_object = coreglib.Take(unsafe.Pointer(arg1))

	f(_object)
}

//export _gotk4_gtk4_SignalListItemFactory_ConnectTeardown
func _gotk4_gtk4_SignalListItemFactory_ConnectTeardown(arg0 C.gpointer, arg1 *C.GObject, arg2 C.guintptr) {
	var f func(object *coreglib.Object)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object *coreglib.Object))
	}

	var _object *coreglib.Object // out

	_object = coreglib.Take(unsafe.Pointer(arg1))

	f(_object)
}

//export _gotk4_gtk4_SignalListItemFactory_ConnectUnbind
func _gotk4_gtk4_SignalListItemFactory_ConnectUnbind(arg0 C.gpointer, arg1 *C.GObject, arg2 C.guintptr) {
	var f func(object *coreglib.Object)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object *coreglib.Object))
	}

	var _object *coreglib.Object // out

	_object = coreglib.Take(unsafe.Pointer(arg1))

	f(_object)
}

//export _gotk4_gtk4_SorterClass_compare
func _gotk4_gtk4_SorterClass_compare(arg0 *C.GtkSorter, arg1 C.gpointer, arg2 C.gpointer) (cret C.GtkOrdering) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SorterOverrides](instance0)
	if overrides.Compare == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SorterOverrides.Compare, got none")
	}

	var _item1 *coreglib.Object // out
	var _item2 *coreglib.Object // out

	_item1 = coreglib.Take(unsafe.Pointer(arg1))
	_item2 = coreglib.Take(unsafe.Pointer(arg2))

	ordering := overrides.Compare(_item1, _item2)

	var _ Ordering

	cret = C.GtkOrdering(ordering)

	return cret
}

//export _gotk4_gtk4_SorterClass_get_order
func _gotk4_gtk4_SorterClass_get_order(arg0 *C.GtkSorter) (cret C.GtkSorterOrder) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[SorterOverrides](instance0)
	if overrides.Order == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected SorterOverrides.Order, got none")
	}

	sorterOrder := overrides.Order()

	var _ SorterOrder

	cret = C.GtkSorterOrder(sorterOrder)

	return cret
}

//export _gotk4_gtk4_Sorter_ConnectChanged
func _gotk4_gtk4_Sorter_ConnectChanged(arg0 C.gpointer, arg1 C.GtkSorterChange, arg2 C.guintptr) {
	var f func(change SorterChange)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(change SorterChange))
	}

	var _change SorterChange // out

	_change = SorterChange(arg1)

	f(_change)
}

//export _gotk4_gtk4_SpinButton_ConnectActivate
func _gotk4_gtk4_SpinButton_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_SpinButton_ConnectChangeValue
func _gotk4_gtk4_SpinButton_ConnectChangeValue(arg0 C.gpointer, arg1 C.GtkScrollType, arg2 C.guintptr) {
	var f func(scroll ScrollType)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(scroll ScrollType))
	}

	var _scroll ScrollType // out

	_scroll = ScrollType(arg1)

	f(_scroll)
}

//export _gotk4_gtk4_SpinButton_ConnectOutput
func _gotk4_gtk4_SpinButton_ConnectOutput(arg0 C.gpointer, arg1 C.guintptr) (cret C.gboolean) {
	var f func() (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (ok bool))
	}

	ok := f()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_SpinButton_ConnectValueChanged
func _gotk4_gtk4_SpinButton_ConnectValueChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_SpinButton_ConnectWrapped
func _gotk4_gtk4_SpinButton_ConnectWrapped(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Statusbar_ConnectTextPopped
func _gotk4_gtk4_Statusbar_ConnectTextPopped(arg0 C.gpointer, arg1 C.guint, arg2 *C.gchar, arg3 C.guintptr) {
	var f func(contextId uint, text string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(contextId uint, text string))
	}

	var _contextId uint // out
	var _text string    // out

	_contextId = uint(arg1)
	_text = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	f(_contextId, _text)
}

//export _gotk4_gtk4_Statusbar_ConnectTextPushed
func _gotk4_gtk4_Statusbar_ConnectTextPushed(arg0 C.gpointer, arg1 C.guint, arg2 *C.gchar, arg3 C.guintptr) {
	var f func(contextId uint, text string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(contextId uint, text string))
	}

	var _contextId uint // out
	var _text string    // out

	_contextId = uint(arg1)
	_text = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))

	f(_contextId, _text)
}

//export _gotk4_gtk4_StyleContextClass_changed
func _gotk4_gtk4_StyleContextClass_changed(arg0 *C.GtkStyleContext) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[StyleContextOverrides](instance0)
	if overrides.Changed == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected StyleContextOverrides.Changed, got none")
	}

	overrides.Changed()
}

//export _gotk4_gtk4_Switch_ConnectActivate
func _gotk4_gtk4_Switch_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Switch_ConnectStateSet
func _gotk4_gtk4_Switch_ConnectStateSet(arg0 C.gpointer, arg1 C.gboolean, arg2 C.guintptr) (cret C.gboolean) {
	var f func(state bool) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(state bool) (ok bool))
	}

	var _state bool // out

	if arg1 != 0 {
		_state = true
	}

	ok := f(_state)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Text_ConnectActivate
func _gotk4_gtk4_Text_ConnectActivate(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Text_ConnectBackspace
func _gotk4_gtk4_Text_ConnectBackspace(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Text_ConnectCopyClipboard
func _gotk4_gtk4_Text_ConnectCopyClipboard(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Text_ConnectCutClipboard
func _gotk4_gtk4_Text_ConnectCutClipboard(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Text_ConnectDeleteFromCursor
func _gotk4_gtk4_Text_ConnectDeleteFromCursor(arg0 C.gpointer, arg1 C.GtkDeleteType, arg2 C.gint, arg3 C.guintptr) {
	var f func(typ DeleteType, count int)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(typ DeleteType, count int))
	}

	var _typ DeleteType // out
	var _count int      // out

	_typ = DeleteType(arg1)
	_count = int(arg2)

	f(_typ, _count)
}

//export _gotk4_gtk4_Text_ConnectInsertAtCursor
func _gotk4_gtk4_Text_ConnectInsertAtCursor(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(str string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(str string))
	}

	var _str string // out

	_str = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_str)
}

//export _gotk4_gtk4_Text_ConnectInsertEmoji
func _gotk4_gtk4_Text_ConnectInsertEmoji(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Text_ConnectMoveCursor
func _gotk4_gtk4_Text_ConnectMoveCursor(arg0 C.gpointer, arg1 C.GtkMovementStep, arg2 C.gint, arg3 C.gboolean, arg4 C.guintptr) {
	var f func(step MovementStep, count int, extend bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(step MovementStep, count int, extend bool))
	}

	var _step MovementStep // out
	var _count int         // out
	var _extend bool       // out

	_step = MovementStep(arg1)
	_count = int(arg2)
	if arg3 != 0 {
		_extend = true
	}

	f(_step, _count, _extend)
}

//export _gotk4_gtk4_Text_ConnectPasteClipboard
func _gotk4_gtk4_Text_ConnectPasteClipboard(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Text_ConnectPreeditChanged
func _gotk4_gtk4_Text_ConnectPreeditChanged(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(preedit string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(preedit string))
	}

	var _preedit string // out

	_preedit = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_preedit)
}

//export _gotk4_gtk4_Text_ConnectToggleOverwrite
func _gotk4_gtk4_Text_ConnectToggleOverwrite(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TextBufferClass_apply_tag
func _gotk4_gtk4_TextBufferClass_apply_tag(arg0 *C.GtkTextBuffer, arg1 *C.GtkTextTag, arg2 *C.GtkTextIter, arg3 *C.GtkTextIter) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextBufferOverrides](instance0)
	if overrides.ApplyTag == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextBufferOverrides.ApplyTag, got none")
	}

	var _tag *TextTag    // out
	var _start *TextIter // out
	var _end *TextIter   // out

	_tag = wrapTextTag(coreglib.Take(unsafe.Pointer(arg1)))
	_start = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_end = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg3)))

	overrides.ApplyTag(_tag, _start, _end)
}

//export _gotk4_gtk4_TextBufferClass_begin_user_action
func _gotk4_gtk4_TextBufferClass_begin_user_action(arg0 *C.GtkTextBuffer) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextBufferOverrides](instance0)
	if overrides.BeginUserAction == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextBufferOverrides.BeginUserAction, got none")
	}

	overrides.BeginUserAction()
}

//export _gotk4_gtk4_TextBufferClass_changed
func _gotk4_gtk4_TextBufferClass_changed(arg0 *C.GtkTextBuffer) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextBufferOverrides](instance0)
	if overrides.Changed == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextBufferOverrides.Changed, got none")
	}

	overrides.Changed()
}

//export _gotk4_gtk4_TextBufferClass_delete_range
func _gotk4_gtk4_TextBufferClass_delete_range(arg0 *C.GtkTextBuffer, arg1 *C.GtkTextIter, arg2 *C.GtkTextIter) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextBufferOverrides](instance0)
	if overrides.DeleteRange == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextBufferOverrides.DeleteRange, got none")
	}

	var _start *TextIter // out
	var _end *TextIter   // out

	_start = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_end = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	overrides.DeleteRange(_start, _end)
}

//export _gotk4_gtk4_TextBufferClass_end_user_action
func _gotk4_gtk4_TextBufferClass_end_user_action(arg0 *C.GtkTextBuffer) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextBufferOverrides](instance0)
	if overrides.EndUserAction == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextBufferOverrides.EndUserAction, got none")
	}

	overrides.EndUserAction()
}

//export _gotk4_gtk4_TextBufferClass_insert_child_anchor
func _gotk4_gtk4_TextBufferClass_insert_child_anchor(arg0 *C.GtkTextBuffer, arg1 *C.GtkTextIter, arg2 *C.GtkTextChildAnchor) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextBufferOverrides](instance0)
	if overrides.InsertChildAnchor == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextBufferOverrides.InsertChildAnchor, got none")
	}

	var _iter *TextIter          // out
	var _anchor *TextChildAnchor // out

	_iter = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_anchor = wrapTextChildAnchor(coreglib.Take(unsafe.Pointer(arg2)))

	overrides.InsertChildAnchor(_iter, _anchor)
}

//export _gotk4_gtk4_TextBufferClass_insert_paintable
func _gotk4_gtk4_TextBufferClass_insert_paintable(arg0 *C.GtkTextBuffer, arg1 *C.GtkTextIter, arg2 *C.GdkPaintable) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextBufferOverrides](instance0)
	if overrides.InsertPaintable == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextBufferOverrides.InsertPaintable, got none")
	}

	var _iter *TextIter           // out
	var _paintable gdk.Paintabler // out

	_iter = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gdk.Paintabler is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Paintabler)
			return ok
		})
		rv, ok := casted.(gdk.Paintabler)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Paintabler")
		}
		_paintable = rv
	}

	overrides.InsertPaintable(_iter, _paintable)
}

//export _gotk4_gtk4_TextBufferClass_insert_text
func _gotk4_gtk4_TextBufferClass_insert_text(arg0 *C.GtkTextBuffer, arg1 *C.GtkTextIter, arg2 *C.char, arg3 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextBufferOverrides](instance0)
	if overrides.InsertText == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextBufferOverrides.InsertText, got none")
	}

	var _pos *TextIter     // out
	var _newText string    // out
	var _newTextLength int // out

	_pos = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_newText = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_newTextLength = int(arg3)

	overrides.InsertText(_pos, _newText, _newTextLength)
}

//export _gotk4_gtk4_TextBufferClass_mark_deleted
func _gotk4_gtk4_TextBufferClass_mark_deleted(arg0 *C.GtkTextBuffer, arg1 *C.GtkTextMark) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextBufferOverrides](instance0)
	if overrides.MarkDeleted == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextBufferOverrides.MarkDeleted, got none")
	}

	var _mark *TextMark // out

	_mark = wrapTextMark(coreglib.Take(unsafe.Pointer(arg1)))

	overrides.MarkDeleted(_mark)
}

//export _gotk4_gtk4_TextBufferClass_mark_set
func _gotk4_gtk4_TextBufferClass_mark_set(arg0 *C.GtkTextBuffer, arg1 *C.GtkTextIter, arg2 *C.GtkTextMark) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextBufferOverrides](instance0)
	if overrides.MarkSet == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextBufferOverrides.MarkSet, got none")
	}

	var _location *TextIter // out
	var _mark *TextMark     // out

	_location = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_mark = wrapTextMark(coreglib.Take(unsafe.Pointer(arg2)))

	overrides.MarkSet(_location, _mark)
}

//export _gotk4_gtk4_TextBufferClass_modified_changed
func _gotk4_gtk4_TextBufferClass_modified_changed(arg0 *C.GtkTextBuffer) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextBufferOverrides](instance0)
	if overrides.ModifiedChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextBufferOverrides.ModifiedChanged, got none")
	}

	overrides.ModifiedChanged()
}

//export _gotk4_gtk4_TextBufferClass_paste_done
func _gotk4_gtk4_TextBufferClass_paste_done(arg0 *C.GtkTextBuffer, arg1 *C.GdkClipboard) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextBufferOverrides](instance0)
	if overrides.PasteDone == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextBufferOverrides.PasteDone, got none")
	}

	var _clipboard *gdk.Clipboard // out

	{
		obj := coreglib.Take(unsafe.Pointer(arg1))
		_clipboard = &gdk.Clipboard{
			Object: obj,
		}
	}

	overrides.PasteDone(_clipboard)
}

//export _gotk4_gtk4_TextBufferClass_redo
func _gotk4_gtk4_TextBufferClass_redo(arg0 *C.GtkTextBuffer) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextBufferOverrides](instance0)
	if overrides.Redo == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextBufferOverrides.Redo, got none")
	}

	overrides.Redo()
}

//export _gotk4_gtk4_TextBufferClass_remove_tag
func _gotk4_gtk4_TextBufferClass_remove_tag(arg0 *C.GtkTextBuffer, arg1 *C.GtkTextTag, arg2 *C.GtkTextIter, arg3 *C.GtkTextIter) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextBufferOverrides](instance0)
	if overrides.RemoveTag == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextBufferOverrides.RemoveTag, got none")
	}

	var _tag *TextTag    // out
	var _start *TextIter // out
	var _end *TextIter   // out

	_tag = wrapTextTag(coreglib.Take(unsafe.Pointer(arg1)))
	_start = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_end = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg3)))

	overrides.RemoveTag(_tag, _start, _end)
}

//export _gotk4_gtk4_TextBufferClass_undo
func _gotk4_gtk4_TextBufferClass_undo(arg0 *C.GtkTextBuffer) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextBufferOverrides](instance0)
	if overrides.Undo == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextBufferOverrides.Undo, got none")
	}

	overrides.Undo()
}

//export _gotk4_gtk4_TextBuffer_ConnectApplyTag
func _gotk4_gtk4_TextBuffer_ConnectApplyTag(arg0 C.gpointer, arg1 *C.GtkTextTag, arg2 *C.GtkTextIter, arg3 *C.GtkTextIter, arg4 C.guintptr) {
	var f func(tag *TextTag, start, end *TextIter)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(tag *TextTag, start, end *TextIter))
	}

	var _tag *TextTag    // out
	var _start *TextIter // out
	var _end *TextIter   // out

	_tag = wrapTextTag(coreglib.Take(unsafe.Pointer(arg1)))
	_start = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_end = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg3)))

	f(_tag, _start, _end)
}

//export _gotk4_gtk4_TextBuffer_ConnectBeginUserAction
func _gotk4_gtk4_TextBuffer_ConnectBeginUserAction(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TextBuffer_ConnectChanged
func _gotk4_gtk4_TextBuffer_ConnectChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TextBuffer_ConnectDeleteRange
func _gotk4_gtk4_TextBuffer_ConnectDeleteRange(arg0 C.gpointer, arg1 *C.GtkTextIter, arg2 *C.GtkTextIter, arg3 C.guintptr) {
	var f func(start, end *TextIter)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(start, end *TextIter))
	}

	var _start *TextIter // out
	var _end *TextIter   // out

	_start = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_end = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	f(_start, _end)
}

//export _gotk4_gtk4_TextBuffer_ConnectEndUserAction
func _gotk4_gtk4_TextBuffer_ConnectEndUserAction(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TextBuffer_ConnectInsertChildAnchor
func _gotk4_gtk4_TextBuffer_ConnectInsertChildAnchor(arg0 C.gpointer, arg1 *C.GtkTextIter, arg2 *C.GtkTextChildAnchor, arg3 C.guintptr) {
	var f func(location *TextIter, anchor *TextChildAnchor)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(location *TextIter, anchor *TextChildAnchor))
	}

	var _location *TextIter      // out
	var _anchor *TextChildAnchor // out

	_location = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_anchor = wrapTextChildAnchor(coreglib.Take(unsafe.Pointer(arg2)))

	f(_location, _anchor)
}

//export _gotk4_gtk4_TextBuffer_ConnectInsertPaintable
func _gotk4_gtk4_TextBuffer_ConnectInsertPaintable(arg0 C.gpointer, arg1 *C.GtkTextIter, arg2 *C.GdkPaintable, arg3 C.guintptr) {
	var f func(location *TextIter, paintable gdk.Paintabler)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(location *TextIter, paintable gdk.Paintabler))
	}

	var _location *TextIter       // out
	var _paintable gdk.Paintabler // out

	_location = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	{
		objptr := unsafe.Pointer(arg2)
		if objptr == nil {
			panic("object of type gdk.Paintabler is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(gdk.Paintabler)
			return ok
		})
		rv, ok := casted.(gdk.Paintabler)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gdk.Paintabler")
		}
		_paintable = rv
	}

	f(_location, _paintable)
}

//export _gotk4_gtk4_TextBuffer_ConnectInsertText
func _gotk4_gtk4_TextBuffer_ConnectInsertText(arg0 C.gpointer, arg1 *C.GtkTextIter, arg2 *C.gchar, arg3 C.gint, arg4 C.guintptr) {
	var f func(location *TextIter, text string, len int)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(location *TextIter, text string, len int))
	}

	var _location *TextIter // out
	var _text string        // out
	var _len int            // out

	_location = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_text = C.GoString((*C.gchar)(unsafe.Pointer(arg2)))
	_len = int(arg3)

	f(_location, _text, _len)
}

//export _gotk4_gtk4_TextBuffer_ConnectMarkDeleted
func _gotk4_gtk4_TextBuffer_ConnectMarkDeleted(arg0 C.gpointer, arg1 *C.GtkTextMark, arg2 C.guintptr) {
	var f func(mark *TextMark)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(mark *TextMark))
	}

	var _mark *TextMark // out

	_mark = wrapTextMark(coreglib.Take(unsafe.Pointer(arg1)))

	f(_mark)
}

//export _gotk4_gtk4_TextBuffer_ConnectMarkSet
func _gotk4_gtk4_TextBuffer_ConnectMarkSet(arg0 C.gpointer, arg1 *C.GtkTextIter, arg2 *C.GtkTextMark, arg3 C.guintptr) {
	var f func(location *TextIter, mark *TextMark)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(location *TextIter, mark *TextMark))
	}

	var _location *TextIter // out
	var _mark *TextMark     // out

	_location = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_mark = wrapTextMark(coreglib.Take(unsafe.Pointer(arg2)))

	f(_location, _mark)
}

//export _gotk4_gtk4_TextBuffer_ConnectModifiedChanged
func _gotk4_gtk4_TextBuffer_ConnectModifiedChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TextBuffer_ConnectPasteDone
func _gotk4_gtk4_TextBuffer_ConnectPasteDone(arg0 C.gpointer, arg1 *C.GdkClipboard, arg2 C.guintptr) {
	var f func(clipboard *gdk.Clipboard)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(clipboard *gdk.Clipboard))
	}

	var _clipboard *gdk.Clipboard // out

	{
		obj := coreglib.Take(unsafe.Pointer(arg1))
		_clipboard = &gdk.Clipboard{
			Object: obj,
		}
	}

	f(_clipboard)
}

//export _gotk4_gtk4_TextBuffer_ConnectRedo
func _gotk4_gtk4_TextBuffer_ConnectRedo(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TextBuffer_ConnectRemoveTag
func _gotk4_gtk4_TextBuffer_ConnectRemoveTag(arg0 C.gpointer, arg1 *C.GtkTextTag, arg2 *C.GtkTextIter, arg3 *C.GtkTextIter, arg4 C.guintptr) {
	var f func(tag *TextTag, start, end *TextIter)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(tag *TextTag, start, end *TextIter))
	}

	var _tag *TextTag    // out
	var _start *TextIter // out
	var _end *TextIter   // out

	_tag = wrapTextTag(coreglib.Take(unsafe.Pointer(arg1)))
	_start = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_end = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg3)))

	f(_tag, _start, _end)
}

//export _gotk4_gtk4_TextBuffer_ConnectUndo
func _gotk4_gtk4_TextBuffer_ConnectUndo(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TextTagTable_ConnectTagAdded
func _gotk4_gtk4_TextTagTable_ConnectTagAdded(arg0 C.gpointer, arg1 *C.GtkTextTag, arg2 C.guintptr) {
	var f func(tag *TextTag)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(tag *TextTag))
	}

	var _tag *TextTag // out

	_tag = wrapTextTag(coreglib.Take(unsafe.Pointer(arg1)))

	f(_tag)
}

//export _gotk4_gtk4_TextTagTable_ConnectTagChanged
func _gotk4_gtk4_TextTagTable_ConnectTagChanged(arg0 C.gpointer, arg1 *C.GtkTextTag, arg2 C.gboolean, arg3 C.guintptr) {
	var f func(tag *TextTag, sizeChanged bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(tag *TextTag, sizeChanged bool))
	}

	var _tag *TextTag     // out
	var _sizeChanged bool // out

	_tag = wrapTextTag(coreglib.Take(unsafe.Pointer(arg1)))
	if arg2 != 0 {
		_sizeChanged = true
	}

	f(_tag, _sizeChanged)
}

//export _gotk4_gtk4_TextTagTable_ConnectTagRemoved
func _gotk4_gtk4_TextTagTable_ConnectTagRemoved(arg0 C.gpointer, arg1 *C.GtkTextTag, arg2 C.guintptr) {
	var f func(tag *TextTag)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(tag *TextTag))
	}

	var _tag *TextTag // out

	_tag = wrapTextTag(coreglib.Take(unsafe.Pointer(arg1)))

	f(_tag)
}

//export _gotk4_gtk4_TextViewClass_backspace
func _gotk4_gtk4_TextViewClass_backspace(arg0 *C.GtkTextView) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextViewOverrides](instance0)
	if overrides.Backspace == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextViewOverrides.Backspace, got none")
	}

	overrides.Backspace()
}

//export _gotk4_gtk4_TextViewClass_copy_clipboard
func _gotk4_gtk4_TextViewClass_copy_clipboard(arg0 *C.GtkTextView) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextViewOverrides](instance0)
	if overrides.CopyClipboard == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextViewOverrides.CopyClipboard, got none")
	}

	overrides.CopyClipboard()
}

//export _gotk4_gtk4_TextViewClass_cut_clipboard
func _gotk4_gtk4_TextViewClass_cut_clipboard(arg0 *C.GtkTextView) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextViewOverrides](instance0)
	if overrides.CutClipboard == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextViewOverrides.CutClipboard, got none")
	}

	overrides.CutClipboard()
}

//export _gotk4_gtk4_TextViewClass_delete_from_cursor
func _gotk4_gtk4_TextViewClass_delete_from_cursor(arg0 *C.GtkTextView, arg1 C.GtkDeleteType, arg2 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextViewOverrides](instance0)
	if overrides.DeleteFromCursor == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextViewOverrides.DeleteFromCursor, got none")
	}

	var _typ DeleteType // out
	var _count int      // out

	_typ = DeleteType(arg1)
	_count = int(arg2)

	overrides.DeleteFromCursor(_typ, _count)
}

//export _gotk4_gtk4_TextViewClass_extend_selection
func _gotk4_gtk4_TextViewClass_extend_selection(arg0 *C.GtkTextView, arg1 C.GtkTextExtendSelection, arg2 *C.GtkTextIter, arg3 *C.GtkTextIter, arg4 *C.GtkTextIter) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextViewOverrides](instance0)
	if overrides.ExtendSelection == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextViewOverrides.ExtendSelection, got none")
	}

	var _granularity TextExtendSelection // out
	var _location *TextIter              // out
	var _start *TextIter                 // out
	var _end *TextIter                   // out

	_granularity = TextExtendSelection(arg1)
	_location = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_start = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg3)))
	_end = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg4)))

	ok := overrides.ExtendSelection(_granularity, _location, _start, _end)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TextViewClass_insert_at_cursor
func _gotk4_gtk4_TextViewClass_insert_at_cursor(arg0 *C.GtkTextView, arg1 *C.char) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextViewOverrides](instance0)
	if overrides.InsertAtCursor == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextViewOverrides.InsertAtCursor, got none")
	}

	var _str string // out

	_str = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	overrides.InsertAtCursor(_str)
}

//export _gotk4_gtk4_TextViewClass_insert_emoji
func _gotk4_gtk4_TextViewClass_insert_emoji(arg0 *C.GtkTextView) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextViewOverrides](instance0)
	if overrides.InsertEmoji == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextViewOverrides.InsertEmoji, got none")
	}

	overrides.InsertEmoji()
}

//export _gotk4_gtk4_TextViewClass_move_cursor
func _gotk4_gtk4_TextViewClass_move_cursor(arg0 *C.GtkTextView, arg1 C.GtkMovementStep, arg2 C.int, arg3 C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextViewOverrides](instance0)
	if overrides.MoveCursor == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextViewOverrides.MoveCursor, got none")
	}

	var _step MovementStep    // out
	var _count int            // out
	var _extendSelection bool // out

	_step = MovementStep(arg1)
	_count = int(arg2)
	if arg3 != 0 {
		_extendSelection = true
	}

	overrides.MoveCursor(_step, _count, _extendSelection)
}

//export _gotk4_gtk4_TextViewClass_paste_clipboard
func _gotk4_gtk4_TextViewClass_paste_clipboard(arg0 *C.GtkTextView) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextViewOverrides](instance0)
	if overrides.PasteClipboard == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextViewOverrides.PasteClipboard, got none")
	}

	overrides.PasteClipboard()
}

//export _gotk4_gtk4_TextViewClass_set_anchor
func _gotk4_gtk4_TextViewClass_set_anchor(arg0 *C.GtkTextView) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextViewOverrides](instance0)
	if overrides.SetAnchor == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextViewOverrides.SetAnchor, got none")
	}

	overrides.SetAnchor()
}

//export _gotk4_gtk4_TextViewClass_snapshot_layer
func _gotk4_gtk4_TextViewClass_snapshot_layer(arg0 *C.GtkTextView, arg1 C.GtkTextViewLayer, arg2 *C.GtkSnapshot) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextViewOverrides](instance0)
	if overrides.SnapshotLayer == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextViewOverrides.SnapshotLayer, got none")
	}

	var _layer TextViewLayer // out
	var _snapshot *Snapshot  // out

	_layer = TextViewLayer(arg1)
	_snapshot = wrapSnapshot(coreglib.Take(unsafe.Pointer(arg2)))

	overrides.SnapshotLayer(_layer, _snapshot)
}

//export _gotk4_gtk4_TextViewClass_toggle_overwrite
func _gotk4_gtk4_TextViewClass_toggle_overwrite(arg0 *C.GtkTextView) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TextViewOverrides](instance0)
	if overrides.ToggleOverwrite == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TextViewOverrides.ToggleOverwrite, got none")
	}

	overrides.ToggleOverwrite()
}

//export _gotk4_gtk4_TextView_ConnectBackspace
func _gotk4_gtk4_TextView_ConnectBackspace(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TextView_ConnectCopyClipboard
func _gotk4_gtk4_TextView_ConnectCopyClipboard(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TextView_ConnectCutClipboard
func _gotk4_gtk4_TextView_ConnectCutClipboard(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TextView_ConnectDeleteFromCursor
func _gotk4_gtk4_TextView_ConnectDeleteFromCursor(arg0 C.gpointer, arg1 C.GtkDeleteType, arg2 C.gint, arg3 C.guintptr) {
	var f func(typ DeleteType, count int)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(typ DeleteType, count int))
	}

	var _typ DeleteType // out
	var _count int      // out

	_typ = DeleteType(arg1)
	_count = int(arg2)

	f(_typ, _count)
}

//export _gotk4_gtk4_TextView_ConnectExtendSelection
func _gotk4_gtk4_TextView_ConnectExtendSelection(arg0 C.gpointer, arg1 C.GtkTextExtendSelection, arg2 *C.GtkTextIter, arg3 *C.GtkTextIter, arg4 *C.GtkTextIter, arg5 C.guintptr) (cret C.gboolean) {
	var f func(granularity TextExtendSelection, location, start, end *TextIter) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg5))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(granularity TextExtendSelection, location, start, end *TextIter) (ok bool))
	}

	var _granularity TextExtendSelection // out
	var _location *TextIter              // out
	var _start *TextIter                 // out
	var _end *TextIter                   // out

	_granularity = TextExtendSelection(arg1)
	_location = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_start = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg3)))
	_end = (*TextIter)(gextras.NewStructNative(unsafe.Pointer(arg4)))

	ok := f(_granularity, _location, _start, _end)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TextView_ConnectInsertAtCursor
func _gotk4_gtk4_TextView_ConnectInsertAtCursor(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(str string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(str string))
	}

	var _str string // out

	_str = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_str)
}

//export _gotk4_gtk4_TextView_ConnectInsertEmoji
func _gotk4_gtk4_TextView_ConnectInsertEmoji(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TextView_ConnectMoveCursor
func _gotk4_gtk4_TextView_ConnectMoveCursor(arg0 C.gpointer, arg1 C.GtkMovementStep, arg2 C.gint, arg3 C.gboolean, arg4 C.guintptr) {
	var f func(step MovementStep, count int, extendSelection bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(step MovementStep, count int, extendSelection bool))
	}

	var _step MovementStep    // out
	var _count int            // out
	var _extendSelection bool // out

	_step = MovementStep(arg1)
	_count = int(arg2)
	if arg3 != 0 {
		_extendSelection = true
	}

	f(_step, _count, _extendSelection)
}

//export _gotk4_gtk4_TextView_ConnectMoveViewport
func _gotk4_gtk4_TextView_ConnectMoveViewport(arg0 C.gpointer, arg1 C.GtkScrollStep, arg2 C.gint, arg3 C.guintptr) {
	var f func(step ScrollStep, count int)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(step ScrollStep, count int))
	}

	var _step ScrollStep // out
	var _count int       // out

	_step = ScrollStep(arg1)
	_count = int(arg2)

	f(_step, _count)
}

//export _gotk4_gtk4_TextView_ConnectPasteClipboard
func _gotk4_gtk4_TextView_ConnectPasteClipboard(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TextView_ConnectPreeditChanged
func _gotk4_gtk4_TextView_ConnectPreeditChanged(arg0 C.gpointer, arg1 *C.gchar, arg2 C.guintptr) {
	var f func(preedit string)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(preedit string))
	}

	var _preedit string // out

	_preedit = C.GoString((*C.gchar)(unsafe.Pointer(arg1)))

	f(_preedit)
}

//export _gotk4_gtk4_TextView_ConnectSelectAll
func _gotk4_gtk4_TextView_ConnectSelectAll(arg0 C.gpointer, arg1 C.gboolean, arg2 C.guintptr) {
	var f func(sel bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(sel bool))
	}

	var _sel bool // out

	if arg1 != 0 {
		_sel = true
	}

	f(_sel)
}

//export _gotk4_gtk4_TextView_ConnectSetAnchor
func _gotk4_gtk4_TextView_ConnectSetAnchor(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TextView_ConnectToggleCursorVisible
func _gotk4_gtk4_TextView_ConnectToggleCursorVisible(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TextView_ConnectToggleOverwrite
func _gotk4_gtk4_TextView_ConnectToggleOverwrite(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_ToggleButtonClass_toggled
func _gotk4_gtk4_ToggleButtonClass_toggled(arg0 *C.GtkToggleButton) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[ToggleButtonOverrides](instance0)
	if overrides.Toggled == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected ToggleButtonOverrides.Toggled, got none")
	}

	overrides.Toggled()
}

//export _gotk4_gtk4_ToggleButton_ConnectToggled
func _gotk4_gtk4_ToggleButton_ConnectToggled(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TreeModelFilterClass_modify
func _gotk4_gtk4_TreeModelFilterClass_modify(arg0 *C.GtkTreeModelFilter, arg1 *C.GtkTreeModel, arg2 *C.GtkTreeIter, arg3 *C.GValue, arg4 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeModelFilterOverrides](instance0)
	if overrides.Modify == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeModelFilterOverrides.Modify, got none")
	}

	var _childModel TreeModeller // out
	var _iter *TreeIter          // out
	var _value *coreglib.Value   // out
	var _column int              // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_childModel = rv
	}
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))
	_value = coreglib.ValueFromNative(unsafe.Pointer(arg3))
	_column = int(arg4)

	overrides.Modify(_childModel, _iter, _value, _column)
}

//export _gotk4_gtk4_TreeModelFilterClass_visible
func _gotk4_gtk4_TreeModelFilterClass_visible(arg0 *C.GtkTreeModelFilter, arg1 *C.GtkTreeModel, arg2 *C.GtkTreeIter) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeModelFilterOverrides](instance0)
	if overrides.Visible == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeModelFilterOverrides.Visible, got none")
	}

	var _childModel TreeModeller // out
	var _iter *TreeIter          // out

	{
		objptr := unsafe.Pointer(arg1)
		if objptr == nil {
			panic("object of type gtk.TreeModeller is nil")
		}

		object := coreglib.Take(objptr)
		casted := object.WalkCast(func(obj coreglib.Objector) bool {
			_, ok := obj.(TreeModeller)
			return ok
		})
		rv, ok := casted.(TreeModeller)
		if !ok {
			panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.TreeModeller")
		}
		_childModel = rv
	}
	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	ok := overrides.Visible(_childModel, _iter)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeSelection_ConnectChanged
func _gotk4_gtk4_TreeSelection_ConnectChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TreeViewClass_columns_changed
func _gotk4_gtk4_TreeViewClass_columns_changed(arg0 *C.GtkTreeView) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeViewOverrides](instance0)
	if overrides.ColumnsChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeViewOverrides.ColumnsChanged, got none")
	}

	overrides.ColumnsChanged()
}

//export _gotk4_gtk4_TreeViewClass_cursor_changed
func _gotk4_gtk4_TreeViewClass_cursor_changed(arg0 *C.GtkTreeView) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeViewOverrides](instance0)
	if overrides.CursorChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeViewOverrides.CursorChanged, got none")
	}

	overrides.CursorChanged()
}

//export _gotk4_gtk4_TreeViewClass_expand_collapse_cursor_row
func _gotk4_gtk4_TreeViewClass_expand_collapse_cursor_row(arg0 *C.GtkTreeView, arg1 C.gboolean, arg2 C.gboolean, arg3 C.gboolean) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeViewOverrides](instance0)
	if overrides.ExpandCollapseCursorRow == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeViewOverrides.ExpandCollapseCursorRow, got none")
	}

	var _logical bool // out
	var _expand bool  // out
	var _openAll bool // out

	if arg1 != 0 {
		_logical = true
	}
	if arg2 != 0 {
		_expand = true
	}
	if arg3 != 0 {
		_openAll = true
	}

	ok := overrides.ExpandCollapseCursorRow(_logical, _expand, _openAll)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeViewClass_move_cursor
func _gotk4_gtk4_TreeViewClass_move_cursor(arg0 *C.GtkTreeView, arg1 C.GtkMovementStep, arg2 C.int, arg3 C.gboolean, arg4 C.gboolean) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeViewOverrides](instance0)
	if overrides.MoveCursor == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeViewOverrides.MoveCursor, got none")
	}

	var _step MovementStep // out
	var _count int         // out
	var _extend bool       // out
	var _modify bool       // out

	_step = MovementStep(arg1)
	_count = int(arg2)
	if arg3 != 0 {
		_extend = true
	}
	if arg4 != 0 {
		_modify = true
	}

	ok := overrides.MoveCursor(_step, _count, _extend, _modify)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeViewClass_row_activated
func _gotk4_gtk4_TreeViewClass_row_activated(arg0 *C.GtkTreeView, arg1 *C.GtkTreePath, arg2 *C.GtkTreeViewColumn) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeViewOverrides](instance0)
	if overrides.RowActivated == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeViewOverrides.RowActivated, got none")
	}

	var _path *TreePath         // out
	var _column *TreeViewColumn // out

	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	if arg2 != nil {
		_column = wrapTreeViewColumn(coreglib.Take(unsafe.Pointer(arg2)))
	}

	overrides.RowActivated(_path, _column)
}

//export _gotk4_gtk4_TreeViewClass_row_collapsed
func _gotk4_gtk4_TreeViewClass_row_collapsed(arg0 *C.GtkTreeView, arg1 *C.GtkTreeIter, arg2 *C.GtkTreePath) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeViewOverrides](instance0)
	if overrides.RowCollapsed == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeViewOverrides.RowCollapsed, got none")
	}

	var _iter *TreeIter // out
	var _path *TreePath // out

	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	overrides.RowCollapsed(_iter, _path)
}

//export _gotk4_gtk4_TreeViewClass_row_expanded
func _gotk4_gtk4_TreeViewClass_row_expanded(arg0 *C.GtkTreeView, arg1 *C.GtkTreeIter, arg2 *C.GtkTreePath) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeViewOverrides](instance0)
	if overrides.RowExpanded == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeViewOverrides.RowExpanded, got none")
	}

	var _iter *TreeIter // out
	var _path *TreePath // out

	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	overrides.RowExpanded(_iter, _path)
}

//export _gotk4_gtk4_TreeViewClass_select_all
func _gotk4_gtk4_TreeViewClass_select_all(arg0 *C.GtkTreeView) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeViewOverrides](instance0)
	if overrides.SelectAll == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeViewOverrides.SelectAll, got none")
	}

	ok := overrides.SelectAll()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeViewClass_select_cursor_parent
func _gotk4_gtk4_TreeViewClass_select_cursor_parent(arg0 *C.GtkTreeView) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeViewOverrides](instance0)
	if overrides.SelectCursorParent == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeViewOverrides.SelectCursorParent, got none")
	}

	ok := overrides.SelectCursorParent()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeViewClass_select_cursor_row
func _gotk4_gtk4_TreeViewClass_select_cursor_row(arg0 *C.GtkTreeView, arg1 C.gboolean) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeViewOverrides](instance0)
	if overrides.SelectCursorRow == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeViewOverrides.SelectCursorRow, got none")
	}

	var _startEditing bool // out

	if arg1 != 0 {
		_startEditing = true
	}

	ok := overrides.SelectCursorRow(_startEditing)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeViewClass_start_interactive_search
func _gotk4_gtk4_TreeViewClass_start_interactive_search(arg0 *C.GtkTreeView) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeViewOverrides](instance0)
	if overrides.StartInteractiveSearch == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeViewOverrides.StartInteractiveSearch, got none")
	}

	ok := overrides.StartInteractiveSearch()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeViewClass_test_collapse_row
func _gotk4_gtk4_TreeViewClass_test_collapse_row(arg0 *C.GtkTreeView, arg1 *C.GtkTreeIter, arg2 *C.GtkTreePath) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeViewOverrides](instance0)
	if overrides.TestCollapseRow == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeViewOverrides.TestCollapseRow, got none")
	}

	var _iter *TreeIter // out
	var _path *TreePath // out

	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	ok := overrides.TestCollapseRow(_iter, _path)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeViewClass_test_expand_row
func _gotk4_gtk4_TreeViewClass_test_expand_row(arg0 *C.GtkTreeView, arg1 *C.GtkTreeIter, arg2 *C.GtkTreePath) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeViewOverrides](instance0)
	if overrides.TestExpandRow == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeViewOverrides.TestExpandRow, got none")
	}

	var _iter *TreeIter // out
	var _path *TreePath // out

	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	ok := overrides.TestExpandRow(_iter, _path)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeViewClass_toggle_cursor_row
func _gotk4_gtk4_TreeViewClass_toggle_cursor_row(arg0 *C.GtkTreeView) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeViewOverrides](instance0)
	if overrides.ToggleCursorRow == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeViewOverrides.ToggleCursorRow, got none")
	}

	ok := overrides.ToggleCursorRow()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeViewClass_unselect_all
func _gotk4_gtk4_TreeViewClass_unselect_all(arg0 *C.GtkTreeView) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[TreeViewOverrides](instance0)
	if overrides.UnselectAll == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected TreeViewOverrides.UnselectAll, got none")
	}

	ok := overrides.UnselectAll()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeView_ConnectColumnsChanged
func _gotk4_gtk4_TreeView_ConnectColumnsChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TreeView_ConnectCursorChanged
func _gotk4_gtk4_TreeView_ConnectCursorChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_TreeView_ConnectExpandCollapseCursorRow
func _gotk4_gtk4_TreeView_ConnectExpandCollapseCursorRow(arg0 C.gpointer, arg1 C.gboolean, arg2 C.gboolean, arg3 C.gboolean, arg4 C.guintptr) (cret C.gboolean) {
	var f func(object, p0, p1 bool) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg4))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object, p0, p1 bool) (ok bool))
	}

	var _object bool // out
	var _p0 bool     // out
	var _p1 bool     // out

	if arg1 != 0 {
		_object = true
	}
	if arg2 != 0 {
		_p0 = true
	}
	if arg3 != 0 {
		_p1 = true
	}

	ok := f(_object, _p0, _p1)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeView_ConnectMoveCursor
func _gotk4_gtk4_TreeView_ConnectMoveCursor(arg0 C.gpointer, arg1 C.GtkMovementStep, arg2 C.gint, arg3 C.gboolean, arg4 C.gboolean, arg5 C.guintptr) (cret C.gboolean) {
	var f func(step MovementStep, direction int, extend, modify bool) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg5))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(step MovementStep, direction int, extend, modify bool) (ok bool))
	}

	var _step MovementStep // out
	var _direction int     // out
	var _extend bool       // out
	var _modify bool       // out

	_step = MovementStep(arg1)
	_direction = int(arg2)
	if arg3 != 0 {
		_extend = true
	}
	if arg4 != 0 {
		_modify = true
	}

	ok := f(_step, _direction, _extend, _modify)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeView_ConnectRowActivated
func _gotk4_gtk4_TreeView_ConnectRowActivated(arg0 C.gpointer, arg1 *C.GtkTreePath, arg2 *C.GtkTreeViewColumn, arg3 C.guintptr) {
	var f func(path *TreePath, column *TreeViewColumn)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(path *TreePath, column *TreeViewColumn))
	}

	var _path *TreePath         // out
	var _column *TreeViewColumn // out

	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	if arg2 != nil {
		_column = wrapTreeViewColumn(coreglib.Take(unsafe.Pointer(arg2)))
	}

	f(_path, _column)
}

//export _gotk4_gtk4_TreeView_ConnectRowCollapsed
func _gotk4_gtk4_TreeView_ConnectRowCollapsed(arg0 C.gpointer, arg1 *C.GtkTreeIter, arg2 *C.GtkTreePath, arg3 C.guintptr) {
	var f func(iter *TreeIter, path *TreePath)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(iter *TreeIter, path *TreePath))
	}

	var _iter *TreeIter // out
	var _path *TreePath // out

	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	f(_iter, _path)
}

//export _gotk4_gtk4_TreeView_ConnectRowExpanded
func _gotk4_gtk4_TreeView_ConnectRowExpanded(arg0 C.gpointer, arg1 *C.GtkTreeIter, arg2 *C.GtkTreePath, arg3 C.guintptr) {
	var f func(iter *TreeIter, path *TreePath)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(iter *TreeIter, path *TreePath))
	}

	var _iter *TreeIter // out
	var _path *TreePath // out

	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	f(_iter, _path)
}

//export _gotk4_gtk4_TreeView_ConnectSelectAll
func _gotk4_gtk4_TreeView_ConnectSelectAll(arg0 C.gpointer, arg1 C.guintptr) (cret C.gboolean) {
	var f func() (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (ok bool))
	}

	ok := f()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeView_ConnectSelectCursorParent
func _gotk4_gtk4_TreeView_ConnectSelectCursorParent(arg0 C.gpointer, arg1 C.guintptr) (cret C.gboolean) {
	var f func() (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (ok bool))
	}

	ok := f()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeView_ConnectSelectCursorRow
func _gotk4_gtk4_TreeView_ConnectSelectCursorRow(arg0 C.gpointer, arg1 C.gboolean, arg2 C.guintptr) (cret C.gboolean) {
	var f func(object bool) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(object bool) (ok bool))
	}

	var _object bool // out

	if arg1 != 0 {
		_object = true
	}

	ok := f(_object)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeView_ConnectStartInteractiveSearch
func _gotk4_gtk4_TreeView_ConnectStartInteractiveSearch(arg0 C.gpointer, arg1 C.guintptr) (cret C.gboolean) {
	var f func() (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (ok bool))
	}

	ok := f()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeView_ConnectTestCollapseRow
func _gotk4_gtk4_TreeView_ConnectTestCollapseRow(arg0 C.gpointer, arg1 *C.GtkTreeIter, arg2 *C.GtkTreePath, arg3 C.guintptr) (cret C.gboolean) {
	var f func(iter *TreeIter, path *TreePath) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(iter *TreeIter, path *TreePath) (ok bool))
	}

	var _iter *TreeIter // out
	var _path *TreePath // out

	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	ok := f(_iter, _path)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeView_ConnectTestExpandRow
func _gotk4_gtk4_TreeView_ConnectTestExpandRow(arg0 C.gpointer, arg1 *C.GtkTreeIter, arg2 *C.GtkTreePath, arg3 C.guintptr) (cret C.gboolean) {
	var f func(iter *TreeIter, path *TreePath) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg3))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(iter *TreeIter, path *TreePath) (ok bool))
	}

	var _iter *TreeIter // out
	var _path *TreePath // out

	_iter = (*TreeIter)(gextras.NewStructNative(unsafe.Pointer(arg1)))
	_path = (*TreePath)(gextras.NewStructNative(unsafe.Pointer(arg2)))

	ok := f(_iter, _path)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeView_ConnectToggleCursorRow
func _gotk4_gtk4_TreeView_ConnectToggleCursorRow(arg0 C.gpointer, arg1 C.guintptr) (cret C.gboolean) {
	var f func() (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (ok bool))
	}

	ok := f()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeView_ConnectUnselectAll
func _gotk4_gtk4_TreeView_ConnectUnselectAll(arg0 C.gpointer, arg1 C.guintptr) (cret C.gboolean) {
	var f func() (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (ok bool))
	}

	ok := f()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_TreeViewColumn_ConnectClicked
func _gotk4_gtk4_TreeViewColumn_ConnectClicked(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_WidgetClass_contains
func _gotk4_gtk4_WidgetClass_contains(arg0 *C.GtkWidget, arg1 C.double, arg2 C.double) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.Contains == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.Contains, got none")
	}

	var _x float64 // out
	var _y float64 // out

	_x = float64(arg1)
	_y = float64(arg2)

	ok := overrides.Contains(_x, _y)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_WidgetClass_direction_changed
func _gotk4_gtk4_WidgetClass_direction_changed(arg0 *C.GtkWidget, arg1 C.GtkTextDirection) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.DirectionChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.DirectionChanged, got none")
	}

	var _previousDirection TextDirection // out

	_previousDirection = TextDirection(arg1)

	overrides.DirectionChanged(_previousDirection)
}

//export _gotk4_gtk4_WidgetClass_focus
func _gotk4_gtk4_WidgetClass_focus(arg0 *C.GtkWidget, arg1 C.GtkDirectionType) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.Focus == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.Focus, got none")
	}

	var _direction DirectionType // out

	_direction = DirectionType(arg1)

	ok := overrides.Focus(_direction)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_WidgetClass_get_request_mode
func _gotk4_gtk4_WidgetClass_get_request_mode(arg0 *C.GtkWidget) (cret C.GtkSizeRequestMode) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.RequestMode == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.RequestMode, got none")
	}

	sizeRequestMode := overrides.RequestMode()

	var _ SizeRequestMode

	cret = C.GtkSizeRequestMode(sizeRequestMode)

	return cret
}

//export _gotk4_gtk4_WidgetClass_grab_focus
func _gotk4_gtk4_WidgetClass_grab_focus(arg0 *C.GtkWidget) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.GrabFocus == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.GrabFocus, got none")
	}

	ok := overrides.GrabFocus()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_WidgetClass_hide
func _gotk4_gtk4_WidgetClass_hide(arg0 *C.GtkWidget) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.Hide == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.Hide, got none")
	}

	overrides.Hide()
}

//export _gotk4_gtk4_WidgetClass_keynav_failed
func _gotk4_gtk4_WidgetClass_keynav_failed(arg0 *C.GtkWidget, arg1 C.GtkDirectionType) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.KeynavFailed == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.KeynavFailed, got none")
	}

	var _direction DirectionType // out

	_direction = DirectionType(arg1)

	ok := overrides.KeynavFailed(_direction)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_WidgetClass_map
func _gotk4_gtk4_WidgetClass_map(arg0 *C.GtkWidget) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.Map == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.Map, got none")
	}

	overrides.Map()
}

//export _gotk4_gtk4_WidgetClass_measure
func _gotk4_gtk4_WidgetClass_measure(arg0 *C.GtkWidget, arg1 C.GtkOrientation, arg2 C.int, arg3 *C.int, arg4 *C.int, arg5 *C.int, arg6 *C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.Measure == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.Measure, got none")
	}

	var _orientation Orientation // out
	var _forSize int             // out

	_orientation = Orientation(arg1)
	_forSize = int(arg2)

	minimum, natural, minimumBaseline, naturalBaseline := overrides.Measure(_orientation, _forSize)

	var _ int
	var _ int
	var _ int
	var _ int

	*arg3 = C.int(minimum)
	*arg4 = C.int(natural)
	*arg5 = C.int(minimumBaseline)
	*arg6 = C.int(naturalBaseline)
}

//export _gotk4_gtk4_WidgetClass_mnemonic_activate
func _gotk4_gtk4_WidgetClass_mnemonic_activate(arg0 *C.GtkWidget, arg1 C.gboolean) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.MnemonicActivate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.MnemonicActivate, got none")
	}

	var _groupCycling bool // out

	if arg1 != 0 {
		_groupCycling = true
	}

	ok := overrides.MnemonicActivate(_groupCycling)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_WidgetClass_move_focus
func _gotk4_gtk4_WidgetClass_move_focus(arg0 *C.GtkWidget, arg1 C.GtkDirectionType) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.MoveFocus == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.MoveFocus, got none")
	}

	var _direction DirectionType // out

	_direction = DirectionType(arg1)

	overrides.MoveFocus(_direction)
}

//export _gotk4_gtk4_WidgetClass_query_tooltip
func _gotk4_gtk4_WidgetClass_query_tooltip(arg0 *C.GtkWidget, arg1 C.int, arg2 C.int, arg3 C.gboolean, arg4 *C.GtkTooltip) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.QueryTooltip == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.QueryTooltip, got none")
	}

	var _x int                // out
	var _y int                // out
	var _keyboardTooltip bool // out
	var _tooltip *Tooltip     // out

	_x = int(arg1)
	_y = int(arg2)
	if arg3 != 0 {
		_keyboardTooltip = true
	}
	_tooltip = wrapTooltip(coreglib.Take(unsafe.Pointer(arg4)))

	ok := overrides.QueryTooltip(_x, _y, _keyboardTooltip, _tooltip)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_WidgetClass_realize
func _gotk4_gtk4_WidgetClass_realize(arg0 *C.GtkWidget) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.Realize == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.Realize, got none")
	}

	overrides.Realize()
}

//export _gotk4_gtk4_WidgetClass_root
func _gotk4_gtk4_WidgetClass_root(arg0 *C.GtkWidget) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.Root == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.Root, got none")
	}

	overrides.Root()
}

//export _gotk4_gtk4_WidgetClass_set_focus_child
func _gotk4_gtk4_WidgetClass_set_focus_child(arg0 *C.GtkWidget, arg1 *C.GtkWidget) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.SetFocusChild == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.SetFocusChild, got none")
	}

	var _child Widgetter // out

	if arg1 != nil {
		{
			objptr := unsafe.Pointer(arg1)

			object := coreglib.Take(objptr)
			casted := object.WalkCast(func(obj coreglib.Objector) bool {
				_, ok := obj.(Widgetter)
				return ok
			})
			rv, ok := casted.(Widgetter)
			if !ok {
				panic("no marshaler for " + object.TypeFromInstance().String() + " matching gtk.Widgetter")
			}
			_child = rv
		}
	}

	overrides.SetFocusChild(_child)
}

//export _gotk4_gtk4_WidgetClass_show
func _gotk4_gtk4_WidgetClass_show(arg0 *C.GtkWidget) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.Show == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.Show, got none")
	}

	overrides.Show()
}

//export _gotk4_gtk4_WidgetClass_size_allocate
func _gotk4_gtk4_WidgetClass_size_allocate(arg0 *C.GtkWidget, arg1 C.int, arg2 C.int, arg3 C.int) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.SizeAllocate == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.SizeAllocate, got none")
	}

	var _width int    // out
	var _height int   // out
	var _baseline int // out

	_width = int(arg1)
	_height = int(arg2)
	_baseline = int(arg3)

	overrides.SizeAllocate(_width, _height, _baseline)
}

//export _gotk4_gtk4_WidgetClass_snapshot
func _gotk4_gtk4_WidgetClass_snapshot(arg0 *C.GtkWidget, arg1 *C.GtkSnapshot) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.Snapshot == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.Snapshot, got none")
	}

	var _snapshot *Snapshot // out

	_snapshot = wrapSnapshot(coreglib.Take(unsafe.Pointer(arg1)))

	overrides.Snapshot(_snapshot)
}

//export _gotk4_gtk4_WidgetClass_state_flags_changed
func _gotk4_gtk4_WidgetClass_state_flags_changed(arg0 *C.GtkWidget, arg1 C.GtkStateFlags) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.StateFlagsChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.StateFlagsChanged, got none")
	}

	var _previousStateFlags StateFlags // out

	_previousStateFlags = StateFlags(arg1)

	overrides.StateFlagsChanged(_previousStateFlags)
}

//export _gotk4_gtk4_WidgetClass_system_setting_changed
func _gotk4_gtk4_WidgetClass_system_setting_changed(arg0 *C.GtkWidget, arg1 C.GtkSystemSetting) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.SystemSettingChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.SystemSettingChanged, got none")
	}

	var _settings SystemSetting // out

	_settings = SystemSetting(arg1)

	overrides.SystemSettingChanged(_settings)
}

//export _gotk4_gtk4_WidgetClass_unmap
func _gotk4_gtk4_WidgetClass_unmap(arg0 *C.GtkWidget) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.Unmap == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.Unmap, got none")
	}

	overrides.Unmap()
}

//export _gotk4_gtk4_WidgetClass_unrealize
func _gotk4_gtk4_WidgetClass_unrealize(arg0 *C.GtkWidget) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.Unrealize == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.Unrealize, got none")
	}

	overrides.Unrealize()
}

//export _gotk4_gtk4_WidgetClass_unroot
func _gotk4_gtk4_WidgetClass_unroot(arg0 *C.GtkWidget) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WidgetOverrides](instance0)
	if overrides.Unroot == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WidgetOverrides.Unroot, got none")
	}

	overrides.Unroot()
}

//export _gotk4_gtk4_Widget_ConnectDestroy
func _gotk4_gtk4_Widget_ConnectDestroy(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Widget_ConnectDirectionChanged
func _gotk4_gtk4_Widget_ConnectDirectionChanged(arg0 C.gpointer, arg1 C.GtkTextDirection, arg2 C.guintptr) {
	var f func(previousDirection TextDirection)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(previousDirection TextDirection))
	}

	var _previousDirection TextDirection // out

	_previousDirection = TextDirection(arg1)

	f(_previousDirection)
}

//export _gotk4_gtk4_Widget_ConnectHide
func _gotk4_gtk4_Widget_ConnectHide(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Widget_ConnectKeynavFailed
func _gotk4_gtk4_Widget_ConnectKeynavFailed(arg0 C.gpointer, arg1 C.GtkDirectionType, arg2 C.guintptr) (cret C.gboolean) {
	var f func(direction DirectionType) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(direction DirectionType) (ok bool))
	}

	var _direction DirectionType // out

	_direction = DirectionType(arg1)

	ok := f(_direction)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Widget_ConnectMap
func _gotk4_gtk4_Widget_ConnectMap(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Widget_ConnectMnemonicActivate
func _gotk4_gtk4_Widget_ConnectMnemonicActivate(arg0 C.gpointer, arg1 C.gboolean, arg2 C.guintptr) (cret C.gboolean) {
	var f func(groupCycling bool) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(groupCycling bool) (ok bool))
	}

	var _groupCycling bool // out

	if arg1 != 0 {
		_groupCycling = true
	}

	ok := f(_groupCycling)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Widget_ConnectMoveFocus
func _gotk4_gtk4_Widget_ConnectMoveFocus(arg0 C.gpointer, arg1 C.GtkDirectionType, arg2 C.guintptr) {
	var f func(direction DirectionType)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(direction DirectionType))
	}

	var _direction DirectionType // out

	_direction = DirectionType(arg1)

	f(_direction)
}

//export _gotk4_gtk4_Widget_ConnectQueryTooltip
func _gotk4_gtk4_Widget_ConnectQueryTooltip(arg0 C.gpointer, arg1 C.gint, arg2 C.gint, arg3 C.gboolean, arg4 *C.GtkTooltip, arg5 C.guintptr) (cret C.gboolean) {
	var f func(x, y int, keyboardMode bool, tooltip *Tooltip) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg5))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(x, y int, keyboardMode bool, tooltip *Tooltip) (ok bool))
	}

	var _x int             // out
	var _y int             // out
	var _keyboardMode bool // out
	var _tooltip *Tooltip  // out

	_x = int(arg1)
	_y = int(arg2)
	if arg3 != 0 {
		_keyboardMode = true
	}
	_tooltip = wrapTooltip(coreglib.Take(unsafe.Pointer(arg4)))

	ok := f(_x, _y, _keyboardMode, _tooltip)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Widget_ConnectRealize
func _gotk4_gtk4_Widget_ConnectRealize(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Widget_ConnectShow
func _gotk4_gtk4_Widget_ConnectShow(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Widget_ConnectStateFlagsChanged
func _gotk4_gtk4_Widget_ConnectStateFlagsChanged(arg0 C.gpointer, arg1 C.GtkStateFlags, arg2 C.guintptr) {
	var f func(flags StateFlags)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(flags StateFlags))
	}

	var _flags StateFlags // out

	_flags = StateFlags(arg1)

	f(_flags)
}

//export _gotk4_gtk4_Widget_ConnectUnmap
func _gotk4_gtk4_Widget_ConnectUnmap(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Widget_ConnectUnrealize
func _gotk4_gtk4_Widget_ConnectUnrealize(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_WindowClass_activate_default
func _gotk4_gtk4_WindowClass_activate_default(arg0 *C.GtkWindow) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WindowOverrides](instance0)
	if overrides.ActivateDefault == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WindowOverrides.ActivateDefault, got none")
	}

	overrides.ActivateDefault()
}

//export _gotk4_gtk4_WindowClass_activate_focus
func _gotk4_gtk4_WindowClass_activate_focus(arg0 *C.GtkWindow) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WindowOverrides](instance0)
	if overrides.ActivateFocus == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WindowOverrides.ActivateFocus, got none")
	}

	overrides.ActivateFocus()
}

//export _gotk4_gtk4_WindowClass_close_request
func _gotk4_gtk4_WindowClass_close_request(arg0 *C.GtkWindow) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WindowOverrides](instance0)
	if overrides.CloseRequest == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WindowOverrides.CloseRequest, got none")
	}

	ok := overrides.CloseRequest()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_WindowClass_enable_debugging
func _gotk4_gtk4_WindowClass_enable_debugging(arg0 *C.GtkWindow, arg1 C.gboolean) (cret C.gboolean) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WindowOverrides](instance0)
	if overrides.EnableDebugging == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WindowOverrides.EnableDebugging, got none")
	}

	var _toggle bool // out

	if arg1 != 0 {
		_toggle = true
	}

	ok := overrides.EnableDebugging(_toggle)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_WindowClass_keys_changed
func _gotk4_gtk4_WindowClass_keys_changed(arg0 *C.GtkWindow) {
	instance0 := coreglib.Take(unsafe.Pointer(arg0))
	overrides := coreglib.OverridesFromObj[WindowOverrides](instance0)
	if overrides.KeysChanged == nil {
		panic("gotk4: " + instance0.TypeFromInstance().String() + ": expected WindowOverrides.KeysChanged, got none")
	}

	overrides.KeysChanged()
}

//export _gotk4_gtk4_Window_ConnectActivateDefault
func _gotk4_gtk4_Window_ConnectActivateDefault(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Window_ConnectActivateFocus
func _gotk4_gtk4_Window_ConnectActivateFocus(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}

//export _gotk4_gtk4_Window_ConnectCloseRequest
func _gotk4_gtk4_Window_ConnectCloseRequest(arg0 C.gpointer, arg1 C.guintptr) (cret C.gboolean) {
	var f func() (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func() (ok bool))
	}

	ok := f()

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Window_ConnectEnableDebugging
func _gotk4_gtk4_Window_ConnectEnableDebugging(arg0 C.gpointer, arg1 C.gboolean, arg2 C.guintptr) (cret C.gboolean) {
	var f func(toggle bool) (ok bool)
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg2))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func(toggle bool) (ok bool))
	}

	var _toggle bool // out

	if arg1 != 0 {
		_toggle = true
	}

	ok := f(_toggle)

	var _ bool

	if ok {
		cret = C.TRUE
	}

	return cret
}

//export _gotk4_gtk4_Window_ConnectKeysChanged
func _gotk4_gtk4_Window_ConnectKeysChanged(arg0 C.gpointer, arg1 C.guintptr) {
	var f func()
	{
		closure := coreglib.ConnectedGeneratedClosure(uintptr(arg1))
		if closure == nil {
			panic("given unknown closure user_data")
		}
		defer closure.TryRepanic()

		f = closure.Func.(func())
	}

	f()
}
