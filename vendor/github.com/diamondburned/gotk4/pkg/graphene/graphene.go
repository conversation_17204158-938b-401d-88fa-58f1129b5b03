// Code generated by girgen. DO NOT EDIT.

package graphene

import (
	"fmt"
	"runtime"
	_ "runtime/cgo"
	"unsafe"

	"github.com/diamondburned/gotk4/pkg/core/gextras"
	coreglib "github.com/diamondburned/gotk4/pkg/core/glib"
)

// #cgo pkg-config: graphene-gobject-1.0 graphene-1.0
// #cgo CFLAGS: -Wno-deprecated-declarations
// #include <stdlib.h>
// #include <glib-object.h>
// #include <graphene-gobject.h>
import "C"

// GType values.
var (
	GTypeBox        = coreglib.Type(C.graphene_box_get_type())
	GTypeEuler      = coreglib.Type(C.graphene_euler_get_type())
	GTypeFrustum    = coreglib.Type(C.graphene_frustum_get_type())
	GTypeMatrix     = coreglib.Type(C.graphene_matrix_get_type())
	GTypePlane      = coreglib.Type(C.graphene_plane_get_type())
	GTypePoint      = coreglib.Type(C.graphene_point_get_type())
	GTypePoint3D    = coreglib.Type(C.graphene_point3d_get_type())
	GTypeQuad       = coreglib.Type(C.graphene_quad_get_type())
	GTypeQuaternion = coreglib.Type(C.graphene_quaternion_get_type())
	GTypeRay        = coreglib.Type(C.graphene_ray_get_type())
	GTypeRect       = coreglib.Type(C.graphene_rect_get_type())
	GTypeSize       = coreglib.Type(C.graphene_size_get_type())
	GTypeSphere     = coreglib.Type(C.graphene_sphere_get_type())
	GTypeTriangle   = coreglib.Type(C.graphene_triangle_get_type())
	GTypeVec2       = coreglib.Type(C.graphene_vec2_get_type())
	GTypeVec3       = coreglib.Type(C.graphene_vec3_get_type())
	GTypeVec4       = coreglib.Type(C.graphene_vec4_get_type())
)

func init() {
	coreglib.RegisterGValueMarshalers([]coreglib.TypeMarshaler{
		coreglib.TypeMarshaler{T: GTypeBox, F: marshalBox},
		coreglib.TypeMarshaler{T: GTypeEuler, F: marshalEuler},
		coreglib.TypeMarshaler{T: GTypeFrustum, F: marshalFrustum},
		coreglib.TypeMarshaler{T: GTypeMatrix, F: marshalMatrix},
		coreglib.TypeMarshaler{T: GTypePlane, F: marshalPlane},
		coreglib.TypeMarshaler{T: GTypePoint, F: marshalPoint},
		coreglib.TypeMarshaler{T: GTypePoint3D, F: marshalPoint3D},
		coreglib.TypeMarshaler{T: GTypeQuad, F: marshalQuad},
		coreglib.TypeMarshaler{T: GTypeQuaternion, F: marshalQuaternion},
		coreglib.TypeMarshaler{T: GTypeRay, F: marshalRay},
		coreglib.TypeMarshaler{T: GTypeRect, F: marshalRect},
		coreglib.TypeMarshaler{T: GTypeSize, F: marshalSize},
		coreglib.TypeMarshaler{T: GTypeSphere, F: marshalSphere},
		coreglib.TypeMarshaler{T: GTypeTriangle, F: marshalTriangle},
		coreglib.TypeMarshaler{T: GTypeVec2, F: marshalVec2},
		coreglib.TypeMarshaler{T: GTypeVec3, F: marshalVec3},
		coreglib.TypeMarshaler{T: GTypeVec4, F: marshalVec4},
	})
}

const PI = 3.141593
const PI_2 = 1.570796

// VEC2_LEN evaluates to the number of components of a #graphene_vec2_t.
//
// This symbol is useful when declaring a C array of floating point values to be
// used with graphene_vec2_init_from_float() and graphene_vec2_to_float(), e.g.
//
//	float v[GRAPHENE_VEC2_LEN];
//
//	// vec is defined elsewhere
//	graphene_vec2_to_float (&vec, v);
//
//	for (int i = 0; i < GRAPHENE_VEC2_LEN; i++)
//	  fprintf (stdout, "component d: g\n", i, v[i]);.
const VEC2_LEN = 2

// VEC3_LEN evaluates to the number of components of a #graphene_vec3_t.
//
// This symbol is useful when declaring a C array of floating point values to be
// used with graphene_vec3_init_from_float() and graphene_vec3_to_float(), e.g.
//
//	float v[GRAPHENE_VEC3_LEN];
//
//	// vec is defined elsewhere
//	graphene_vec3_to_float (&vec, v);
//
//	for (int i = 0; i < GRAPHENE_VEC2_LEN; i++)
//	  fprintf (stdout, "component d: g\n", i, v[i]);.
const VEC3_LEN = 3

// VEC4_LEN evaluates to the number of components of a #graphene_vec4_t.
//
// This symbol is useful when declaring a C array of floating point values to be
// used with graphene_vec4_init_from_float() and graphene_vec4_to_float(), e.g.
//
//	float v[GRAPHENE_VEC4_LEN];
//
//	// vec is defined elsewhere
//	graphene_vec4_to_float (&vec, v);
//
//	for (int i = 0; i < GRAPHENE_VEC4_LEN; i++)
//	  fprintf (stdout, "component d: g\n", i, v[i]);.
const VEC4_LEN = 4

// EulerOrder: specify the order of the rotations on each axis.
//
// The GRAPHENE_EULER_ORDER_DEFAULT value is special, and is used as an alias
// for one of the other orders.
type EulerOrder C.gint

const (
	// EulerOrderDefault: rotate in the default order; the default order is one
	// of the following enumeration values.
	EulerOrderDefault EulerOrder = -1
	// EulerOrderXYZ: rotate in the X, Y, and Z order. Deprecated in Graphene
	// 1.10, it's an alias for GRAPHENE_EULER_ORDER_SXYZ.
	EulerOrderXYZ EulerOrder = 0
	// EulerOrderYZX: rotate in the Y, Z, and X order. Deprecated in Graphene
	// 1.10, it's an alias for GRAPHENE_EULER_ORDER_SYZX.
	EulerOrderYZX EulerOrder = 1
	// EulerOrderZXY: rotate in the Z, X, and Y order. Deprecated in Graphene
	// 1.10, it's an alias for GRAPHENE_EULER_ORDER_SZXY.
	EulerOrderZXY EulerOrder = 2
	// EulerOrderXZY: rotate in the X, Z, and Y order. Deprecated in Graphene
	// 1.10, it's an alias for GRAPHENE_EULER_ORDER_SXZY.
	EulerOrderXZY EulerOrder = 3
	// EulerOrderYXZ: rotate in the Y, X, and Z order. Deprecated in Graphene
	// 1.10, it's an alias for GRAPHENE_EULER_ORDER_SYXZ.
	EulerOrderYXZ EulerOrder = 4
	// EulerOrderZYX: rotate in the Z, Y, and X order. Deprecated in Graphene
	// 1.10, it's an alias for GRAPHENE_EULER_ORDER_SZYX.
	EulerOrderZYX EulerOrder = 5
	// EulerOrderSXYZ defines a static rotation along the X, Y, and Z axes
	// (Since: 1.10).
	EulerOrderSXYZ EulerOrder = 6
	// EulerOrderSXYX defines a static rotation along the X, Y, and X axes
	// (Since: 1.10).
	EulerOrderSXYX EulerOrder = 7
	// EulerOrderSXZY defines a static rotation along the X, Z, and Y axes
	// (Since: 1.10).
	EulerOrderSXZY EulerOrder = 8
	// EulerOrderSXZX defines a static rotation along the X, Z, and X axes
	// (Since: 1.10).
	EulerOrderSXZX EulerOrder = 9
	// EulerOrderSYZX defines a static rotation along the Y, Z, and X axes
	// (Since: 1.10).
	EulerOrderSYZX EulerOrder = 10
	// EulerOrderSYZY defines a static rotation along the Y, Z, and Y axes
	// (Since: 1.10).
	EulerOrderSYZY EulerOrder = 11
	// EulerOrderSYXZ defines a static rotation along the Y, X, and Z axes
	// (Since: 1.10).
	EulerOrderSYXZ EulerOrder = 12
	// EulerOrderSYXY defines a static rotation along the Y, X, and Y axes
	// (Since: 1.10).
	EulerOrderSYXY EulerOrder = 13
	// EulerOrderSZXY defines a static rotation along the Z, X, and Y axes
	// (Since: 1.10).
	EulerOrderSZXY EulerOrder = 14
	// EulerOrderSZXZ defines a static rotation along the Z, X, and Z axes
	// (Since: 1.10).
	EulerOrderSZXZ EulerOrder = 15
	// EulerOrderSZYX defines a static rotation along the Z, Y, and X axes
	// (Since: 1.10).
	EulerOrderSZYX EulerOrder = 16
	// EulerOrderSZYZ defines a static rotation along the Z, Y, and Z axes
	// (Since: 1.10).
	EulerOrderSZYZ EulerOrder = 17
	// EulerOrderRZYX defines a relative rotation along the Z, Y, and X axes
	// (Since: 1.10).
	EulerOrderRZYX EulerOrder = 18
	// EulerOrderRXYX defines a relative rotation along the X, Y, and X axes
	// (Since: 1.10).
	EulerOrderRXYX EulerOrder = 19
	// EulerOrderRYZX defines a relative rotation along the Y, Z, and X axes
	// (Since: 1.10).
	EulerOrderRYZX EulerOrder = 20
	// EulerOrderRXZX defines a relative rotation along the X, Z, and X axes
	// (Since: 1.10).
	EulerOrderRXZX EulerOrder = 21
	// EulerOrderRXZY defines a relative rotation along the X, Z, and Y axes
	// (Since: 1.10).
	EulerOrderRXZY EulerOrder = 22
	// EulerOrderRYZY defines a relative rotation along the Y, Z, and Y axes
	// (Since: 1.10).
	EulerOrderRYZY EulerOrder = 23
	// EulerOrderRZXY defines a relative rotation along the Z, X, and Y axes
	// (Since: 1.10).
	EulerOrderRZXY EulerOrder = 24
	// EulerOrderRYXY defines a relative rotation along the Y, X, and Y axes
	// (Since: 1.10).
	EulerOrderRYXY EulerOrder = 25
	// EulerOrderRYXZ defines a relative rotation along the Y, X, and Z axes
	// (Since: 1.10).
	EulerOrderRYXZ EulerOrder = 26
	// EulerOrderRZXZ defines a relative rotation along the Z, X, and Z axes
	// (Since: 1.10).
	EulerOrderRZXZ EulerOrder = 27
	// EulerOrderRXYZ defines a relative rotation along the X, Y, and Z axes
	// (Since: 1.10).
	EulerOrderRXYZ EulerOrder = 28
	// EulerOrderRZYZ defines a relative rotation along the Z, Y, and Z axes
	// (Since: 1.10).
	EulerOrderRZYZ EulerOrder = 29
)

// String returns the name in string for EulerOrder.
func (e EulerOrder) String() string {
	switch e {
	case EulerOrderDefault:
		return "Default"
	case EulerOrderXYZ:
		return "XYZ"
	case EulerOrderYZX:
		return "YZX"
	case EulerOrderZXY:
		return "ZXY"
	case EulerOrderXZY:
		return "XZY"
	case EulerOrderYXZ:
		return "YXZ"
	case EulerOrderZYX:
		return "ZYX"
	case EulerOrderSXYZ:
		return "SXYZ"
	case EulerOrderSXYX:
		return "SXYX"
	case EulerOrderSXZY:
		return "SXZY"
	case EulerOrderSXZX:
		return "SXZX"
	case EulerOrderSYZX:
		return "SYZX"
	case EulerOrderSYZY:
		return "SYZY"
	case EulerOrderSYXZ:
		return "SYXZ"
	case EulerOrderSYXY:
		return "SYXY"
	case EulerOrderSZXY:
		return "SZXY"
	case EulerOrderSZXZ:
		return "SZXZ"
	case EulerOrderSZYX:
		return "SZYX"
	case EulerOrderSZYZ:
		return "SZYZ"
	case EulerOrderRZYX:
		return "RZYX"
	case EulerOrderRXYX:
		return "RXYX"
	case EulerOrderRYZX:
		return "RYZX"
	case EulerOrderRXZX:
		return "RXZX"
	case EulerOrderRXZY:
		return "RXZY"
	case EulerOrderRYZY:
		return "RYZY"
	case EulerOrderRZXY:
		return "RZXY"
	case EulerOrderRYXY:
		return "RYXY"
	case EulerOrderRYXZ:
		return "RYXZ"
	case EulerOrderRZXZ:
		return "RZXZ"
	case EulerOrderRXYZ:
		return "RXYZ"
	case EulerOrderRZYZ:
		return "RZYZ"
	default:
		return fmt.Sprintf("EulerOrder(%d)", e)
	}
}

// RayIntersectionKind: type of intersection.
type RayIntersectionKind C.gint

const (
	// RayIntersectionKindNone: no intersection.
	RayIntersectionKindNone RayIntersectionKind = iota
	// RayIntersectionKindEnter: ray is entering the intersected object.
	RayIntersectionKindEnter
	// RayIntersectionKindLeave: ray is leaving the intersected object.
	RayIntersectionKindLeave
)

// String returns the name in string for RayIntersectionKind.
func (r RayIntersectionKind) String() string {
	switch r {
	case RayIntersectionKindNone:
		return "None"
	case RayIntersectionKindEnter:
		return "Enter"
	case RayIntersectionKindLeave:
		return "Leave"
	default:
		return fmt.Sprintf("RayIntersectionKind(%d)", r)
	}
}

// Box: 3D box, described as the volume between a minimum and a maximum
// vertices.
//
// An instance of this type is always passed by reference.
type Box struct {
	*box
}

// box is the struct that's finalized.
type box struct {
	native *C.graphene_box_t
}

func marshalBox(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Box{&box{(*C.graphene_box_t)(b)}}, nil
}

// NewBoxAlloc constructs a struct Box.
func NewBoxAlloc() *Box {
	var _cret *C.graphene_box_t // in

	_cret = C.graphene_box_alloc()

	var _box *Box // out

	_box = (*Box)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_box)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_box_free((*C.graphene_box_t)(intern.C))
		},
	)

	return _box
}

// ContainsBox checks whether the #graphene_box_t a contains the given
// #graphene_box_t b.
//
// The function takes the following parameters:
//
//   - b: #graphene_box_t.
//
// The function returns the following values:
//
//   - ok: true if the box is contained in the given box.
func (a *Box) ContainsBox(b *Box) bool {
	var _arg0 *C.graphene_box_t // out
	var _arg1 *C.graphene_box_t // out
	var _cret C._Bool           // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_box_contains_box(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// ContainsPoint checks whether box contains the given point.
//
// The function takes the following parameters:
//
//   - point coordinates to check.
//
// The function returns the following values:
//
//   - ok: true if the point is contained in the given box.
func (box *Box) ContainsPoint(point *Point3D) bool {
	var _arg0 *C.graphene_box_t     // out
	var _arg1 *C.graphene_point3d_t // out
	var _cret C._Bool               // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(point)))

	_cret = C.graphene_box_contains_point(_arg0, _arg1)
	runtime.KeepAlive(box)
	runtime.KeepAlive(point)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Equal checks whether the two given boxes are equal.
//
// The function takes the following parameters:
//
//   - b: #graphene_box_t.
//
// The function returns the following values:
//
//   - ok: true if the boxes are equal.
func (a *Box) Equal(b *Box) bool {
	var _arg0 *C.graphene_box_t // out
	var _arg1 *C.graphene_box_t // out
	var _cret C._Bool           // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_box_equal(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Expand expands the dimensions of box to include the coordinates at point.
//
// The function takes the following parameters:
//
//   - point coordinates of the point to include.
//
// The function returns the following values:
//
//   - res: return location for the expanded box.
func (box *Box) Expand(point *Point3D) *Box {
	var _arg0 *C.graphene_box_t     // out
	var _arg1 *C.graphene_point3d_t // out
	var _arg2 C.graphene_box_t      // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(point)))

	C.graphene_box_expand(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(box)
	runtime.KeepAlive(point)

	var _res *Box // out

	_res = (*Box)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// ExpandScalar expands the dimensions of box by the given scalar value.
//
// If scalar is positive, the #graphene_box_t will grow; if scalar is negative,
// the #graphene_box_t will shrink.
//
// The function takes the following parameters:
//
//   - scalar value.
//
// The function returns the following values:
//
//   - res: return location for the expanded box.
func (box *Box) ExpandScalar(scalar float32) *Box {
	var _arg0 *C.graphene_box_t // out
	var _arg1 C.float           // out
	var _arg2 C.graphene_box_t  // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))
	_arg1 = C.float(scalar)

	C.graphene_box_expand_scalar(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(box)
	runtime.KeepAlive(scalar)

	var _res *Box // out

	_res = (*Box)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// ExpandVec3 expands the dimensions of box to include the coordinates of the
// given vector.
//
// The function takes the following parameters:
//
//   - vec coordinates of the point to include, as a #graphene_vec3_t.
//
// The function returns the following values:
//
//   - res: return location for the expanded box.
func (box *Box) ExpandVec3(vec *Vec3) *Box {
	var _arg0 *C.graphene_box_t  // out
	var _arg1 *C.graphene_vec3_t // out
	var _arg2 C.graphene_box_t   // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(vec)))

	C.graphene_box_expand_vec3(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(box)
	runtime.KeepAlive(vec)

	var _res *Box // out

	_res = (*Box)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// BoundingSphere computes the bounding #graphene_sphere_t capable of containing
// the given #graphene_box_t.
//
// The function returns the following values:
//
//   - sphere: return location for the bounding sphere.
func (box *Box) BoundingSphere() *Sphere {
	var _arg0 *C.graphene_box_t   // out
	var _arg1 C.graphene_sphere_t // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))

	C.graphene_box_get_bounding_sphere(_arg0, &_arg1)
	runtime.KeepAlive(box)

	var _sphere *Sphere // out

	_sphere = (*Sphere)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _sphere
}

// Center retrieves the coordinates of the center of a #graphene_box_t.
//
// The function returns the following values:
//
//   - center: return location for the coordinates of the center.
func (box *Box) Center() *Point3D {
	var _arg0 *C.graphene_box_t    // out
	var _arg1 C.graphene_point3d_t // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))

	C.graphene_box_get_center(_arg0, &_arg1)
	runtime.KeepAlive(box)

	var _center *Point3D // out

	_center = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _center
}

// Depth retrieves the size of the box on the Z axis.
//
// The function returns the following values:
//
//   - gfloat: depth of the box.
func (box *Box) Depth() float32 {
	var _arg0 *C.graphene_box_t // out
	var _cret C.float           // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))

	_cret = C.graphene_box_get_depth(_arg0)
	runtime.KeepAlive(box)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Height retrieves the size of the box on the Y axis.
//
// The function returns the following values:
//
//   - gfloat: height of the box.
func (box *Box) Height() float32 {
	var _arg0 *C.graphene_box_t // out
	var _cret C.float           // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))

	_cret = C.graphene_box_get_height(_arg0)
	runtime.KeepAlive(box)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Max retrieves the coordinates of the maximum point of the given
// #graphene_box_t.
//
// The function returns the following values:
//
//   - max: return location for the maximum point.
func (box *Box) Max() *Point3D {
	var _arg0 *C.graphene_box_t    // out
	var _arg1 C.graphene_point3d_t // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))

	C.graphene_box_get_max(_arg0, &_arg1)
	runtime.KeepAlive(box)

	var _max *Point3D // out

	_max = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _max
}

// Min retrieves the coordinates of the minimum point of the given
// #graphene_box_t.
//
// The function returns the following values:
//
//   - min: return location for the minimum point.
func (box *Box) Min() *Point3D {
	var _arg0 *C.graphene_box_t    // out
	var _arg1 C.graphene_point3d_t // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))

	C.graphene_box_get_min(_arg0, &_arg1)
	runtime.KeepAlive(box)

	var _min *Point3D // out

	_min = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _min
}

// Size retrieves the size of the box on all three axes, and stores it into the
// given size vector.
//
// The function returns the following values:
//
//   - size: return location for the size.
func (box *Box) Size() *Vec3 {
	var _arg0 *C.graphene_box_t // out
	var _arg1 C.graphene_vec3_t // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))

	C.graphene_box_get_size(_arg0, &_arg1)
	runtime.KeepAlive(box)

	var _size *Vec3 // out

	_size = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _size
}

// Vertices computes the vertices of the given #graphene_box_t.
//
// The function returns the following values:
//
//   - vertices: return location for an array of 8 #graphene_vec3_t.
func (box *Box) Vertices() [8]Vec3 {
	var _arg0 *C.graphene_box_t    // out
	var _arg1 [8]C.graphene_vec3_t // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))

	C.graphene_box_get_vertices(_arg0, &_arg1[0])
	runtime.KeepAlive(box)

	var _vertices [8]Vec3 // out

	{
		src := &_arg1
		for i := 0; i < 8; i++ {
			_vertices[i] = *(*Vec3)(gextras.NewStructNative(unsafe.Pointer((&src[i]))))
		}
	}

	return _vertices
}

// Width retrieves the size of the box on the X axis.
//
// The function returns the following values:
//
//   - gfloat: width of the box.
func (box *Box) Width() float32 {
	var _arg0 *C.graphene_box_t // out
	var _cret C.float           // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))

	_cret = C.graphene_box_get_width(_arg0)
	runtime.KeepAlive(box)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Init initializes the given #graphene_box_t with two vertices.
//
// The function takes the following parameters:
//
//   - min (optional) coordinates of the minimum vertex.
//   - max (optional) coordinates of the maximum vertex.
//
// The function returns the following values:
//
//   - ret: initialized #graphene_box_t.
func (box *Box) Init(min *Point3D, max *Point3D) *Box {
	var _arg0 *C.graphene_box_t     // out
	var _arg1 *C.graphene_point3d_t // out
	var _arg2 *C.graphene_point3d_t // out
	var _cret *C.graphene_box_t     // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))
	if min != nil {
		_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(min)))
	}
	if max != nil {
		_arg2 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(max)))
	}

	_cret = C.graphene_box_init(_arg0, _arg1, _arg2)
	runtime.KeepAlive(box)
	runtime.KeepAlive(min)
	runtime.KeepAlive(max)

	var _ret *Box // out

	_ret = (*Box)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _ret
}

// InitFromBox initializes the given #graphene_box_t with the vertices of
// another #graphene_box_t.
//
// The function takes the following parameters:
//
//   - src: #graphene_box_t.
//
// The function returns the following values:
//
//   - ret: initialized #graphene_box_t.
func (box *Box) InitFromBox(src *Box) *Box {
	var _arg0 *C.graphene_box_t // out
	var _arg1 *C.graphene_box_t // out
	var _cret *C.graphene_box_t // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))
	_arg1 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_box_init_from_box(_arg0, _arg1)
	runtime.KeepAlive(box)
	runtime.KeepAlive(src)

	var _ret *Box // out

	_ret = (*Box)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _ret
}

// InitFromPoints initializes the given #graphene_box_t with the given array of
// vertices.
//
// If n_points is 0, the returned box is initialized with graphene_box_empty().
//
// The function takes the following parameters:
//
//   - points: array of #graphene_point3d_t.
//
// The function returns the following values:
//
//   - ret: initialized #graphene_box_t.
func (box *Box) InitFromPoints(points []Point3D) *Box {
	var _arg0 *C.graphene_box_t     // out
	var _arg2 *C.graphene_point3d_t // out
	var _arg1 C.uint
	var _cret *C.graphene_box_t // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))
	_arg1 = (C.uint)(len(points))
	_arg2 = (*C.graphene_point3d_t)(C.calloc(C.size_t(len(points)), C.size_t(C.sizeof_graphene_point3d_t)))
	defer C.free(unsafe.Pointer(_arg2))
	{
		out := unsafe.Slice((*C.graphene_point3d_t)(_arg2), len(points))
		for i := range points {
			out[i] = *(*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer((&points[i]))))
		}
	}

	_cret = C.graphene_box_init_from_points(_arg0, _arg1, _arg2)
	runtime.KeepAlive(box)
	runtime.KeepAlive(points)

	var _ret *Box // out

	_ret = (*Box)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _ret
}

// InitFromVec3 initializes the given #graphene_box_t with two vertices stored
// inside #graphene_vec3_t.
//
// The function takes the following parameters:
//
//   - min (optional) coordinates of the minimum vertex.
//   - max (optional) coordinates of the maximum vertex.
//
// The function returns the following values:
//
//   - ret: initialized #graphene_box_t.
func (box *Box) InitFromVec3(min *Vec3, max *Vec3) *Box {
	var _arg0 *C.graphene_box_t  // out
	var _arg1 *C.graphene_vec3_t // out
	var _arg2 *C.graphene_vec3_t // out
	var _cret *C.graphene_box_t  // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))
	if min != nil {
		_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(min)))
	}
	if max != nil {
		_arg2 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(max)))
	}

	_cret = C.graphene_box_init_from_vec3(_arg0, _arg1, _arg2)
	runtime.KeepAlive(box)
	runtime.KeepAlive(min)
	runtime.KeepAlive(max)

	var _ret *Box // out

	_ret = (*Box)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _ret
}

// InitFromVectors initializes the given #graphene_box_t with the given array of
// vertices.
//
// If n_vectors is 0, the returned box is initialized with graphene_box_empty().
//
// The function takes the following parameters:
//
//   - vectors: array of #graphene_vec3_t.
//
// The function returns the following values:
//
//   - ret: initialized #graphene_box_t.
func (box *Box) InitFromVectors(vectors []Vec3) *Box {
	var _arg0 *C.graphene_box_t  // out
	var _arg2 *C.graphene_vec3_t // out
	var _arg1 C.uint
	var _cret *C.graphene_box_t // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))
	_arg1 = (C.uint)(len(vectors))
	_arg2 = (*C.graphene_vec3_t)(C.calloc(C.size_t(len(vectors)), C.size_t(C.sizeof_graphene_vec3_t)))
	defer C.free(unsafe.Pointer(_arg2))
	{
		out := unsafe.Slice((*C.graphene_vec3_t)(_arg2), len(vectors))
		for i := range vectors {
			out[i] = *(*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer((&vectors[i]))))
		}
	}

	_cret = C.graphene_box_init_from_vectors(_arg0, _arg1, _arg2)
	runtime.KeepAlive(box)
	runtime.KeepAlive(vectors)

	var _ret *Box // out

	_ret = (*Box)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _ret
}

// Intersection intersects the two given #graphene_box_t.
//
// If the two boxes do not intersect, res will contain a degenerate box
// initialized with graphene_box_empty().
//
// The function takes the following parameters:
//
//   - b: #graphene_box_t.
//
// The function returns the following values:
//
//   - res (optional): return location for the result.
//   - ok: true if the two boxes intersect.
func (a *Box) Intersection(b *Box) (*Box, bool) {
	var _arg0 *C.graphene_box_t // out
	var _arg1 *C.graphene_box_t // out
	var _arg2 C.graphene_box_t  // in
	var _cret C._Bool           // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_box_intersection(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Box // out
	var _ok bool  // out

	_res = (*Box)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))
	if _cret {
		_ok = true
	}

	return _res, _ok
}

// Union unions the two given #graphene_box_t.
//
// The function takes the following parameters:
//
//   - b: box to union to a.
//
// The function returns the following values:
//
//   - res: return location for the result.
func (a *Box) Union(b *Box) *Box {
	var _arg0 *C.graphene_box_t // out
	var _arg1 *C.graphene_box_t // out
	var _arg2 C.graphene_box_t  // in

	_arg0 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_box_union(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Box // out

	_res = (*Box)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// BoxEmpty: degenerate #graphene_box_t that can only be expanded.
//
// The returned value is owned by Graphene and should not be modified or freed.
//
// The function returns the following values:
//
//   - box: #graphene_box_t.
func BoxEmpty() *Box {
	var _cret *C.graphene_box_t // in

	_cret = C.graphene_box_empty()

	var _box *Box // out

	_box = (*Box)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _box
}

// BoxInfinite: degenerate #graphene_box_t that cannot be expanded.
//
// The returned value is owned by Graphene and should not be modified or freed.
//
// The function returns the following values:
//
//   - box: #graphene_box_t.
func BoxInfinite() *Box {
	var _cret *C.graphene_box_t // in

	_cret = C.graphene_box_infinite()

	var _box *Box // out

	_box = (*Box)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _box
}

// BoxMinusOne with the minimum vertex set at (-1, -1, -1) and the maximum
// vertex set at (0, 0, 0).
//
// The returned value is owned by Graphene and should not be modified or freed.
//
// The function returns the following values:
//
//   - box: #graphene_box_t.
func BoxMinusOne() *Box {
	var _cret *C.graphene_box_t // in

	_cret = C.graphene_box_minus_one()

	var _box *Box // out

	_box = (*Box)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _box
}

// BoxOne with the minimum vertex set at (0, 0, 0) and the maximum vertex set at
// (1, 1, 1).
//
// The returned value is owned by Graphene and should not be modified or freed.
//
// The function returns the following values:
//
//   - box: #graphene_box_t.
func BoxOne() *Box {
	var _cret *C.graphene_box_t // in

	_cret = C.graphene_box_one()

	var _box *Box // out

	_box = (*Box)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _box
}

// BoxOneMinusOne with the minimum vertex set at (-1, -1, -1) and the maximum
// vertex set at (1, 1, 1).
//
// The returned value is owned by Graphene and should not be modified or freed.
//
// The function returns the following values:
//
//   - box: #graphene_box_t.
func BoxOneMinusOne() *Box {
	var _cret *C.graphene_box_t // in

	_cret = C.graphene_box_one_minus_one()

	var _box *Box // out

	_box = (*Box)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _box
}

// BoxZero with both the minimum and maximum vertices set at (0, 0, 0).
//
// The returned value is owned by Graphene and should not be modified or freed.
//
// The function returns the following values:
//
//   - box: #graphene_box_t.
func BoxZero() *Box {
	var _cret *C.graphene_box_t // in

	_cret = C.graphene_box_zero()

	var _box *Box // out

	_box = (*Box)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _box
}

// Euler: describe a rotation using Euler angles.
//
// The contents of the #graphene_euler_t structure are private and should never
// be accessed directly.
//
// An instance of this type is always passed by reference.
type Euler struct {
	*euler
}

// euler is the struct that's finalized.
type euler struct {
	native *C.graphene_euler_t
}

func marshalEuler(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Euler{&euler{(*C.graphene_euler_t)(b)}}, nil
}

// NewEulerAlloc constructs a struct Euler.
func NewEulerAlloc() *Euler {
	var _cret *C.graphene_euler_t // in

	_cret = C.graphene_euler_alloc()

	var _euler *Euler // out

	_euler = (*Euler)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_euler)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_euler_free((*C.graphene_euler_t)(intern.C))
		},
	)

	return _euler
}

// Equal checks if two #graphene_euler_t are equal.
//
// The function takes the following parameters:
//
//   - b: #graphene_euler_t.
//
// The function returns the following values:
//
//   - ok: true if the two #graphene_euler_t are equal.
func (a *Euler) Equal(b *Euler) bool {
	var _arg0 *C.graphene_euler_t // out
	var _arg1 *C.graphene_euler_t // out
	var _cret C._Bool             // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_euler_equal(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Alpha retrieves the first component of the Euler angle vector, depending on
// the order of rotation.
//
// See also: graphene_euler_get_x().
//
// The function returns the following values:
//
//   - gfloat: first component of the Euler angle vector, in radians.
func (e *Euler) Alpha() float32 {
	var _arg0 *C.graphene_euler_t // out
	var _cret C.float             // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))

	_cret = C.graphene_euler_get_alpha(_arg0)
	runtime.KeepAlive(e)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Beta retrieves the second component of the Euler angle vector, depending on
// the order of rotation.
//
// See also: graphene_euler_get_y().
//
// The function returns the following values:
//
//   - gfloat: second component of the Euler angle vector, in radians.
func (e *Euler) Beta() float32 {
	var _arg0 *C.graphene_euler_t // out
	var _cret C.float             // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))

	_cret = C.graphene_euler_get_beta(_arg0)
	runtime.KeepAlive(e)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Gamma retrieves the third component of the Euler angle vector, depending on
// the order of rotation.
//
// See also: graphene_euler_get_z().
//
// The function returns the following values:
//
//   - gfloat: third component of the Euler angle vector, in radians.
func (e *Euler) Gamma() float32 {
	var _arg0 *C.graphene_euler_t // out
	var _cret C.float             // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))

	_cret = C.graphene_euler_get_gamma(_arg0)
	runtime.KeepAlive(e)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Order retrieves the order used to apply the rotations described in the
// #graphene_euler_t structure, when converting to and from other structures,
// like #graphene_quaternion_t and #graphene_matrix_t.
//
// This function does not return the GRAPHENE_EULER_ORDER_DEFAULT enumeration
// value; it will return the effective order of rotation instead.
//
// The function returns the following values:
//
//   - eulerOrder: order used to apply the rotations.
func (e *Euler) Order() EulerOrder {
	var _arg0 *C.graphene_euler_t      // out
	var _cret C.graphene_euler_order_t // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))

	_cret = C.graphene_euler_get_order(_arg0)
	runtime.KeepAlive(e)

	var _eulerOrder EulerOrder // out

	_eulerOrder = EulerOrder(_cret)

	return _eulerOrder
}

// X retrieves the rotation angle on the X axis, in degrees.
//
// The function returns the following values:
//
//   - gfloat: rotation angle.
func (e *Euler) X() float32 {
	var _arg0 *C.graphene_euler_t // out
	var _cret C.float             // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))

	_cret = C.graphene_euler_get_x(_arg0)
	runtime.KeepAlive(e)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Y retrieves the rotation angle on the Y axis, in degrees.
//
// The function returns the following values:
//
//   - gfloat: rotation angle.
func (e *Euler) Y() float32 {
	var _arg0 *C.graphene_euler_t // out
	var _cret C.float             // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))

	_cret = C.graphene_euler_get_y(_arg0)
	runtime.KeepAlive(e)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Z retrieves the rotation angle on the Z axis, in degrees.
//
// The function returns the following values:
//
//   - gfloat: rotation angle.
func (e *Euler) Z() float32 {
	var _arg0 *C.graphene_euler_t // out
	var _cret C.float             // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))

	_cret = C.graphene_euler_get_z(_arg0)
	runtime.KeepAlive(e)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Init initializes a #graphene_euler_t using the given angles.
//
// The order of the rotations is GRAPHENE_EULER_ORDER_DEFAULT.
//
// The function takes the following parameters:
//
//   - x: rotation angle on the X axis, in degrees.
//   - y: rotation angle on the Y axis, in degrees.
//   - z: rotation angle on the Z axis, in degrees.
//
// The function returns the following values:
//
//   - euler: initialized #graphene_euler_t.
func (e *Euler) Init(x float32, y float32, z float32) *Euler {
	var _arg0 *C.graphene_euler_t // out
	var _arg1 C.float             // out
	var _arg2 C.float             // out
	var _arg3 C.float             // out
	var _cret *C.graphene_euler_t // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))
	_arg1 = C.float(x)
	_arg2 = C.float(y)
	_arg3 = C.float(z)

	_cret = C.graphene_euler_init(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(e)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
	runtime.KeepAlive(z)

	var _euler *Euler // out

	_euler = (*Euler)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _euler
}

// InitFromEuler initializes a #graphene_euler_t using the angles and order of
// another #graphene_euler_t.
//
// If the #graphene_euler_t src is NULL, this function is equivalent to calling
// graphene_euler_init() with all angles set to 0.
//
// The function takes the following parameters:
//
//   - src (optional): #graphene_euler_t.
//
// The function returns the following values:
//
//   - euler: initialized #graphene_euler_t.
func (e *Euler) InitFromEuler(src *Euler) *Euler {
	var _arg0 *C.graphene_euler_t // out
	var _arg1 *C.graphene_euler_t // out
	var _cret *C.graphene_euler_t // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))
	if src != nil {
		_arg1 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(src)))
	}

	_cret = C.graphene_euler_init_from_euler(_arg0, _arg1)
	runtime.KeepAlive(e)
	runtime.KeepAlive(src)

	var _euler *Euler // out

	_euler = (*Euler)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _euler
}

// InitFromMatrix initializes a #graphene_euler_t using the given rotation
// matrix.
//
// If the #graphene_matrix_t m is NULL, the #graphene_euler_t will be
// initialized with all angles set to 0.
//
// The function takes the following parameters:
//
//   - m (optional): rotation matrix.
//   - order used to apply the rotations.
//
// The function returns the following values:
//
//   - euler: initialized #graphene_euler_t.
func (e *Euler) InitFromMatrix(m *Matrix, order EulerOrder) *Euler {
	var _arg0 *C.graphene_euler_t      // out
	var _arg1 *C.graphene_matrix_t     // out
	var _arg2 C.graphene_euler_order_t // out
	var _cret *C.graphene_euler_t      // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))
	if m != nil {
		_arg1 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	}
	_arg2 = C.graphene_euler_order_t(order)

	_cret = C.graphene_euler_init_from_matrix(_arg0, _arg1, _arg2)
	runtime.KeepAlive(e)
	runtime.KeepAlive(m)
	runtime.KeepAlive(order)

	var _euler *Euler // out

	_euler = (*Euler)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _euler
}

// InitFromQuaternion initializes a #graphene_euler_t using the given normalized
// quaternion.
//
// If the #graphene_quaternion_t q is NULL, the #graphene_euler_t will be
// initialized with all angles set to 0.
//
// The function takes the following parameters:
//
//   - q (optional): normalized #graphene_quaternion_t.
//   - order used to apply the rotations.
//
// The function returns the following values:
//
//   - euler: initialized #graphene_euler_t.
func (e *Euler) InitFromQuaternion(q *Quaternion, order EulerOrder) *Euler {
	var _arg0 *C.graphene_euler_t      // out
	var _arg1 *C.graphene_quaternion_t // out
	var _arg2 C.graphene_euler_order_t // out
	var _cret *C.graphene_euler_t      // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))
	if q != nil {
		_arg1 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))
	}
	_arg2 = C.graphene_euler_order_t(order)

	_cret = C.graphene_euler_init_from_quaternion(_arg0, _arg1, _arg2)
	runtime.KeepAlive(e)
	runtime.KeepAlive(q)
	runtime.KeepAlive(order)

	var _euler *Euler // out

	_euler = (*Euler)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _euler
}

// InitFromRadians initializes a #graphene_euler_t using the given angles and
// order of rotation.
//
// The function takes the following parameters:
//
//   - x: rotation angle on the X axis, in radians.
//   - y: rotation angle on the Y axis, in radians.
//   - z: rotation angle on the Z axis, in radians.
//   - order of rotations.
//
// The function returns the following values:
//
//   - euler: initialized #graphene_euler_t.
func (e *Euler) InitFromRadians(x float32, y float32, z float32, order EulerOrder) *Euler {
	var _arg0 *C.graphene_euler_t      // out
	var _arg1 C.float                  // out
	var _arg2 C.float                  // out
	var _arg3 C.float                  // out
	var _arg4 C.graphene_euler_order_t // out
	var _cret *C.graphene_euler_t      // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))
	_arg1 = C.float(x)
	_arg2 = C.float(y)
	_arg3 = C.float(z)
	_arg4 = C.graphene_euler_order_t(order)

	_cret = C.graphene_euler_init_from_radians(_arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(e)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
	runtime.KeepAlive(z)
	runtime.KeepAlive(order)

	var _euler *Euler // out

	_euler = (*Euler)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _euler
}

// InitFromVec3 initializes a #graphene_euler_t using the angles contained in a
// #graphene_vec3_t.
//
// If the #graphene_vec3_t v is NULL, the #graphene_euler_t will be initialized
// with all angles set to 0.
//
// The function takes the following parameters:
//
//   - v (optional) containing the rotation angles in degrees.
//   - order used to apply the rotations.
//
// The function returns the following values:
//
//   - euler: initialized #graphene_euler_t.
func (e *Euler) InitFromVec3(v *Vec3, order EulerOrder) *Euler {
	var _arg0 *C.graphene_euler_t      // out
	var _arg1 *C.graphene_vec3_t       // out
	var _arg2 C.graphene_euler_order_t // out
	var _cret *C.graphene_euler_t      // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))
	if v != nil {
		_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))
	}
	_arg2 = C.graphene_euler_order_t(order)

	_cret = C.graphene_euler_init_from_vec3(_arg0, _arg1, _arg2)
	runtime.KeepAlive(e)
	runtime.KeepAlive(v)
	runtime.KeepAlive(order)

	var _euler *Euler // out

	_euler = (*Euler)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _euler
}

// InitWithOrder initializes a #graphene_euler_t with the given angles and
// order.
//
// The function takes the following parameters:
//
//   - x: rotation angle on the X axis, in degrees.
//   - y: rotation angle on the Y axis, in degrees.
//   - z: rotation angle on the Z axis, in degrees.
//   - order used to apply the rotations.
//
// The function returns the following values:
//
//   - euler: initialized #graphene_euler_t.
func (e *Euler) InitWithOrder(x float32, y float32, z float32, order EulerOrder) *Euler {
	var _arg0 *C.graphene_euler_t      // out
	var _arg1 C.float                  // out
	var _arg2 C.float                  // out
	var _arg3 C.float                  // out
	var _arg4 C.graphene_euler_order_t // out
	var _cret *C.graphene_euler_t      // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))
	_arg1 = C.float(x)
	_arg2 = C.float(y)
	_arg3 = C.float(z)
	_arg4 = C.graphene_euler_order_t(order)

	_cret = C.graphene_euler_init_with_order(_arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(e)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
	runtime.KeepAlive(z)
	runtime.KeepAlive(order)

	var _euler *Euler // out

	_euler = (*Euler)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _euler
}

// Reorder reorders a #graphene_euler_t using order.
//
// This function is equivalent to creating a #graphene_quaternion_t from the
// given #graphene_euler_t, and then converting the quaternion into another
// #graphene_euler_t.
//
// The function takes the following parameters:
//
//   - order: new order.
//
// The function returns the following values:
//
//   - res: return location for the reordered #graphene_euler_t.
func (e *Euler) Reorder(order EulerOrder) *Euler {
	var _arg0 *C.graphene_euler_t      // out
	var _arg1 C.graphene_euler_order_t // out
	var _arg2 C.graphene_euler_t       // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))
	_arg1 = C.graphene_euler_order_t(order)

	C.graphene_euler_reorder(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(e)
	runtime.KeepAlive(order)

	var _res *Euler // out

	_res = (*Euler)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// ToMatrix converts a #graphene_euler_t into a transformation matrix expressing
// the extrinsic composition of rotations described by the Euler angles.
//
// The rotations are applied over the reference frame axes in the order
// associated with the #graphene_euler_t; for instance, if the order used to
// initialize e is GRAPHENE_EULER_ORDER_XYZ:
//
//   - the first rotation moves the body around the X axis with an angle φ
//   - the second rotation moves the body around the Y axis with an angle of ϑ
//   - the third rotation moves the body around the Z axis with an angle of ψ
//
// The rotation sign convention is right-handed, to preserve compatibility
// between Euler-based, quaternion-based, and angle-axis-based rotations.
//
// The function returns the following values:
//
//   - res: return location for a #graphene_matrix_t.
func (e *Euler) ToMatrix() *Matrix {
	var _arg0 *C.graphene_euler_t // out
	var _arg1 C.graphene_matrix_t // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))

	C.graphene_euler_to_matrix(_arg0, &_arg1)
	runtime.KeepAlive(e)

	var _res *Matrix // out

	_res = (*Matrix)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// ToQuaternion converts a #graphene_euler_t into a #graphene_quaternion_t.
//
// The function returns the following values:
//
//   - res: return location for a #graphene_quaternion_t.
func (e *Euler) ToQuaternion() *Quaternion {
	var _arg0 *C.graphene_euler_t     // out
	var _arg1 C.graphene_quaternion_t // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))

	C.graphene_euler_to_quaternion(_arg0, &_arg1)
	runtime.KeepAlive(e)

	var _res *Quaternion // out

	_res = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// ToVec3 retrieves the angles of a #graphene_euler_t and initializes a
// #graphene_vec3_t with them.
//
// The function returns the following values:
//
//   - res: return location for a #graphene_vec3_t.
func (e *Euler) ToVec3() *Vec3 {
	var _arg0 *C.graphene_euler_t // out
	var _arg1 C.graphene_vec3_t   // in

	_arg0 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))

	C.graphene_euler_to_vec3(_arg0, &_arg1)
	runtime.KeepAlive(e)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Frustum: 3D volume delimited by 2D clip planes.
//
// The contents of the graphene_frustum_t are private, and should not be
// modified directly.
//
// An instance of this type is always passed by reference.
type Frustum struct {
	*frustum
}

// frustum is the struct that's finalized.
type frustum struct {
	native *C.graphene_frustum_t
}

func marshalFrustum(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Frustum{&frustum{(*C.graphene_frustum_t)(b)}}, nil
}

// NewFrustumAlloc constructs a struct Frustum.
func NewFrustumAlloc() *Frustum {
	var _cret *C.graphene_frustum_t // in

	_cret = C.graphene_frustum_alloc()

	var _frustum *Frustum // out

	_frustum = (*Frustum)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_frustum)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_frustum_free((*C.graphene_frustum_t)(intern.C))
		},
	)

	return _frustum
}

// ContainsPoint checks whether a point is inside the volume defined by the
// given #graphene_frustum_t.
//
// The function takes the following parameters:
//
//   - point: #graphene_point3d_t.
//
// The function returns the following values:
//
//   - ok: true if the point is inside the frustum.
func (f *Frustum) ContainsPoint(point *Point3D) bool {
	var _arg0 *C.graphene_frustum_t // out
	var _arg1 *C.graphene_point3d_t // out
	var _cret C._Bool               // in

	_arg0 = (*C.graphene_frustum_t)(gextras.StructNative(unsafe.Pointer(f)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(point)))

	_cret = C.graphene_frustum_contains_point(_arg0, _arg1)
	runtime.KeepAlive(f)
	runtime.KeepAlive(point)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Equal checks whether the two given #graphene_frustum_t are equal.
//
// The function takes the following parameters:
//
//   - b: #graphene_frustum_t.
//
// The function returns the following values:
//
//   - ok: true if the given frustums are equal.
func (a *Frustum) Equal(b *Frustum) bool {
	var _arg0 *C.graphene_frustum_t // out
	var _arg1 *C.graphene_frustum_t // out
	var _cret C._Bool               // in

	_arg0 = (*C.graphene_frustum_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_frustum_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_frustum_equal(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Planes retrieves the planes that define the given #graphene_frustum_t.
//
// The function returns the following values:
//
//   - planes: return location for an array of 6 #graphene_plane_t.
func (f *Frustum) Planes() [6]Plane {
	var _arg0 *C.graphene_frustum_t // out
	var _arg1 [6]C.graphene_plane_t // in

	_arg0 = (*C.graphene_frustum_t)(gextras.StructNative(unsafe.Pointer(f)))

	C.graphene_frustum_get_planes(_arg0, &_arg1[0])
	runtime.KeepAlive(f)

	var _planes [6]Plane // out

	{
		src := &_arg1
		for i := 0; i < 6; i++ {
			_planes[i] = *(*Plane)(gextras.NewStructNative(unsafe.Pointer((&src[i]))))
		}
	}

	return _planes
}

// Init initializes the given #graphene_frustum_t using the provided clipping
// planes.
//
// The function takes the following parameters:
//
//   - p0: clipping plane.
//   - p1: clipping plane.
//   - p2: clipping plane.
//   - p3: clipping plane.
//   - p4: clipping plane.
//   - p5: clipping plane.
//
// The function returns the following values:
//
//   - frustum: initialized frustum.
func (f *Frustum) Init(p0 *Plane, p1 *Plane, p2 *Plane, p3 *Plane, p4 *Plane, p5 *Plane) *Frustum {
	var _arg0 *C.graphene_frustum_t // out
	var _arg1 *C.graphene_plane_t   // out
	var _arg2 *C.graphene_plane_t   // out
	var _arg3 *C.graphene_plane_t   // out
	var _arg4 *C.graphene_plane_t   // out
	var _arg5 *C.graphene_plane_t   // out
	var _arg6 *C.graphene_plane_t   // out
	var _cret *C.graphene_frustum_t // in

	_arg0 = (*C.graphene_frustum_t)(gextras.StructNative(unsafe.Pointer(f)))
	_arg1 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p0)))
	_arg2 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p1)))
	_arg3 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p2)))
	_arg4 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p3)))
	_arg5 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p4)))
	_arg6 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p5)))

	_cret = C.graphene_frustum_init(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, _arg6)
	runtime.KeepAlive(f)
	runtime.KeepAlive(p0)
	runtime.KeepAlive(p1)
	runtime.KeepAlive(p2)
	runtime.KeepAlive(p3)
	runtime.KeepAlive(p4)
	runtime.KeepAlive(p5)

	var _frustum *Frustum // out

	_frustum = (*Frustum)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _frustum
}

// InitFromFrustum initializes the given #graphene_frustum_t using the clipping
// planes of another #graphene_frustum_t.
//
// The function takes the following parameters:
//
//   - src: #graphene_frustum_t.
//
// The function returns the following values:
//
//   - frustum: initialized frustum.
func (f *Frustum) InitFromFrustum(src *Frustum) *Frustum {
	var _arg0 *C.graphene_frustum_t // out
	var _arg1 *C.graphene_frustum_t // out
	var _cret *C.graphene_frustum_t // in

	_arg0 = (*C.graphene_frustum_t)(gextras.StructNative(unsafe.Pointer(f)))
	_arg1 = (*C.graphene_frustum_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_frustum_init_from_frustum(_arg0, _arg1)
	runtime.KeepAlive(f)
	runtime.KeepAlive(src)

	var _frustum *Frustum // out

	_frustum = (*Frustum)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _frustum
}

// InitFromMatrix initializes a #graphene_frustum_t using the given matrix.
//
// The function takes the following parameters:
//
//   - matrix: #graphene_matrix_t.
//
// The function returns the following values:
//
//   - frustum: initialized frustum.
func (f *Frustum) InitFromMatrix(matrix *Matrix) *Frustum {
	var _arg0 *C.graphene_frustum_t // out
	var _arg1 *C.graphene_matrix_t  // out
	var _cret *C.graphene_frustum_t // in

	_arg0 = (*C.graphene_frustum_t)(gextras.StructNative(unsafe.Pointer(f)))
	_arg1 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(matrix)))

	_cret = C.graphene_frustum_init_from_matrix(_arg0, _arg1)
	runtime.KeepAlive(f)
	runtime.KeepAlive(matrix)

	var _frustum *Frustum // out

	_frustum = (*Frustum)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _frustum
}

// IntersectsBox checks whether the given box intersects a plane of a
// #graphene_frustum_t.
//
// The function takes the following parameters:
//
//   - box: #graphene_box_t.
//
// The function returns the following values:
//
//   - ok: true if the box intersects the frustum.
func (f *Frustum) IntersectsBox(box *Box) bool {
	var _arg0 *C.graphene_frustum_t // out
	var _arg1 *C.graphene_box_t     // out
	var _cret C._Bool               // in

	_arg0 = (*C.graphene_frustum_t)(gextras.StructNative(unsafe.Pointer(f)))
	_arg1 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(box)))

	_cret = C.graphene_frustum_intersects_box(_arg0, _arg1)
	runtime.KeepAlive(f)
	runtime.KeepAlive(box)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// IntersectsSphere checks whether the given sphere intersects a plane of a
// #graphene_frustum_t.
//
// The function takes the following parameters:
//
//   - sphere: #graphene_sphere_t.
//
// The function returns the following values:
//
//   - ok: true if the sphere intersects the frustum.
func (f *Frustum) IntersectsSphere(sphere *Sphere) bool {
	var _arg0 *C.graphene_frustum_t // out
	var _arg1 *C.graphene_sphere_t  // out
	var _cret C._Bool               // in

	_arg0 = (*C.graphene_frustum_t)(gextras.StructNative(unsafe.Pointer(f)))
	_arg1 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(sphere)))

	_cret = C.graphene_frustum_intersects_sphere(_arg0, _arg1)
	runtime.KeepAlive(f)
	runtime.KeepAlive(sphere)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Matrix: structure capable of holding a 4x4 matrix.
//
// The contents of the #graphene_matrix_t structure are private and should never
// be accessed directly.
//
// An instance of this type is always passed by reference.
type Matrix struct {
	*matrix
}

// matrix is the struct that's finalized.
type matrix struct {
	native *C.graphene_matrix_t
}

func marshalMatrix(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Matrix{&matrix{(*C.graphene_matrix_t)(b)}}, nil
}

// NewMatrixAlloc constructs a struct Matrix.
func NewMatrixAlloc() *Matrix {
	var _cret *C.graphene_matrix_t // in

	_cret = C.graphene_matrix_alloc()

	var _matrix *Matrix // out

	_matrix = (*Matrix)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_matrix)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_matrix_free((*C.graphene_matrix_t)(intern.C))
		},
	)

	return _matrix
}

// Decompose decomposes a transformation matrix into its component
// transformations.
//
// The algorithm for decomposing a matrix is taken from the CSS3 Transforms
// specification (http://dev.w3.org/csswg/css-transforms/); specifically,
// the decomposition code is based on the equivalent code published
// in "Graphics Gems II", edited by Jim Arvo, and available online
// (http://web.archive.org/web/20150512160205/http://tog.acm.org/resources/GraphicsGems/gemsii/unmatrix.c).
//
// The function returns the following values:
//
//   - translate: translation vector.
//   - scale vector.
//   - rotate: rotation quaternion.
//   - shear vector.
//   - perspective vector.
//   - ok: true if the matrix could be decomposed.
func (m *Matrix) Decompose() (translate *Vec3, scale *Vec3, rotate *Quaternion, shear *Vec3, perspective *Vec4, ok bool) {
	var _arg0 *C.graphene_matrix_t    // out
	var _arg1 C.graphene_vec3_t       // in
	var _arg2 C.graphene_vec3_t       // in
	var _arg3 C.graphene_quaternion_t // in
	var _arg4 C.graphene_vec3_t       // in
	var _arg5 C.graphene_vec4_t       // in
	var _cret C._Bool                 // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_matrix_decompose(_arg0, &_arg1, &_arg2, &_arg3, &_arg4, &_arg5)
	runtime.KeepAlive(m)

	var _translate *Vec3    // out
	var _scale *Vec3        // out
	var _rotate *Quaternion // out
	var _shear *Vec3        // out
	var _perspective *Vec4  // out
	var _ok bool            // out

	_translate = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))
	_scale = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))
	_rotate = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))
	_shear = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg4))))
	_perspective = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg5))))
	if _cret {
		_ok = true
	}

	return _translate, _scale, _rotate, _shear, _perspective, _ok
}

// Determinant computes the determinant of the given matrix.
//
// The function returns the following values:
//
//   - gfloat: value of the determinant.
func (m *Matrix) Determinant() float32 {
	var _arg0 *C.graphene_matrix_t // out
	var _cret C.float              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_matrix_determinant(_arg0)
	runtime.KeepAlive(m)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Equal checks whether the two given #graphene_matrix_t matrices are equal.
//
// The function takes the following parameters:
//
//   - b: #graphene_matrix_t.
//
// The function returns the following values:
//
//   - ok: true if the two matrices are equal, and false otherwise.
func (a *Matrix) Equal(b *Matrix) bool {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_matrix_t // out
	var _cret C._Bool              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_matrix_equal(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// EqualFast checks whether the two given #graphene_matrix_t matrices are
// byte-by-byte equal.
//
// While this function is faster than graphene_matrix_equal(), it can also
// return false negatives, so it should be used in conjuction with either
// graphene_matrix_equal() or graphene_matrix_near(). For instance:
//
//	if (graphene_matrix_equal_fast (a, b))
//	  {
//	    // matrices are definitely the same
//	  }
//	else
//	  {
//	    if (graphene_matrix_equal (a, b))
//	      // matrices contain the same values within an epsilon of FLT_EPSILON
//	    else if (graphene_matrix_near (a, b, 0.0001))
//	      // matrices contain the same values within an epsilon of 0.0001
//	    else
//	      // matrices are not equal
//	  }.
//
// The function takes the following parameters:
//
//   - b: #graphene_matrix_t.
//
// The function returns the following values:
//
//   - ok: true if the matrices are equal. and false otherwise.
func (a *Matrix) EqualFast(b *Matrix) bool {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_matrix_t // out
	var _cret C._Bool              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_matrix_equal_fast(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Row retrieves the given row vector at index_ inside a matrix.
//
// The function takes the following parameters:
//
//   - index_: index of the row vector, between 0 and 3.
//
// The function returns the following values:
//
//   - res: return location for the #graphene_vec4_t that is used to store the
//     row vector.
func (m *Matrix) Row(index_ uint) *Vec4 {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.uint               // out
	var _arg2 C.graphene_vec4_t    // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.uint(index_)

	C.graphene_matrix_get_row(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(index_)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Value retrieves the value at the given row and col index.
//
// The function takes the following parameters:
//
//   - row index.
//   - col: column index.
//
// The function returns the following values:
//
//   - gfloat: value at the given indices.
func (m *Matrix) Value(row uint, col uint) float32 {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.uint               // out
	var _arg2 C.uint               // out
	var _cret C.float              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.uint(row)
	_arg2 = C.uint(col)

	_cret = C.graphene_matrix_get_value(_arg0, _arg1, _arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(row)
	runtime.KeepAlive(col)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// XScale retrieves the scaling factor on the X axis in m.
//
// The function returns the following values:
//
//   - gfloat: value of the scaling factor.
func (m *Matrix) XScale() float32 {
	var _arg0 *C.graphene_matrix_t // out
	var _cret C.float              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_matrix_get_x_scale(_arg0)
	runtime.KeepAlive(m)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// XTranslation retrieves the translation component on the X axis from m.
//
// The function returns the following values:
//
//   - gfloat: translation component.
func (m *Matrix) XTranslation() float32 {
	var _arg0 *C.graphene_matrix_t // out
	var _cret C.float              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_matrix_get_x_translation(_arg0)
	runtime.KeepAlive(m)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// YScale retrieves the scaling factor on the Y axis in m.
//
// The function returns the following values:
//
//   - gfloat: value of the scaling factor.
func (m *Matrix) YScale() float32 {
	var _arg0 *C.graphene_matrix_t // out
	var _cret C.float              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_matrix_get_y_scale(_arg0)
	runtime.KeepAlive(m)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// YTranslation retrieves the translation component on the Y axis from m.
//
// The function returns the following values:
//
//   - gfloat: translation component.
func (m *Matrix) YTranslation() float32 {
	var _arg0 *C.graphene_matrix_t // out
	var _cret C.float              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_matrix_get_y_translation(_arg0)
	runtime.KeepAlive(m)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// ZScale retrieves the scaling factor on the Z axis in m.
//
// The function returns the following values:
//
//   - gfloat: value of the scaling factor.
func (m *Matrix) ZScale() float32 {
	var _arg0 *C.graphene_matrix_t // out
	var _cret C.float              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_matrix_get_z_scale(_arg0)
	runtime.KeepAlive(m)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// ZTranslation retrieves the translation component on the Z axis from m.
//
// The function returns the following values:
//
//   - gfloat: translation component.
func (m *Matrix) ZTranslation() float32 {
	var _arg0 *C.graphene_matrix_t // out
	var _cret C.float              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_matrix_get_z_translation(_arg0)
	runtime.KeepAlive(m)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// InitFrom2D initializes a #graphene_matrix_t from the values of an affine
// transformation matrix.
//
// The arguments map to the following matrix layout:
//
//	⎛ xx  yx ⎞   ⎛  a   b  0 ⎞
//	⎜ xy  yy ⎟ = ⎜  c   d  0 ⎟
//	⎝ x0  y0 ⎠   ⎝ tx  ty  1 ⎠
//
// This function can be used to convert between an affine matrix type from other
// libraries and a #graphene_matrix_t.
//
// The function takes the following parameters:
//
//   - xx member.
//   - yx member.
//   - xy member.
//   - yy member.
//   - x0 member.
//   - y0 member.
//
// The function returns the following values:
//
//   - matrix: initialized matrix.
func (m *Matrix) InitFrom2D(xx float64, yx float64, xy float64, yy float64, x0 float64, y0 float64) *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.double             // out
	var _arg2 C.double             // out
	var _arg3 C.double             // out
	var _arg4 C.double             // out
	var _arg5 C.double             // out
	var _arg6 C.double             // out
	var _cret *C.graphene_matrix_t // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.double(xx)
	_arg2 = C.double(yx)
	_arg3 = C.double(xy)
	_arg4 = C.double(yy)
	_arg5 = C.double(x0)
	_arg6 = C.double(y0)

	_cret = C.graphene_matrix_init_from_2d(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, _arg6)
	runtime.KeepAlive(m)
	runtime.KeepAlive(xx)
	runtime.KeepAlive(yx)
	runtime.KeepAlive(xy)
	runtime.KeepAlive(yy)
	runtime.KeepAlive(x0)
	runtime.KeepAlive(y0)

	var _matrix *Matrix // out

	_matrix = (*Matrix)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _matrix
}

// InitFromFloat initializes a #graphene_matrix_t with the given array of
// floating point values.
//
// The function takes the following parameters:
//
//   - v: array of at least 16 floating point values.
//
// The function returns the following values:
//
//   - matrix: initialized matrix.
func (m *Matrix) InitFromFloat(v [16]float32) *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.float             // out
	var _cret *C.graphene_matrix_t // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.float)(unsafe.Pointer(&v))

	_cret = C.graphene_matrix_init_from_float(_arg0, _arg1)
	runtime.KeepAlive(m)
	runtime.KeepAlive(v)

	var _matrix *Matrix // out

	_matrix = (*Matrix)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _matrix
}

// InitFromMatrix initializes a #graphene_matrix_t using the values of the given
// matrix.
//
// The function takes the following parameters:
//
//   - src: #graphene_matrix_t.
//
// The function returns the following values:
//
//   - matrix: initialized matrix.
func (m *Matrix) InitFromMatrix(src *Matrix) *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_matrix_t // out
	var _cret *C.graphene_matrix_t // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_matrix_init_from_matrix(_arg0, _arg1)
	runtime.KeepAlive(m)
	runtime.KeepAlive(src)

	var _matrix *Matrix // out

	_matrix = (*Matrix)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _matrix
}

// InitFromVec4 initializes a #graphene_matrix_t with the given four row
// vectors.
//
// The function takes the following parameters:
//
//   - v0: first row vector.
//   - v1: second row vector.
//   - v2: third row vector.
//   - v3: fourth row vector.
//
// The function returns the following values:
//
//   - matrix: initialized matrix.
func (m *Matrix) InitFromVec4(v0 *Vec4, v1 *Vec4, v2 *Vec4, v3 *Vec4) *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_vec4_t   // out
	var _arg2 *C.graphene_vec4_t   // out
	var _arg3 *C.graphene_vec4_t   // out
	var _arg4 *C.graphene_vec4_t   // out
	var _cret *C.graphene_matrix_t // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v0)))
	_arg2 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v1)))
	_arg3 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v2)))
	_arg4 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v3)))

	_cret = C.graphene_matrix_init_from_vec4(_arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(m)
	runtime.KeepAlive(v0)
	runtime.KeepAlive(v1)
	runtime.KeepAlive(v2)
	runtime.KeepAlive(v3)

	var _matrix *Matrix // out

	_matrix = (*Matrix)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _matrix
}

// InitFrustum initializes a #graphene_matrix_t compatible with
// #graphene_frustum_t.
//
// See also: graphene_frustum_init_from_matrix().
//
// The function takes the following parameters:
//
//   - left: distance of the left clipping plane.
//   - right: distance of the right clipping plane.
//   - bottom: distance of the bottom clipping plane.
//   - top: distance of the top clipping plane.
//   - zNear: distance of the near clipping plane.
//   - zFar: distance of the far clipping plane.
//
// The function returns the following values:
//
//   - matrix: initialized matrix.
func (m *Matrix) InitFrustum(left float32, right float32, bottom float32, top float32, zNear float32, zFar float32) *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.float              // out
	var _arg2 C.float              // out
	var _arg3 C.float              // out
	var _arg4 C.float              // out
	var _arg5 C.float              // out
	var _arg6 C.float              // out
	var _cret *C.graphene_matrix_t // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.float(left)
	_arg2 = C.float(right)
	_arg3 = C.float(bottom)
	_arg4 = C.float(top)
	_arg5 = C.float(zNear)
	_arg6 = C.float(zFar)

	_cret = C.graphene_matrix_init_frustum(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, _arg6)
	runtime.KeepAlive(m)
	runtime.KeepAlive(left)
	runtime.KeepAlive(right)
	runtime.KeepAlive(bottom)
	runtime.KeepAlive(top)
	runtime.KeepAlive(zNear)
	runtime.KeepAlive(zFar)

	var _matrix *Matrix // out

	_matrix = (*Matrix)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _matrix
}

// InitIdentity initializes a #graphene_matrix_t with the identity matrix.
//
// The function returns the following values:
//
//   - matrix: initialized matrix.
func (m *Matrix) InitIdentity() *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _cret *C.graphene_matrix_t // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_matrix_init_identity(_arg0)
	runtime.KeepAlive(m)

	var _matrix *Matrix // out

	_matrix = (*Matrix)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _matrix
}

// InitLookAt initializes a #graphene_matrix_t so that it positions the "camera"
// at the given eye coordinates towards an object at the center coordinates.
// The top of the camera is aligned to the direction of the up vector.
//
// Before the transform, the camera is assumed to be placed at the origin,
// looking towards the negative Z axis, with the top side of the camera facing
// in the direction of the Y axis and the right side in the direction of the X
// axis.
//
// In theory, one could use m to transform a model of such a camera into
// world-space. However, it is more common to use the inverse of m to transform
// another object from world coordinates to the view coordinates of the camera.
// Typically you would then apply the camera projection transform to get from
// view to screen coordinates.
//
// The function takes the following parameters:
//
//   - eye: vector describing the position to look from.
//   - center: vector describing the position to look at.
//   - up: vector describing the world's upward direction; usually, this is the
//     graphene_vec3_y_axis() vector.
//
// The function returns the following values:
//
//   - matrix: initialized matrix.
func (m *Matrix) InitLookAt(eye *Vec3, center *Vec3, up *Vec3) *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_vec3_t   // out
	var _arg2 *C.graphene_vec3_t   // out
	var _arg3 *C.graphene_vec3_t   // out
	var _cret *C.graphene_matrix_t // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(eye)))
	_arg2 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(center)))
	_arg3 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(up)))

	_cret = C.graphene_matrix_init_look_at(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(m)
	runtime.KeepAlive(eye)
	runtime.KeepAlive(center)
	runtime.KeepAlive(up)

	var _matrix *Matrix // out

	_matrix = (*Matrix)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _matrix
}

// InitOrtho initializes a #graphene_matrix_t with an orthographic projection.
//
// The function takes the following parameters:
//
//   - left edge of the clipping plane.
//   - right edge of the clipping plane.
//   - top edge of the clipping plane.
//   - bottom edge of the clipping plane.
//   - zNear: distance of the near clipping plane.
//   - zFar: distance of the far clipping plane.
//
// The function returns the following values:
//
//   - matrix: initialized matrix.
func (m *Matrix) InitOrtho(left float32, right float32, top float32, bottom float32, zNear float32, zFar float32) *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.float              // out
	var _arg2 C.float              // out
	var _arg3 C.float              // out
	var _arg4 C.float              // out
	var _arg5 C.float              // out
	var _arg6 C.float              // out
	var _cret *C.graphene_matrix_t // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.float(left)
	_arg2 = C.float(right)
	_arg3 = C.float(top)
	_arg4 = C.float(bottom)
	_arg5 = C.float(zNear)
	_arg6 = C.float(zFar)

	_cret = C.graphene_matrix_init_ortho(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5, _arg6)
	runtime.KeepAlive(m)
	runtime.KeepAlive(left)
	runtime.KeepAlive(right)
	runtime.KeepAlive(top)
	runtime.KeepAlive(bottom)
	runtime.KeepAlive(zNear)
	runtime.KeepAlive(zFar)

	var _matrix *Matrix // out

	_matrix = (*Matrix)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _matrix
}

// InitPerspective initializes a #graphene_matrix_t with a perspective
// projection.
//
// The function takes the following parameters:
//
//   - fovy: field of view angle, in degrees.
//   - aspect value.
//   - zNear: near Z plane.
//   - zFar: far Z plane.
//
// The function returns the following values:
//
//   - matrix: initialized matrix.
func (m *Matrix) InitPerspective(fovy float32, aspect float32, zNear float32, zFar float32) *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.float              // out
	var _arg2 C.float              // out
	var _arg3 C.float              // out
	var _arg4 C.float              // out
	var _cret *C.graphene_matrix_t // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.float(fovy)
	_arg2 = C.float(aspect)
	_arg3 = C.float(zNear)
	_arg4 = C.float(zFar)

	_cret = C.graphene_matrix_init_perspective(_arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(m)
	runtime.KeepAlive(fovy)
	runtime.KeepAlive(aspect)
	runtime.KeepAlive(zNear)
	runtime.KeepAlive(zFar)

	var _matrix *Matrix // out

	_matrix = (*Matrix)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _matrix
}

// InitRotate initializes m to represent a rotation of angle degrees on the axis
// represented by the axis vector.
//
// The function takes the following parameters:
//
//   - angle: rotation angle, in degrees.
//   - axis vector as a #graphene_vec3_t.
//
// The function returns the following values:
//
//   - matrix: initialized matrix.
func (m *Matrix) InitRotate(angle float32, axis *Vec3) *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.float              // out
	var _arg2 *C.graphene_vec3_t   // out
	var _cret *C.graphene_matrix_t // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.float(angle)
	_arg2 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(axis)))

	_cret = C.graphene_matrix_init_rotate(_arg0, _arg1, _arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(angle)
	runtime.KeepAlive(axis)

	var _matrix *Matrix // out

	_matrix = (*Matrix)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _matrix
}

// InitScale initializes a #graphene_matrix_t with the given scaling factors.
//
// The function takes the following parameters:
//
//   - x: scale factor on the X axis.
//   - y: scale factor on the Y axis.
//   - z: scale factor on the Z axis.
//
// The function returns the following values:
//
//   - matrix: initialized matrix.
func (m *Matrix) InitScale(x float32, y float32, z float32) *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.float              // out
	var _arg2 C.float              // out
	var _arg3 C.float              // out
	var _cret *C.graphene_matrix_t // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.float(x)
	_arg2 = C.float(y)
	_arg3 = C.float(z)

	_cret = C.graphene_matrix_init_scale(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(m)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
	runtime.KeepAlive(z)

	var _matrix *Matrix // out

	_matrix = (*Matrix)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _matrix
}

// InitSkew initializes a #graphene_matrix_t with a skew transformation with the
// given factors.
//
// The function takes the following parameters:
//
//   - xSkew: skew factor, in radians, on the X axis.
//   - ySkew: skew factor, in radians, on the Y axis.
//
// The function returns the following values:
//
//   - matrix: initialized matrix.
func (m *Matrix) InitSkew(xSkew float32, ySkew float32) *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.float              // out
	var _arg2 C.float              // out
	var _cret *C.graphene_matrix_t // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.float(xSkew)
	_arg2 = C.float(ySkew)

	_cret = C.graphene_matrix_init_skew(_arg0, _arg1, _arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(xSkew)
	runtime.KeepAlive(ySkew)

	var _matrix *Matrix // out

	_matrix = (*Matrix)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _matrix
}

// InitTranslate initializes a #graphene_matrix_t with a translation to the
// given coordinates.
//
// The function takes the following parameters:
//
//   - p: translation coordinates.
//
// The function returns the following values:
//
//   - matrix: initialized matrix.
func (m *Matrix) InitTranslate(p *Point3D) *Matrix {
	var _arg0 *C.graphene_matrix_t  // out
	var _arg1 *C.graphene_point3d_t // out
	var _cret *C.graphene_matrix_t  // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(p)))

	_cret = C.graphene_matrix_init_translate(_arg0, _arg1)
	runtime.KeepAlive(m)
	runtime.KeepAlive(p)

	var _matrix *Matrix // out

	_matrix = (*Matrix)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _matrix
}

// Interpolate: linearly interpolates the two given #graphene_matrix_t by
// interpolating the decomposed transformations separately.
//
// If either matrix cannot be reduced to their transformations then the
// interpolation cannot be performed, and this function will return an identity
// matrix.
//
// The function takes the following parameters:
//
//   - b: #graphene_matrix_t.
//   - factor: linear interpolation factor.
//
// The function returns the following values:
//
//   - res: return location for the interpolated matrix.
func (a *Matrix) Interpolate(b *Matrix, factor float64) *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_matrix_t // out
	var _arg2 C.double             // out
	var _arg3 C.graphene_matrix_t  // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(b)))
	_arg2 = C.double(factor)

	C.graphene_matrix_interpolate(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)
	runtime.KeepAlive(factor)

	var _res *Matrix // out

	_res = (*Matrix)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _res
}

// Inverse inverts the given matrix.
//
// The function returns the following values:
//
//   - res: return location for the inverse matrix.
//   - ok: true if the matrix is invertible.
func (m *Matrix) Inverse() (*Matrix, bool) {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.graphene_matrix_t  // in
	var _cret C._Bool              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_matrix_inverse(_arg0, &_arg1)
	runtime.KeepAlive(m)

	var _res *Matrix // out
	var _ok bool     // out

	_res = (*Matrix)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))
	if _cret {
		_ok = true
	}

	return _res, _ok
}

// Is2D checks whether the given #graphene_matrix_t is compatible with an a 2D
// affine transformation matrix.
//
// The function returns the following values:
//
//   - ok: true if the matrix is compatible with an affine transformation
//     matrix.
func (m *Matrix) Is2D() bool {
	var _arg0 *C.graphene_matrix_t // out
	var _cret C._Bool              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_matrix_is_2d(_arg0)
	runtime.KeepAlive(m)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// IsBackfaceVisible checks whether a #graphene_matrix_t has a visible back
// face.
//
// The function returns the following values:
//
//   - ok: true if the back face of the matrix is visible.
func (m *Matrix) IsBackfaceVisible() bool {
	var _arg0 *C.graphene_matrix_t // out
	var _cret C._Bool              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_matrix_is_backface_visible(_arg0)
	runtime.KeepAlive(m)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// IsIdentity checks whether the given #graphene_matrix_t is the identity
// matrix.
//
// The function returns the following values:
//
//   - ok: true if the matrix is the identity matrix.
func (m *Matrix) IsIdentity() bool {
	var _arg0 *C.graphene_matrix_t // out
	var _cret C._Bool              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_matrix_is_identity(_arg0)
	runtime.KeepAlive(m)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// IsSingular checks whether a matrix is singular.
//
// The function returns the following values:
//
//   - ok: true if the matrix is singular.
func (m *Matrix) IsSingular() bool {
	var _arg0 *C.graphene_matrix_t // out
	var _cret C._Bool              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_matrix_is_singular(_arg0)
	runtime.KeepAlive(m)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Multiply multiplies two #graphene_matrix_t.
//
// Matrix multiplication is not commutative in general; the order of the factors
// matters. The product of this multiplication is (a × b).
//
// The function takes the following parameters:
//
//   - b: #graphene_matrix_t.
//
// The function returns the following values:
//
//   - res: return location for the matrix result.
func (a *Matrix) Multiply(b *Matrix) *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_matrix_t // out
	var _arg2 C.graphene_matrix_t  // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_matrix_multiply(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Matrix // out

	_res = (*Matrix)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Near compares the two given #graphene_matrix_t matrices and checks whether
// their values are within the given epsilon of each other.
//
// The function takes the following parameters:
//
//   - b: #graphene_matrix_t.
//   - epsilon: threshold between the two matrices.
//
// The function returns the following values:
//
//   - ok: true if the two matrices are near each other, and false otherwise.
func (a *Matrix) Near(b *Matrix, epsilon float32) bool {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_matrix_t // out
	var _arg2 C.float              // out
	var _cret C._Bool              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(b)))
	_arg2 = C.float(epsilon)

	_cret = C.graphene_matrix_near(_arg0, _arg1, _arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)
	runtime.KeepAlive(epsilon)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Normalize normalizes the given #graphene_matrix_t.
//
// The function returns the following values:
//
//   - res: return location for the normalized matrix.
func (m *Matrix) Normalize() *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.graphene_matrix_t  // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	C.graphene_matrix_normalize(_arg0, &_arg1)
	runtime.KeepAlive(m)

	var _res *Matrix // out

	_res = (*Matrix)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Perspective applies a perspective of depth to the matrix.
//
// The function takes the following parameters:
//
//   - depth of the perspective.
//
// The function returns the following values:
//
//   - res: return location for the perspective matrix.
func (m *Matrix) Perspective(depth float32) *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.float              // out
	var _arg2 C.graphene_matrix_t  // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.float(depth)

	C.graphene_matrix_perspective(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(depth)

	var _res *Matrix // out

	_res = (*Matrix)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Print prints the contents of a matrix to the standard error stream.
//
// This function is only useful for debugging; there are no guarantees made on
// the format of the output.
func (m *Matrix) Print() {
	var _arg0 *C.graphene_matrix_t // out

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	C.graphene_matrix_print(_arg0)
	runtime.KeepAlive(m)
}

// ProjectPoint projects a #graphene_point_t using the matrix m.
//
// The function takes the following parameters:
//
//   - p: #graphene_point_t.
//
// The function returns the following values:
//
//   - res: return location for the projected point.
func (m *Matrix) ProjectPoint(p *Point) *Point {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_point_t  // out
	var _arg2 C.graphene_point_t   // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(p)))

	C.graphene_matrix_project_point(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(p)

	var _res *Point // out

	_res = (*Point)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// ProjectRect projects all corners of a #graphene_rect_t using the given
// matrix.
//
// See also: graphene_matrix_project_point().
//
// The function takes the following parameters:
//
//   - r: #graphene_rect_t.
//
// The function returns the following values:
//
//   - res: return location for the projected rectangle.
func (m *Matrix) ProjectRect(r *Rect) *Quad {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_rect_t   // out
	var _arg2 C.graphene_quad_t    // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_matrix_project_rect(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(r)

	var _res *Quad // out

	_res = (*Quad)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// ProjectRectBounds projects a #graphene_rect_t using the given matrix.
//
// The resulting rectangle is the axis aligned bounding rectangle capable of
// fully containing the projected rectangle.
//
// The function takes the following parameters:
//
//   - r: #graphene_rect_t.
//
// The function returns the following values:
//
//   - res: return location for the projected rectangle.
func (m *Matrix) ProjectRectBounds(r *Rect) *Rect {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_rect_t   // out
	var _arg2 C.graphene_rect_t    // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_matrix_project_rect_bounds(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(r)

	var _res *Rect // out

	_res = (*Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Rotate adds a rotation transformation to m, using the given angle and axis
// vector.
//
// This is the equivalent of calling graphene_matrix_init_rotate() and then
// multiplying the matrix m with the rotation matrix.
//
// The function takes the following parameters:
//
//   - angle: rotation angle, in degrees.
//   - axis: rotation axis, as a #graphene_vec3_t.
func (m *Matrix) Rotate(angle float32, axis *Vec3) {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.float              // out
	var _arg2 *C.graphene_vec3_t   // out

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.float(angle)
	_arg2 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(axis)))

	C.graphene_matrix_rotate(_arg0, _arg1, _arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(angle)
	runtime.KeepAlive(axis)
}

// RotateEuler adds a rotation transformation to m, using the given
// #graphene_euler_t.
//
// The function takes the following parameters:
//
//   - e: rotation described by a #graphene_euler_t.
func (m *Matrix) RotateEuler(e *Euler) {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_euler_t  // out

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))

	C.graphene_matrix_rotate_euler(_arg0, _arg1)
	runtime.KeepAlive(m)
	runtime.KeepAlive(e)
}

// RotateQuaternion adds a rotation transformation to m, using the given
// #graphene_quaternion_t.
//
// This is the equivalent of calling graphene_quaternion_to_matrix() and then
// multiplying m with the rotation matrix.
//
// The function takes the following parameters:
//
//   - q: rotation described by a #graphene_quaternion_t.
func (m *Matrix) RotateQuaternion(q *Quaternion) {
	var _arg0 *C.graphene_matrix_t     // out
	var _arg1 *C.graphene_quaternion_t // out

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))

	C.graphene_matrix_rotate_quaternion(_arg0, _arg1)
	runtime.KeepAlive(m)
	runtime.KeepAlive(q)
}

// RotateX adds a rotation transformation around the X axis to m, using the
// given angle.
//
// See also: graphene_matrix_rotate().
//
// The function takes the following parameters:
//
//   - angle: rotation angle, in degrees.
func (m *Matrix) RotateX(angle float32) {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.float              // out

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.float(angle)

	C.graphene_matrix_rotate_x(_arg0, _arg1)
	runtime.KeepAlive(m)
	runtime.KeepAlive(angle)
}

// RotateY adds a rotation transformation around the Y axis to m, using the
// given angle.
//
// See also: graphene_matrix_rotate().
//
// The function takes the following parameters:
//
//   - angle: rotation angle, in degrees.
func (m *Matrix) RotateY(angle float32) {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.float              // out

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.float(angle)

	C.graphene_matrix_rotate_y(_arg0, _arg1)
	runtime.KeepAlive(m)
	runtime.KeepAlive(angle)
}

// RotateZ adds a rotation transformation around the Z axis to m, using the
// given angle.
//
// See also: graphene_matrix_rotate().
//
// The function takes the following parameters:
//
//   - angle: rotation angle, in degrees.
func (m *Matrix) RotateZ(angle float32) {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.float              // out

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.float(angle)

	C.graphene_matrix_rotate_z(_arg0, _arg1)
	runtime.KeepAlive(m)
	runtime.KeepAlive(angle)
}

// Scale adds a scaling transformation to m, using the three given factors.
//
// This is the equivalent of calling graphene_matrix_init_scale() and then
// multiplying the matrix m with the scale matrix.
//
// The function takes the following parameters:
//
//   - factorX: scaling factor on the X axis.
//   - factorY: scaling factor on the Y axis.
//   - factorZ: scaling factor on the Z axis.
func (m *Matrix) Scale(factorX float32, factorY float32, factorZ float32) {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.float              // out
	var _arg2 C.float              // out
	var _arg3 C.float              // out

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.float(factorX)
	_arg2 = C.float(factorY)
	_arg3 = C.float(factorZ)

	C.graphene_matrix_scale(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(m)
	runtime.KeepAlive(factorX)
	runtime.KeepAlive(factorY)
	runtime.KeepAlive(factorZ)
}

// SkewXY adds a skew of factor on the X and Y axis to the given matrix.
//
// The function takes the following parameters:
//
//   - factor: skew factor.
func (m *Matrix) SkewXY(factor float32) {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.float              // out

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.float(factor)

	C.graphene_matrix_skew_xy(_arg0, _arg1)
	runtime.KeepAlive(m)
	runtime.KeepAlive(factor)
}

// SkewXZ adds a skew of factor on the X and Z axis to the given matrix.
//
// The function takes the following parameters:
//
//   - factor: skew factor.
func (m *Matrix) SkewXZ(factor float32) {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.float              // out

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.float(factor)

	C.graphene_matrix_skew_xz(_arg0, _arg1)
	runtime.KeepAlive(m)
	runtime.KeepAlive(factor)
}

// SkewYZ adds a skew of factor on the Y and Z axis to the given matrix.
//
// The function takes the following parameters:
//
//   - factor: skew factor.
func (m *Matrix) SkewYZ(factor float32) {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.float              // out

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = C.float(factor)

	C.graphene_matrix_skew_yz(_arg0, _arg1)
	runtime.KeepAlive(m)
	runtime.KeepAlive(factor)
}

// To2D converts a #graphene_matrix_t to an affine transformation matrix,
// if the given matrix is compatible.
//
// The returned values have the following layout:
//
//	⎛ xx  yx ⎞   ⎛  a   b  0 ⎞
//	⎜ xy  yy ⎟ = ⎜  c   d  0 ⎟
//	⎝ x0  y0 ⎠   ⎝ tx  ty  1 ⎠
//
// This function can be used to convert between a #graphene_matrix_t and an
// affine matrix type from other libraries.
//
// The function returns the following values:
//
//   - xx: return location for the xx member.
//   - yx: return location for the yx member.
//   - xy: return location for the xy member.
//   - yy: return location for the yy member.
//   - x0: return location for the x0 member.
//   - y0: return location for the y0 member.
//   - ok: true if the matrix is compatible with an affine transformation
//     matrix.
func (m *Matrix) To2D() (xx float64, yx float64, xy float64, yy float64, x0 float64, y0 float64, ok bool) {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.double             // in
	var _arg2 C.double             // in
	var _arg3 C.double             // in
	var _arg4 C.double             // in
	var _arg5 C.double             // in
	var _arg6 C.double             // in
	var _cret C._Bool              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_matrix_to_2d(_arg0, &_arg1, &_arg2, &_arg3, &_arg4, &_arg5, &_arg6)
	runtime.KeepAlive(m)

	var _xx float64 // out
	var _yx float64 // out
	var _xy float64 // out
	var _yy float64 // out
	var _x0 float64 // out
	var _y0 float64 // out
	var _ok bool    // out

	_xx = float64(_arg1)
	_yx = float64(_arg2)
	_xy = float64(_arg3)
	_yy = float64(_arg4)
	_x0 = float64(_arg5)
	_y0 = float64(_arg6)
	if _cret {
		_ok = true
	}

	return _xx, _yx, _xy, _yy, _x0, _y0, _ok
}

// ToFloat converts a #graphene_matrix_t to an array of floating point values.
//
// The function returns the following values:
//
//   - v: return location for an array of floating point values. The array must
//     be capable of holding at least 16 values.
func (m *Matrix) ToFloat() [16]float32 {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 [16]C.float          // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	C.graphene_matrix_to_float(_arg0, &_arg1[0])
	runtime.KeepAlive(m)

	var _v [16]float32 // out

	_v = *(*[16]float32)(unsafe.Pointer(&_arg1))

	return _v
}

// TransformBounds transforms each corner of a #graphene_rect_t using the given
// matrix m.
//
// The result is the axis aligned bounding rectangle containing the coplanar
// quadrilateral.
//
// See also: graphene_matrix_transform_point().
//
// The function takes the following parameters:
//
//   - r: #graphene_rect_t.
//
// The function returns the following values:
//
//   - res: return location for the bounds of the transformed rectangle.
func (m *Matrix) TransformBounds(r *Rect) *Rect {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_rect_t   // out
	var _arg2 C.graphene_rect_t    // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_matrix_transform_bounds(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(r)

	var _res *Rect // out

	_res = (*Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// TransformBox transforms the vertices of a #graphene_box_t using the given
// matrix m.
//
// The result is the axis aligned bounding box containing the transformed
// vertices.
//
// The function takes the following parameters:
//
//   - b: #graphene_box_t.
//
// The function returns the following values:
//
//   - res: return location for the bounds of the transformed box.
func (m *Matrix) TransformBox(b *Box) *Box {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_box_t    // out
	var _arg2 C.graphene_box_t     // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_matrix_transform_box(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(b)

	var _res *Box // out

	_res = (*Box)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// TransformPoint transforms the given #graphene_point_t using the matrix m.
//
// Unlike graphene_matrix_transform_vec3(), this function will take into account
// the fourth row vector of the #graphene_matrix_t when computing the dot
// product of each row vector of the matrix.
//
// See also: graphene_simd4x4f_point3_mul().
//
// The function takes the following parameters:
//
//   - p: #graphene_point_t.
//
// The function returns the following values:
//
//   - res: return location for the transformed #graphene_point_t.
func (m *Matrix) TransformPoint(p *Point) *Point {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_point_t  // out
	var _arg2 C.graphene_point_t   // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(p)))

	C.graphene_matrix_transform_point(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(p)

	var _res *Point // out

	_res = (*Point)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// TransformPoint3D transforms the given #graphene_point3d_t using the matrix m.
//
// Unlike graphene_matrix_transform_vec3(), this function will take into account
// the fourth row vector of the #graphene_matrix_t when computing the dot
// product of each row vector of the matrix.
//
// See also: graphene_simd4x4f_point3_mul().
//
// The function takes the following parameters:
//
//   - p: #graphene_point3d_t.
//
// The function returns the following values:
//
//   - res: return location for the result.
func (m *Matrix) TransformPoint3D(p *Point3D) *Point3D {
	var _arg0 *C.graphene_matrix_t  // out
	var _arg1 *C.graphene_point3d_t // out
	var _arg2 C.graphene_point3d_t  // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(p)))

	C.graphene_matrix_transform_point3d(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(p)

	var _res *Point3D // out

	_res = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// TransformRay: transform a #graphene_ray_t using the given matrix m.
//
// The function takes the following parameters:
//
//   - r: #graphene_ray_t.
//
// The function returns the following values:
//
//   - res: return location for the transformed ray.
func (m *Matrix) TransformRay(r *Ray) *Ray {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_ray_t    // out
	var _arg2 C.graphene_ray_t     // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_matrix_transform_ray(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(r)

	var _res *Ray // out

	_res = (*Ray)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// TransformRect transforms each corner of a #graphene_rect_t using the given
// matrix m.
//
// The result is a coplanar quadrilateral.
//
// See also: graphene_matrix_transform_point().
//
// The function takes the following parameters:
//
//   - r: #graphene_rect_t.
//
// The function returns the following values:
//
//   - res: return location for the transformed quad.
func (m *Matrix) TransformRect(r *Rect) *Quad {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_rect_t   // out
	var _arg2 C.graphene_quad_t    // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_matrix_transform_rect(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(r)

	var _res *Quad // out

	_res = (*Quad)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// TransformSphere transforms a #graphene_sphere_t using the given matrix m.
// The result is the bounding sphere containing the transformed sphere.
//
// The function takes the following parameters:
//
//   - s: #graphene_sphere_t.
//
// The function returns the following values:
//
//   - res: return location for the bounds of the transformed sphere.
func (m *Matrix) TransformSphere(s *Sphere) *Sphere {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_sphere_t // out
	var _arg2 C.graphene_sphere_t  // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(s)))

	C.graphene_matrix_transform_sphere(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(s)

	var _res *Sphere // out

	_res = (*Sphere)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// TransformVec3 transforms the given #graphene_vec3_t using the matrix m.
//
// This function will multiply the X, Y, and Z row vectors of the matrix m
// with the corresponding components of the vector v. The W row vector will be
// ignored.
//
// See also: graphene_simd4x4f_vec3_mul().
//
// The function takes the following parameters:
//
//   - v: #graphene_vec3_t.
//
// The function returns the following values:
//
//   - res: return location for a #graphene_vec3_t.
func (m *Matrix) TransformVec3(v *Vec3) *Vec3 {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_vec3_t   // out
	var _arg2 C.graphene_vec3_t    // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_matrix_transform_vec3(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(v)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// TransformVec4 transforms the given #graphene_vec4_t using the matrix m.
//
// See also: graphene_simd4x4f_vec4_mul().
//
// The function takes the following parameters:
//
//   - v: #graphene_vec4_t.
//
// The function returns the following values:
//
//   - res: return location for a #graphene_vec4_t.
func (m *Matrix) TransformVec4(v *Vec4) *Vec4 {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_vec4_t   // out
	var _arg2 C.graphene_vec4_t    // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_matrix_transform_vec4(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(m)
	runtime.KeepAlive(v)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Translate adds a translation transformation to m using the coordinates of the
// given #graphene_point3d_t.
//
// This is the equivalent of calling graphene_matrix_init_translate() and then
// multiplying m with the translation matrix.
//
// The function takes the following parameters:
//
//   - pos: #graphene_point3d_t.
func (m *Matrix) Translate(pos *Point3D) {
	var _arg0 *C.graphene_matrix_t  // out
	var _arg1 *C.graphene_point3d_t // out

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(pos)))

	C.graphene_matrix_translate(_arg0, _arg1)
	runtime.KeepAlive(m)
	runtime.KeepAlive(pos)
}

// Transpose transposes the given matrix.
//
// The function returns the following values:
//
//   - res: return location for the transposed matrix.
func (m *Matrix) Transpose() *Matrix {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 C.graphene_matrix_t  // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	C.graphene_matrix_transpose(_arg0, &_arg1)
	runtime.KeepAlive(m)

	var _res *Matrix // out

	_res = (*Matrix)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// UnprojectPoint3D unprojects the given point using the projection matrix and a
// modelview matrix.
//
// The function takes the following parameters:
//
//   - modelview for the modelview matrix; this is the inverse of the modelview
//     used when projecting the point.
//   - point with the coordinates of the point.
//
// The function returns the following values:
//
//   - res: return location for the unprojected point.
func (projection *Matrix) UnprojectPoint3D(modelview *Matrix, point *Point3D) *Point3D {
	var _arg0 *C.graphene_matrix_t  // out
	var _arg1 *C.graphene_matrix_t  // out
	var _arg2 *C.graphene_point3d_t // out
	var _arg3 C.graphene_point3d_t  // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(projection)))
	_arg1 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(modelview)))
	_arg2 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(point)))

	C.graphene_matrix_unproject_point3d(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(projection)
	runtime.KeepAlive(modelview)
	runtime.KeepAlive(point)

	var _res *Point3D // out

	_res = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _res
}

// UntransformBounds undoes the transformation on the corners of a
// #graphene_rect_t using the given matrix, within the given axis aligned
// rectangular bounds.
//
// The function takes the following parameters:
//
//   - r: #graphene_rect_t.
//   - bounds of the transformation.
//
// The function returns the following values:
//
//   - res: return location for the untransformed rectangle.
func (m *Matrix) UntransformBounds(r *Rect, bounds *Rect) *Rect {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_rect_t   // out
	var _arg2 *C.graphene_rect_t   // out
	var _arg3 C.graphene_rect_t    // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg2 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(bounds)))

	C.graphene_matrix_untransform_bounds(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(m)
	runtime.KeepAlive(r)
	runtime.KeepAlive(bounds)

	var _res *Rect // out

	_res = (*Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _res
}

// UntransformPoint undoes the transformation of a #graphene_point_t using the
// given matrix, within the given axis aligned rectangular bounds.
//
// The function takes the following parameters:
//
//   - p: #graphene_point_t.
//   - bounds of the transformation.
//
// The function returns the following values:
//
//   - res: return location for the untransformed point.
//   - ok: true if the point was successfully untransformed.
func (m *Matrix) UntransformPoint(p *Point, bounds *Rect) (*Point, bool) {
	var _arg0 *C.graphene_matrix_t // out
	var _arg1 *C.graphene_point_t  // out
	var _arg2 *C.graphene_rect_t   // out
	var _arg3 C.graphene_point_t   // in
	var _cret C._Bool              // in

	_arg0 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(p)))
	_arg2 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(bounds)))

	_cret = C.graphene_matrix_untransform_point(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(m)
	runtime.KeepAlive(p)
	runtime.KeepAlive(bounds)

	var _res *Point // out
	var _ok bool    // out

	_res = (*Point)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))
	if _cret {
		_ok = true
	}

	return _res, _ok
}

// Plane: 2D plane that extends infinitely in a 3D volume.
//
// The contents of the graphene_plane_t are private, and should not be modified
// directly.
//
// An instance of this type is always passed by reference.
type Plane struct {
	*plane
}

// plane is the struct that's finalized.
type plane struct {
	native *C.graphene_plane_t
}

func marshalPlane(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Plane{&plane{(*C.graphene_plane_t)(b)}}, nil
}

// NewPlaneAlloc constructs a struct Plane.
func NewPlaneAlloc() *Plane {
	var _cret *C.graphene_plane_t // in

	_cret = C.graphene_plane_alloc()

	var _plane *Plane // out

	_plane = (*Plane)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_plane)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_plane_free((*C.graphene_plane_t)(intern.C))
		},
	)

	return _plane
}

// Distance computes the distance of point from a #graphene_plane_t.
//
// The function takes the following parameters:
//
//   - point: #graphene_point3d_t.
//
// The function returns the following values:
//
//   - gfloat: distance of the given #graphene_point3d_t from the plane.
func (p *Plane) Distance(point *Point3D) float32 {
	var _arg0 *C.graphene_plane_t   // out
	var _arg1 *C.graphene_point3d_t // out
	var _cret C.float               // in

	_arg0 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(point)))

	_cret = C.graphene_plane_distance(_arg0, _arg1)
	runtime.KeepAlive(p)
	runtime.KeepAlive(point)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Equal checks whether the two given #graphene_plane_t are equal.
//
// The function takes the following parameters:
//
//   - b: #graphene_plane_t.
//
// The function returns the following values:
//
//   - ok: true if the given planes are equal.
func (a *Plane) Equal(b *Plane) bool {
	var _arg0 *C.graphene_plane_t // out
	var _arg1 *C.graphene_plane_t // out
	var _cret C._Bool             // in

	_arg0 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_plane_equal(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Constant retrieves the distance along the normal vector of the given
// #graphene_plane_t from the origin.
//
// The function returns the following values:
//
//   - gfloat: constant value of the plane.
func (p *Plane) Constant() float32 {
	var _arg0 *C.graphene_plane_t // out
	var _cret C.float             // in

	_arg0 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p)))

	_cret = C.graphene_plane_get_constant(_arg0)
	runtime.KeepAlive(p)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Normal retrieves the normal vector pointing towards the origin of the given
// #graphene_plane_t.
//
// The function returns the following values:
//
//   - normal: return location for the normal vector.
func (p *Plane) Normal() *Vec3 {
	var _arg0 *C.graphene_plane_t // out
	var _arg1 C.graphene_vec3_t   // in

	_arg0 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p)))

	C.graphene_plane_get_normal(_arg0, &_arg1)
	runtime.KeepAlive(p)

	var _normal *Vec3 // out

	_normal = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _normal
}

// Init initializes the given #graphene_plane_t using the given normal vector
// and constant values.
//
// The function takes the following parameters:
//
//   - normal (optional): unit length normal vector defining the plane pointing
//     towards the origin; if unset, we use the X axis by default.
//   - constant: distance from the origin to the plane along the normal vector;
//     the sign determines the half-space occupied by the plane.
//
// The function returns the following values:
//
//   - plane: initialized plane.
func (p *Plane) Init(normal *Vec3, constant float32) *Plane {
	var _arg0 *C.graphene_plane_t // out
	var _arg1 *C.graphene_vec3_t  // out
	var _arg2 C.float             // out
	var _cret *C.graphene_plane_t // in

	_arg0 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p)))
	if normal != nil {
		_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(normal)))
	}
	_arg2 = C.float(constant)

	_cret = C.graphene_plane_init(_arg0, _arg1, _arg2)
	runtime.KeepAlive(p)
	runtime.KeepAlive(normal)
	runtime.KeepAlive(constant)

	var _plane *Plane // out

	_plane = (*Plane)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _plane
}

// InitFromPlane initializes the given #graphene_plane_t using the normal vector
// and constant of another #graphene_plane_t.
//
// The function takes the following parameters:
//
//   - src: #graphene_plane_t.
//
// The function returns the following values:
//
//   - plane: initialized plane.
func (p *Plane) InitFromPlane(src *Plane) *Plane {
	var _arg0 *C.graphene_plane_t // out
	var _arg1 *C.graphene_plane_t // out
	var _cret *C.graphene_plane_t // in

	_arg0 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p)))
	_arg1 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_plane_init_from_plane(_arg0, _arg1)
	runtime.KeepAlive(p)
	runtime.KeepAlive(src)

	var _plane *Plane // out

	_plane = (*Plane)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _plane
}

// InitFromPoint initializes the given #graphene_plane_t using the given normal
// vector and an arbitrary co-planar point.
//
// The function takes the following parameters:
//
//   - normal vector defining the plane pointing towards the origin.
//   - point: #graphene_point3d_t.
//
// The function returns the following values:
//
//   - plane: initialized plane.
func (p *Plane) InitFromPoint(normal *Vec3, point *Point3D) *Plane {
	var _arg0 *C.graphene_plane_t   // out
	var _arg1 *C.graphene_vec3_t    // out
	var _arg2 *C.graphene_point3d_t // out
	var _cret *C.graphene_plane_t   // in

	_arg0 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(normal)))
	_arg2 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(point)))

	_cret = C.graphene_plane_init_from_point(_arg0, _arg1, _arg2)
	runtime.KeepAlive(p)
	runtime.KeepAlive(normal)
	runtime.KeepAlive(point)

	var _plane *Plane // out

	_plane = (*Plane)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _plane
}

// InitFromPoints initializes the given #graphene_plane_t using the 3 provided
// co-planar points.
//
// The winding order is counter-clockwise, and determines which direction the
// normal vector will point.
//
// The function takes the following parameters:
//
//   - a: #graphene_point3d_t.
//   - b: #graphene_point3d_t.
//   - c: #graphene_point3d_t.
//
// The function returns the following values:
//
//   - plane: initialized plane.
func (p *Plane) InitFromPoints(a *Point3D, b *Point3D, c *Point3D) *Plane {
	var _arg0 *C.graphene_plane_t   // out
	var _arg1 *C.graphene_point3d_t // out
	var _arg2 *C.graphene_point3d_t // out
	var _arg3 *C.graphene_point3d_t // out
	var _cret *C.graphene_plane_t   // in

	_arg0 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg2 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(b)))
	_arg3 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(c)))

	_cret = C.graphene_plane_init_from_points(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(p)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)
	runtime.KeepAlive(c)

	var _plane *Plane // out

	_plane = (*Plane)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _plane
}

// InitFromVec4 initializes the given #graphene_plane_t using the components of
// the given #graphene_vec4_t vector.
//
// The function takes the following parameters:
//
//   - src containing the normal vector in its first three components, and the
//     distance in its fourth component.
//
// The function returns the following values:
//
//   - plane: initialized plane.
func (p *Plane) InitFromVec4(src *Vec4) *Plane {
	var _arg0 *C.graphene_plane_t // out
	var _arg1 *C.graphene_vec4_t  // out
	var _cret *C.graphene_plane_t // in

	_arg0 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p)))
	_arg1 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_plane_init_from_vec4(_arg0, _arg1)
	runtime.KeepAlive(p)
	runtime.KeepAlive(src)

	var _plane *Plane // out

	_plane = (*Plane)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _plane
}

// Negate negates the normal vector and constant of a #graphene_plane_t,
// effectively mirroring the plane across the origin.
//
// The function returns the following values:
//
//   - res: return location for the negated plane.
func (p *Plane) Negate() *Plane {
	var _arg0 *C.graphene_plane_t // out
	var _arg1 C.graphene_plane_t  // in

	_arg0 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p)))

	C.graphene_plane_negate(_arg0, &_arg1)
	runtime.KeepAlive(p)

	var _res *Plane // out

	_res = (*Plane)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Normalize normalizes the vector of the given #graphene_plane_t, and adjusts
// the constant accordingly.
//
// The function returns the following values:
//
//   - res: return location for the normalized plane.
func (p *Plane) Normalize() *Plane {
	var _arg0 *C.graphene_plane_t // out
	var _arg1 C.graphene_plane_t  // in

	_arg0 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p)))

	C.graphene_plane_normalize(_arg0, &_arg1)
	runtime.KeepAlive(p)

	var _res *Plane // out

	_res = (*Plane)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Transform transforms a #graphene_plane_t p using the given matrix and
// normal_matrix.
//
// If normal_matrix is NULL, a transformation matrix for the plane normal will
// be computed from matrix. If you are transforming multiple planes using the
// same matrix it's recommended to compute the normal matrix beforehand to avoid
// incurring in the cost of recomputing it every time.
//
// The function takes the following parameters:
//
//   - matrix: #graphene_matrix_t.
//   - normalMatrix (optional): #graphene_matrix_t.
//
// The function returns the following values:
//
//   - res: transformed plane.
func (p *Plane) Transform(matrix *Matrix, normalMatrix *Matrix) *Plane {
	var _arg0 *C.graphene_plane_t  // out
	var _arg1 *C.graphene_matrix_t // out
	var _arg2 *C.graphene_matrix_t // out
	var _arg3 C.graphene_plane_t   // in

	_arg0 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p)))
	_arg1 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(matrix)))
	if normalMatrix != nil {
		_arg2 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(normalMatrix)))
	}

	C.graphene_plane_transform(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(p)
	runtime.KeepAlive(matrix)
	runtime.KeepAlive(normalMatrix)

	var _res *Plane // out

	_res = (*Plane)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _res
}

// Point: point with two coordinates.
//
// An instance of this type is always passed by reference.
type Point struct {
	*point
}

// point is the struct that's finalized.
type point struct {
	native *C.graphene_point_t
}

func marshalPoint(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Point{&point{(*C.graphene_point_t)(b)}}, nil
}

// NewPointAlloc constructs a struct Point.
func NewPointAlloc() *Point {
	var _cret *C.graphene_point_t // in

	_cret = C.graphene_point_alloc()

	var _point *Point // out

	_point = (*Point)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_point)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_point_free((*C.graphene_point_t)(intern.C))
		},
	)

	return _point
}

// X coordinate of the point.
func (p *Point) X() float32 {
	valptr := &p.native.x
	var _v float32 // out
	_v = float32(*valptr)
	return _v
}

// Y coordinate of the point.
func (p *Point) Y() float32 {
	valptr := &p.native.y
	var _v float32 // out
	_v = float32(*valptr)
	return _v
}

// X coordinate of the point.
func (p *Point) SetX(x float32) {
	valptr := &p.native.x
	*valptr = C.float(x)
}

// Y coordinate of the point.
func (p *Point) SetY(y float32) {
	valptr := &p.native.y
	*valptr = C.float(y)
}

// Distance computes the distance between a and b.
//
// The function takes the following parameters:
//
//   - b: #graphene_point_t.
//
// The function returns the following values:
//
//   - dX (optional): distance component on the X axis.
//   - dY (optional): distance component on the Y axis.
//   - gfloat: distance between the two points.
func (a *Point) Distance(b *Point) (dX float32, dY float32, gfloat float32) {
	var _arg0 *C.graphene_point_t // out
	var _arg1 *C.graphene_point_t // out
	var _arg2 C.float             // in
	var _arg3 C.float             // in
	var _cret C.float             // in

	_arg0 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_point_distance(_arg0, _arg1, &_arg2, &_arg3)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _dX float32     // out
	var _dY float32     // out
	var _gfloat float32 // out

	_dX = float32(_arg2)
	_dY = float32(_arg3)
	_gfloat = float32(_cret)

	return _dX, _dY, _gfloat
}

// Equal checks if the two points a and b point to the same coordinates.
//
// This function accounts for floating point fluctuations; if you want to
// control the fuzziness of the match, you can use graphene_point_near()
// instead.
//
// The function takes the following parameters:
//
//   - b: #graphene_point_t.
//
// The function returns the following values:
//
//   - ok: true if the points have the same coordinates.
func (a *Point) Equal(b *Point) bool {
	var _arg0 *C.graphene_point_t // out
	var _arg1 *C.graphene_point_t // out
	var _cret C._Bool             // in

	_arg0 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_point_equal(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Init initializes p to the given x and y coordinates.
//
// It's safe to call this function multiple times.
//
// The function takes the following parameters:
//
//   - x: x coordinate.
//   - y: y coordinate.
//
// The function returns the following values:
//
//   - point: initialized point.
func (p *Point) Init(x float32, y float32) *Point {
	var _arg0 *C.graphene_point_t // out
	var _arg1 C.float             // out
	var _arg2 C.float             // out
	var _cret *C.graphene_point_t // in

	_arg0 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(p)))
	_arg1 = C.float(x)
	_arg2 = C.float(y)

	_cret = C.graphene_point_init(_arg0, _arg1, _arg2)
	runtime.KeepAlive(p)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)

	var _point *Point // out

	_point = (*Point)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _point
}

// InitFromPoint initializes p with the same coordinates of src.
//
// The function takes the following parameters:
//
//   - src to use.
//
// The function returns the following values:
//
//   - point: initialized point.
func (p *Point) InitFromPoint(src *Point) *Point {
	var _arg0 *C.graphene_point_t // out
	var _arg1 *C.graphene_point_t // out
	var _cret *C.graphene_point_t // in

	_arg0 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(p)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_point_init_from_point(_arg0, _arg1)
	runtime.KeepAlive(p)
	runtime.KeepAlive(src)

	var _point *Point // out

	_point = (*Point)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _point
}

// InitFromVec2 initializes p with the coordinates inside the given
// #graphene_vec2_t.
//
// The function takes the following parameters:
//
//   - src: #graphene_vec2_t.
//
// The function returns the following values:
//
//   - point: initialized point.
func (p *Point) InitFromVec2(src *Vec2) *Point {
	var _arg0 *C.graphene_point_t // out
	var _arg1 *C.graphene_vec2_t  // out
	var _cret *C.graphene_point_t // in

	_arg0 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(p)))
	_arg1 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_point_init_from_vec2(_arg0, _arg1)
	runtime.KeepAlive(p)
	runtime.KeepAlive(src)

	var _point *Point // out

	_point = (*Point)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _point
}

// Interpolate: linearly interpolates the coordinates of a and b using the given
// factor.
//
// The function takes the following parameters:
//
//   - b: #graphene_point_t.
//   - factor: linear interpolation factor.
//
// The function returns the following values:
//
//   - res: return location for the interpolated point.
func (a *Point) Interpolate(b *Point, factor float64) *Point {
	var _arg0 *C.graphene_point_t // out
	var _arg1 *C.graphene_point_t // out
	var _arg2 C.double            // out
	var _arg3 C.graphene_point_t  // in

	_arg0 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(b)))
	_arg2 = C.double(factor)

	C.graphene_point_interpolate(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)
	runtime.KeepAlive(factor)

	var _res *Point // out

	_res = (*Point)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _res
}

// Near checks whether the two points a and b are within the threshold of
// epsilon.
//
// The function takes the following parameters:
//
//   - b: #graphene_point_t.
//   - epsilon: threshold between the two points.
//
// The function returns the following values:
//
//   - ok: true if the distance is within epsilon.
func (a *Point) Near(b *Point, epsilon float32) bool {
	var _arg0 *C.graphene_point_t // out
	var _arg1 *C.graphene_point_t // out
	var _arg2 C.float             // out
	var _cret C._Bool             // in

	_arg0 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(b)))
	_arg2 = C.float(epsilon)

	_cret = C.graphene_point_near(_arg0, _arg1, _arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)
	runtime.KeepAlive(epsilon)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// ToVec2 stores the coordinates of the given #graphene_point_t into a
// #graphene_vec2_t.
//
// The function returns the following values:
//
//   - v: return location for the vertex.
func (p *Point) ToVec2() *Vec2 {
	var _arg0 *C.graphene_point_t // out
	var _arg1 C.graphene_vec2_t   // in

	_arg0 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(p)))

	C.graphene_point_to_vec2(_arg0, &_arg1)
	runtime.KeepAlive(p)

	var _v *Vec2 // out

	_v = (*Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _v
}

// PointZero returns a point fixed at (0, 0).
//
// The function returns the following values:
//
//   - point: fixed point.
func PointZero() *Point {
	var _cret *C.graphene_point_t // in

	_cret = C.graphene_point_zero()

	var _point *Point // out

	_point = (*Point)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _point
}

// Point3D: point with three components: X, Y, and Z.
//
// An instance of this type is always passed by reference.
type Point3D struct {
	*point3D
}

// point3D is the struct that's finalized.
type point3D struct {
	native *C.graphene_point3d_t
}

func marshalPoint3D(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Point3D{&point3D{(*C.graphene_point3d_t)(b)}}, nil
}

// NewPoint3DAlloc constructs a struct Point3D.
func NewPoint3DAlloc() *Point3D {
	var _cret *C.graphene_point3d_t // in

	_cret = C.graphene_point3d_alloc()

	var _point3D *Point3D // out

	_point3D = (*Point3D)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_point3D)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_point3d_free((*C.graphene_point3d_t)(intern.C))
		},
	)

	return _point3D
}

// X coordinate.
func (p *Point3D) X() float32 {
	valptr := &p.native.x
	var _v float32 // out
	_v = float32(*valptr)
	return _v
}

// Y coordinate.
func (p *Point3D) Y() float32 {
	valptr := &p.native.y
	var _v float32 // out
	_v = float32(*valptr)
	return _v
}

// Z coordinate.
func (p *Point3D) Z() float32 {
	valptr := &p.native.z
	var _v float32 // out
	_v = float32(*valptr)
	return _v
}

// X coordinate.
func (p *Point3D) SetX(x float32) {
	valptr := &p.native.x
	*valptr = C.float(x)
}

// Y coordinate.
func (p *Point3D) SetY(y float32) {
	valptr := &p.native.y
	*valptr = C.float(y)
}

// Z coordinate.
func (p *Point3D) SetZ(z float32) {
	valptr := &p.native.z
	*valptr = C.float(z)
}

// Cross computes the cross product of the two given #graphene_point3d_t.
//
// The function takes the following parameters:
//
//   - b: #graphene_point3d_t.
//
// The function returns the following values:
//
//   - res: return location for the cross product.
func (a *Point3D) Cross(b *Point3D) *Point3D {
	var _arg0 *C.graphene_point3d_t // out
	var _arg1 *C.graphene_point3d_t // out
	var _arg2 C.graphene_point3d_t  // in

	_arg0 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_point3d_cross(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Point3D // out

	_res = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Distance computes the distance between the two given #graphene_point3d_t.
//
// The function takes the following parameters:
//
//   - b: #graphene_point3d_t.
//
// The function returns the following values:
//
//   - delta (optional): return location for the distance components on the X,
//     Y, and Z axis.
//   - gfloat: distance between two points.
func (a *Point3D) Distance(b *Point3D) (*Vec3, float32) {
	var _arg0 *C.graphene_point3d_t // out
	var _arg1 *C.graphene_point3d_t // out
	var _arg2 C.graphene_vec3_t     // in
	var _cret C.float               // in

	_arg0 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_point3d_distance(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _delta *Vec3    // out
	var _gfloat float32 // out

	_delta = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))
	_gfloat = float32(_cret)

	return _delta, _gfloat
}

// Dot computes the dot product of the two given #graphene_point3d_t.
//
// The function takes the following parameters:
//
//   - b: #graphene_point3d_t.
//
// The function returns the following values:
//
//   - gfloat: value of the dot product.
func (a *Point3D) Dot(b *Point3D) float32 {
	var _arg0 *C.graphene_point3d_t // out
	var _arg1 *C.graphene_point3d_t // out
	var _cret C.float               // in

	_arg0 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_point3d_dot(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Equal checks whether two given points are equal.
//
// The function takes the following parameters:
//
//   - b: #graphene_point3d_t.
//
// The function returns the following values:
//
//   - ok: true if the points are equal.
func (a *Point3D) Equal(b *Point3D) bool {
	var _arg0 *C.graphene_point3d_t // out
	var _arg1 *C.graphene_point3d_t // out
	var _cret C._Bool               // in

	_arg0 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_point3d_equal(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Init initializes a #graphene_point3d_t with the given coordinates.
//
// The function takes the following parameters:
//
//   - x: x coordinate of the point.
//   - y: y coordinate of the point.
//   - z: z coordinate of the point.
//
// The function returns the following values:
//
//   - point3D: initialized #graphene_point3d_t.
func (p *Point3D) Init(x float32, y float32, z float32) *Point3D {
	var _arg0 *C.graphene_point3d_t // out
	var _arg1 C.float               // out
	var _arg2 C.float               // out
	var _arg3 C.float               // out
	var _cret *C.graphene_point3d_t // in

	_arg0 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(p)))
	_arg1 = C.float(x)
	_arg2 = C.float(y)
	_arg3 = C.float(z)

	_cret = C.graphene_point3d_init(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(p)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
	runtime.KeepAlive(z)

	var _point3D *Point3D // out

	_point3D = (*Point3D)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _point3D
}

// InitFromPoint initializes a #graphene_point3d_t using the coordinates of
// another #graphene_point3d_t.
//
// The function takes the following parameters:
//
//   - src: #graphene_point3d_t.
//
// The function returns the following values:
//
//   - point3D: initialized point.
func (p *Point3D) InitFromPoint(src *Point3D) *Point3D {
	var _arg0 *C.graphene_point3d_t // out
	var _arg1 *C.graphene_point3d_t // out
	var _cret *C.graphene_point3d_t // in

	_arg0 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(p)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_point3d_init_from_point(_arg0, _arg1)
	runtime.KeepAlive(p)
	runtime.KeepAlive(src)

	var _point3D *Point3D // out

	_point3D = (*Point3D)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _point3D
}

// InitFromVec3 initializes a #graphene_point3d_t using the components of a
// #graphene_vec3_t.
//
// The function takes the following parameters:
//
//   - v: #graphene_vec3_t.
//
// The function returns the following values:
//
//   - point3D: initialized #graphene_point3d_t.
func (p *Point3D) InitFromVec3(v *Vec3) *Point3D {
	var _arg0 *C.graphene_point3d_t // out
	var _arg1 *C.graphene_vec3_t    // out
	var _cret *C.graphene_point3d_t // in

	_arg0 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(p)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))

	_cret = C.graphene_point3d_init_from_vec3(_arg0, _arg1)
	runtime.KeepAlive(p)
	runtime.KeepAlive(v)

	var _point3D *Point3D // out

	_point3D = (*Point3D)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _point3D
}

// Interpolate: linearly interpolates each component of a and b using the
// provided factor, and places the result in res.
//
// The function takes the following parameters:
//
//   - b: #graphene_point3d_t.
//   - factor: interpolation factor.
//
// The function returns the following values:
//
//   - res: return location for the interpolated #graphene_point3d_t.
func (a *Point3D) Interpolate(b *Point3D, factor float64) *Point3D {
	var _arg0 *C.graphene_point3d_t // out
	var _arg1 *C.graphene_point3d_t // out
	var _arg2 C.double              // out
	var _arg3 C.graphene_point3d_t  // in

	_arg0 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(b)))
	_arg2 = C.double(factor)

	C.graphene_point3d_interpolate(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)
	runtime.KeepAlive(factor)

	var _res *Point3D // out

	_res = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _res
}

// Length computes the length of the vector represented by the coordinates of
// the given #graphene_point3d_t.
//
// The function returns the following values:
//
//   - gfloat: length of the vector represented by the point.
func (p *Point3D) Length() float32 {
	var _arg0 *C.graphene_point3d_t // out
	var _cret C.float               // in

	_arg0 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(p)))

	_cret = C.graphene_point3d_length(_arg0)
	runtime.KeepAlive(p)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Near checks whether the two points are near each other, within an epsilon
// factor.
//
// The function takes the following parameters:
//
//   - b: #graphene_point3d_t.
//   - epsilon fuzzyness factor.
//
// The function returns the following values:
//
//   - ok: true if the points are near each other.
func (a *Point3D) Near(b *Point3D, epsilon float32) bool {
	var _arg0 *C.graphene_point3d_t // out
	var _arg1 *C.graphene_point3d_t // out
	var _arg2 C.float               // out
	var _cret C._Bool               // in

	_arg0 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(b)))
	_arg2 = C.float(epsilon)

	_cret = C.graphene_point3d_near(_arg0, _arg1, _arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)
	runtime.KeepAlive(epsilon)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Normalize computes the normalization of the vector represented by the
// coordinates of the given #graphene_point3d_t.
//
// The function returns the following values:
//
//   - res: return location for the normalized #graphene_point3d_t.
func (p *Point3D) Normalize() *Point3D {
	var _arg0 *C.graphene_point3d_t // out
	var _arg1 C.graphene_point3d_t  // in

	_arg0 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(p)))

	C.graphene_point3d_normalize(_arg0, &_arg1)
	runtime.KeepAlive(p)

	var _res *Point3D // out

	_res = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// NormalizeViewport normalizes the coordinates of a #graphene_point3d_t using
// the given viewport and clipping planes.
//
// The coordinates of the resulting #graphene_point3d_t will be in the [ -1,
// 1 ] range.
//
// The function takes the following parameters:
//
//   - viewport representing a viewport.
//   - zNear: coordinate of the near clipping plane, or 0 for the default near
//     clipping plane.
//   - zFar: coordinate of the far clipping plane, or 1 for the default far
//     clipping plane.
//
// The function returns the following values:
//
//   - res: return location for the normalized #graphene_point3d_t.
func (p *Point3D) NormalizeViewport(viewport *Rect, zNear float32, zFar float32) *Point3D {
	var _arg0 *C.graphene_point3d_t // out
	var _arg1 *C.graphene_rect_t    // out
	var _arg2 C.float               // out
	var _arg3 C.float               // out
	var _arg4 C.graphene_point3d_t  // in

	_arg0 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(p)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(viewport)))
	_arg2 = C.float(zNear)
	_arg3 = C.float(zFar)

	C.graphene_point3d_normalize_viewport(_arg0, _arg1, _arg2, _arg3, &_arg4)
	runtime.KeepAlive(p)
	runtime.KeepAlive(viewport)
	runtime.KeepAlive(zNear)
	runtime.KeepAlive(zFar)

	var _res *Point3D // out

	_res = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg4))))

	return _res
}

// Scale scales the coordinates of the given #graphene_point3d_t by the given
// factor.
//
// The function takes the following parameters:
//
//   - factor: scaling factor.
//
// The function returns the following values:
//
//   - res: return location for the scaled point.
func (p *Point3D) Scale(factor float32) *Point3D {
	var _arg0 *C.graphene_point3d_t // out
	var _arg1 C.float               // out
	var _arg2 C.graphene_point3d_t  // in

	_arg0 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(p)))
	_arg1 = C.float(factor)

	C.graphene_point3d_scale(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(p)
	runtime.KeepAlive(factor)

	var _res *Point3D // out

	_res = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// ToVec3 stores the coordinates of a #graphene_point3d_t into a
// #graphene_vec3_t.
//
// The function returns the following values:
//
//   - v: return location for a #graphene_vec3_t.
func (p *Point3D) ToVec3() *Vec3 {
	var _arg0 *C.graphene_point3d_t // out
	var _arg1 C.graphene_vec3_t     // in

	_arg0 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(p)))

	C.graphene_point3d_to_vec3(_arg0, &_arg1)
	runtime.KeepAlive(p)

	var _v *Vec3 // out

	_v = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _v
}

// Point3DZero retrieves a constant point with all three coordinates set to 0.
//
// The function returns the following values:
//
//   - point3D: zero point.
func Point3DZero() *Point3D {
	var _cret *C.graphene_point3d_t // in

	_cret = C.graphene_point3d_zero()

	var _point3D *Point3D // out

	_point3D = (*Point3D)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _point3D
}

// Quad: 4 vertex quadrilateral, as represented by four #graphene_point_t.
//
// The contents of a #graphene_quad_t are private and should never be accessed
// directly.
//
// An instance of this type is always passed by reference.
type Quad struct {
	*quad
}

// quad is the struct that's finalized.
type quad struct {
	native *C.graphene_quad_t
}

func marshalQuad(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Quad{&quad{(*C.graphene_quad_t)(b)}}, nil
}

// NewQuadAlloc constructs a struct Quad.
func NewQuadAlloc() *Quad {
	var _cret *C.graphene_quad_t // in

	_cret = C.graphene_quad_alloc()

	var _quad *Quad // out

	_quad = (*Quad)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_quad)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_quad_free((*C.graphene_quad_t)(intern.C))
		},
	)

	return _quad
}

// Bounds computes the bounding rectangle of q and places it into r.
//
// The function returns the following values:
//
//   - r: return location for a #graphene_rect_t.
func (q *Quad) Bounds() *Rect {
	var _arg0 *C.graphene_quad_t // out
	var _arg1 C.graphene_rect_t  // in

	_arg0 = (*C.graphene_quad_t)(gextras.StructNative(unsafe.Pointer(q)))

	C.graphene_quad_bounds(_arg0, &_arg1)
	runtime.KeepAlive(q)

	var _r *Rect // out

	_r = (*Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _r
}

// Contains checks if the given #graphene_quad_t contains the given
// #graphene_point_t.
//
// The function takes the following parameters:
//
//   - p: #graphene_point_t.
//
// The function returns the following values:
//
//   - ok: true if the point is inside the #graphene_quad_t.
func (q *Quad) Contains(p *Point) bool {
	var _arg0 *C.graphene_quad_t  // out
	var _arg1 *C.graphene_point_t // out
	var _cret C._Bool             // in

	_arg0 = (*C.graphene_quad_t)(gextras.StructNative(unsafe.Pointer(q)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(p)))

	_cret = C.graphene_quad_contains(_arg0, _arg1)
	runtime.KeepAlive(q)
	runtime.KeepAlive(p)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Point retrieves the point of a #graphene_quad_t at the given index.
//
// The function takes the following parameters:
//
//   - index_: index of the point to retrieve.
//
// The function returns the following values:
//
//   - point: #graphene_point_t.
func (q *Quad) Point(index_ uint) *Point {
	var _arg0 *C.graphene_quad_t  // out
	var _arg1 C.uint              // out
	var _cret *C.graphene_point_t // in

	_arg0 = (*C.graphene_quad_t)(gextras.StructNative(unsafe.Pointer(q)))
	_arg1 = C.uint(index_)

	_cret = C.graphene_quad_get_point(_arg0, _arg1)
	runtime.KeepAlive(q)
	runtime.KeepAlive(index_)

	var _point *Point // out

	_point = (*Point)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _point
}

// Init initializes a #graphene_quad_t with the given points.
//
// The function takes the following parameters:
//
//   - p1: first point of the quadrilateral.
//   - p2: second point of the quadrilateral.
//   - p3: third point of the quadrilateral.
//   - p4: fourth point of the quadrilateral.
//
// The function returns the following values:
//
//   - quad: initialized #graphene_quad_t.
func (q *Quad) Init(p1 *Point, p2 *Point, p3 *Point, p4 *Point) *Quad {
	var _arg0 *C.graphene_quad_t  // out
	var _arg1 *C.graphene_point_t // out
	var _arg2 *C.graphene_point_t // out
	var _arg3 *C.graphene_point_t // out
	var _arg4 *C.graphene_point_t // out
	var _cret *C.graphene_quad_t  // in

	_arg0 = (*C.graphene_quad_t)(gextras.StructNative(unsafe.Pointer(q)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(p1)))
	_arg2 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(p2)))
	_arg3 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(p3)))
	_arg4 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(p4)))

	_cret = C.graphene_quad_init(_arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(q)
	runtime.KeepAlive(p1)
	runtime.KeepAlive(p2)
	runtime.KeepAlive(p3)
	runtime.KeepAlive(p4)

	var _quad *Quad // out

	_quad = (*Quad)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _quad
}

// InitFromPoints initializes a #graphene_quad_t using an array of points.
//
// The function takes the following parameters:
//
//   - points: array of 4 #graphene_point_t.
//
// The function returns the following values:
//
//   - quad: initialized #graphene_quad_t.
func (q *Quad) InitFromPoints(points [4]Point) *Quad {
	var _arg0 *C.graphene_quad_t  // out
	var _arg1 *C.graphene_point_t // out
	var _cret *C.graphene_quad_t  // in

	_arg0 = (*C.graphene_quad_t)(gextras.StructNative(unsafe.Pointer(q)))
	{
		var out [4]C.graphene_point_t
		_arg1 = &out[0]
		for i := 0; i < 4; i++ {
			out[i] = *(*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer((&points[i]))))
		}
	}

	_cret = C.graphene_quad_init_from_points(_arg0, _arg1)
	runtime.KeepAlive(q)
	runtime.KeepAlive(points)

	var _quad *Quad // out

	_quad = (*Quad)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _quad
}

// InitFromRect initializes a #graphene_quad_t using the four corners of the
// given #graphene_rect_t.
//
// The function takes the following parameters:
//
//   - r: #graphene_rect_t.
//
// The function returns the following values:
//
//   - quad: initialized #graphene_quad_t.
func (q *Quad) InitFromRect(r *Rect) *Quad {
	var _arg0 *C.graphene_quad_t // out
	var _arg1 *C.graphene_rect_t // out
	var _cret *C.graphene_quad_t // in

	_arg0 = (*C.graphene_quad_t)(gextras.StructNative(unsafe.Pointer(q)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	_cret = C.graphene_quad_init_from_rect(_arg0, _arg1)
	runtime.KeepAlive(q)
	runtime.KeepAlive(r)

	var _quad *Quad // out

	_quad = (*Quad)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _quad
}

// Quaternion: quaternion.
//
// The contents of the #graphene_quaternion_t structure are private and should
// never be accessed directly.
//
// An instance of this type is always passed by reference.
type Quaternion struct {
	*quaternion
}

// quaternion is the struct that's finalized.
type quaternion struct {
	native *C.graphene_quaternion_t
}

func marshalQuaternion(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Quaternion{&quaternion{(*C.graphene_quaternion_t)(b)}}, nil
}

// NewQuaternionAlloc constructs a struct Quaternion.
func NewQuaternionAlloc() *Quaternion {
	var _cret *C.graphene_quaternion_t // in

	_cret = C.graphene_quaternion_alloc()

	var _quaternion *Quaternion // out

	_quaternion = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_quaternion)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_quaternion_free((*C.graphene_quaternion_t)(intern.C))
		},
	)

	return _quaternion
}

// Add adds two #graphene_quaternion_t a and b.
//
// The function takes the following parameters:
//
//   - b: #graphene_quaternion_t.
//
// The function returns the following values:
//
//   - res: result of the operation.
func (a *Quaternion) Add(b *Quaternion) *Quaternion {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 *C.graphene_quaternion_t // out
	var _arg2 C.graphene_quaternion_t  // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_quaternion_add(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Quaternion // out

	_res = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Dot computes the dot product of two #graphene_quaternion_t.
//
// The function takes the following parameters:
//
//   - b: #graphene_quaternion_t.
//
// The function returns the following values:
//
//   - gfloat: value of the dot products.
func (a *Quaternion) Dot(b *Quaternion) float32 {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 *C.graphene_quaternion_t // out
	var _cret C.float                  // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_quaternion_dot(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Equal checks whether the given quaternions are equal.
//
// The function takes the following parameters:
//
//   - b: #graphene_quaternion_t.
//
// The function returns the following values:
//
//   - ok: true if the quaternions are equal.
func (a *Quaternion) Equal(b *Quaternion) bool {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 *C.graphene_quaternion_t // out
	var _cret C._Bool                  // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_quaternion_equal(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Init initializes a #graphene_quaternion_t using the given four values.
//
// The function takes the following parameters:
//
//   - x: first component of the quaternion.
//   - y: second component of the quaternion.
//   - z: third component of the quaternion.
//   - w: fourth component of the quaternion.
//
// The function returns the following values:
//
//   - quaternion: initialized quaternion.
func (q *Quaternion) Init(x float32, y float32, z float32, w float32) *Quaternion {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 C.float                  // out
	var _arg2 C.float                  // out
	var _arg3 C.float                  // out
	var _arg4 C.float                  // out
	var _cret *C.graphene_quaternion_t // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))
	_arg1 = C.float(x)
	_arg2 = C.float(y)
	_arg3 = C.float(z)
	_arg4 = C.float(w)

	_cret = C.graphene_quaternion_init(_arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(q)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
	runtime.KeepAlive(z)
	runtime.KeepAlive(w)

	var _quaternion *Quaternion // out

	_quaternion = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _quaternion
}

// InitFromAngleVec3 initializes a #graphene_quaternion_t using an angle on a
// specific axis.
//
// The function takes the following parameters:
//
//   - angle: rotation on a given axis, in degrees.
//   - axis of rotation, expressed as a vector.
//
// The function returns the following values:
//
//   - quaternion: initialized quaternion.
func (q *Quaternion) InitFromAngleVec3(angle float32, axis *Vec3) *Quaternion {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 C.float                  // out
	var _arg2 *C.graphene_vec3_t       // out
	var _cret *C.graphene_quaternion_t // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))
	_arg1 = C.float(angle)
	_arg2 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(axis)))

	_cret = C.graphene_quaternion_init_from_angle_vec3(_arg0, _arg1, _arg2)
	runtime.KeepAlive(q)
	runtime.KeepAlive(angle)
	runtime.KeepAlive(axis)

	var _quaternion *Quaternion // out

	_quaternion = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _quaternion
}

// InitFromAngles initializes a #graphene_quaternion_t using the values of the
// Euler angles (http://en.wikipedia.org/wiki/Euler_angles) on each axis.
//
// See also: graphene_quaternion_init_from_euler().
//
// The function takes the following parameters:
//
//   - degX: rotation angle on the X axis (yaw), in degrees.
//   - degY: rotation angle on the Y axis (pitch), in degrees.
//   - degZ: rotation angle on the Z axis (roll), in degrees.
//
// The function returns the following values:
//
//   - quaternion: initialized quaternion.
func (q *Quaternion) InitFromAngles(degX float32, degY float32, degZ float32) *Quaternion {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 C.float                  // out
	var _arg2 C.float                  // out
	var _arg3 C.float                  // out
	var _cret *C.graphene_quaternion_t // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))
	_arg1 = C.float(degX)
	_arg2 = C.float(degY)
	_arg3 = C.float(degZ)

	_cret = C.graphene_quaternion_init_from_angles(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(q)
	runtime.KeepAlive(degX)
	runtime.KeepAlive(degY)
	runtime.KeepAlive(degZ)

	var _quaternion *Quaternion // out

	_quaternion = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _quaternion
}

// InitFromEuler initializes a #graphene_quaternion_t using the given
// #graphene_euler_t.
//
// The function takes the following parameters:
//
//   - e: #graphene_euler_t.
//
// The function returns the following values:
//
//   - quaternion: initialized #graphene_quaternion_t.
func (q *Quaternion) InitFromEuler(e *Euler) *Quaternion {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 *C.graphene_euler_t      // out
	var _cret *C.graphene_quaternion_t // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))
	_arg1 = (*C.graphene_euler_t)(gextras.StructNative(unsafe.Pointer(e)))

	_cret = C.graphene_quaternion_init_from_euler(_arg0, _arg1)
	runtime.KeepAlive(q)
	runtime.KeepAlive(e)

	var _quaternion *Quaternion // out

	_quaternion = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _quaternion
}

// InitFromMatrix initializes a #graphene_quaternion_t using the rotation
// components of a transformation matrix.
//
// The function takes the following parameters:
//
//   - m: #graphene_matrix_t.
//
// The function returns the following values:
//
//   - quaternion: initialized quaternion.
func (q *Quaternion) InitFromMatrix(m *Matrix) *Quaternion {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 *C.graphene_matrix_t     // out
	var _cret *C.graphene_quaternion_t // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))
	_arg1 = (*C.graphene_matrix_t)(gextras.StructNative(unsafe.Pointer(m)))

	_cret = C.graphene_quaternion_init_from_matrix(_arg0, _arg1)
	runtime.KeepAlive(q)
	runtime.KeepAlive(m)

	var _quaternion *Quaternion // out

	_quaternion = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _quaternion
}

// InitFromQuaternion initializes a #graphene_quaternion_t with the values from
// src.
//
// The function takes the following parameters:
//
//   - src: #graphene_quaternion_t.
//
// The function returns the following values:
//
//   - quaternion: initialized quaternion.
func (q *Quaternion) InitFromQuaternion(src *Quaternion) *Quaternion {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 *C.graphene_quaternion_t // out
	var _cret *C.graphene_quaternion_t // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))
	_arg1 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_quaternion_init_from_quaternion(_arg0, _arg1)
	runtime.KeepAlive(q)
	runtime.KeepAlive(src)

	var _quaternion *Quaternion // out

	_quaternion = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _quaternion
}

// InitFromRadians initializes a #graphene_quaternion_t using the values of the
// Euler angles (http://en.wikipedia.org/wiki/Euler_angles) on each axis.
//
// See also: graphene_quaternion_init_from_euler().
//
// The function takes the following parameters:
//
//   - radX: rotation angle on the X axis (yaw), in radians.
//   - radY: rotation angle on the Y axis (pitch), in radians.
//   - radZ: rotation angle on the Z axis (roll), in radians.
//
// The function returns the following values:
//
//   - quaternion: initialized quaternion.
func (q *Quaternion) InitFromRadians(radX float32, radY float32, radZ float32) *Quaternion {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 C.float                  // out
	var _arg2 C.float                  // out
	var _arg3 C.float                  // out
	var _cret *C.graphene_quaternion_t // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))
	_arg1 = C.float(radX)
	_arg2 = C.float(radY)
	_arg3 = C.float(radZ)

	_cret = C.graphene_quaternion_init_from_radians(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(q)
	runtime.KeepAlive(radX)
	runtime.KeepAlive(radY)
	runtime.KeepAlive(radZ)

	var _quaternion *Quaternion // out

	_quaternion = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _quaternion
}

// InitFromVec4 initializes a #graphene_quaternion_t with the values from src.
//
// The function takes the following parameters:
//
//   - src: #graphene_vec4_t.
//
// The function returns the following values:
//
//   - quaternion: initialized quaternion.
func (q *Quaternion) InitFromVec4(src *Vec4) *Quaternion {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 *C.graphene_vec4_t       // out
	var _cret *C.graphene_quaternion_t // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))
	_arg1 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_quaternion_init_from_vec4(_arg0, _arg1)
	runtime.KeepAlive(q)
	runtime.KeepAlive(src)

	var _quaternion *Quaternion // out

	_quaternion = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _quaternion
}

// InitIdentity initializes a #graphene_quaternion_t using the identity
// transformation.
//
// The function returns the following values:
//
//   - quaternion: initialized quaternion.
func (q *Quaternion) InitIdentity() *Quaternion {
	var _arg0 *C.graphene_quaternion_t // out
	var _cret *C.graphene_quaternion_t // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))

	_cret = C.graphene_quaternion_init_identity(_arg0)
	runtime.KeepAlive(q)

	var _quaternion *Quaternion // out

	_quaternion = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _quaternion
}

// Invert inverts a #graphene_quaternion_t, and returns the conjugate quaternion
// of q.
//
// The function returns the following values:
//
//   - res: return location for the inverted quaternion.
func (q *Quaternion) Invert() *Quaternion {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 C.graphene_quaternion_t  // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))

	C.graphene_quaternion_invert(_arg0, &_arg1)
	runtime.KeepAlive(q)

	var _res *Quaternion // out

	_res = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Multiply multiplies two #graphene_quaternion_t a and b.
//
// The function takes the following parameters:
//
//   - b: #graphene_quaternion_t.
//
// The function returns the following values:
//
//   - res: result of the operation.
func (a *Quaternion) Multiply(b *Quaternion) *Quaternion {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 *C.graphene_quaternion_t // out
	var _arg2 C.graphene_quaternion_t  // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_quaternion_multiply(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Quaternion // out

	_res = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Normalize normalizes a #graphene_quaternion_t.
//
// The function returns the following values:
//
//   - res: return location for the normalized quaternion.
func (q *Quaternion) Normalize() *Quaternion {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 C.graphene_quaternion_t  // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))

	C.graphene_quaternion_normalize(_arg0, &_arg1)
	runtime.KeepAlive(q)

	var _res *Quaternion // out

	_res = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Scale scales all the elements of a #graphene_quaternion_t q using the given
// scalar factor.
//
// The function takes the following parameters:
//
//   - factor: scaling factor.
//
// The function returns the following values:
//
//   - res: result of the operation.
func (q *Quaternion) Scale(factor float32) *Quaternion {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 C.float                  // out
	var _arg2 C.graphene_quaternion_t  // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))
	_arg1 = C.float(factor)

	C.graphene_quaternion_scale(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(q)
	runtime.KeepAlive(factor)

	var _res *Quaternion // out

	_res = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Slerp interpolates between the two given quaternions using a spherical linear
// interpolation, or SLERP (http://en.wikipedia.org/wiki/Slerp), using the given
// interpolation factor.
//
// The function takes the following parameters:
//
//   - b: #graphene_quaternion_t.
//   - factor: linear interpolation factor.
//
// The function returns the following values:
//
//   - res: return location for the interpolated quaternion.
func (a *Quaternion) Slerp(b *Quaternion, factor float32) *Quaternion {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 *C.graphene_quaternion_t // out
	var _arg2 C.float                  // out
	var _arg3 C.graphene_quaternion_t  // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(b)))
	_arg2 = C.float(factor)

	C.graphene_quaternion_slerp(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)
	runtime.KeepAlive(factor)

	var _res *Quaternion // out

	_res = (*Quaternion)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _res
}

// ToAngleVec3 converts a quaternion into an angle, axis pair.
//
// The function returns the following values:
//
//   - angle: return location for the angle, in degrees.
//   - axis: return location for the rotation axis.
func (q *Quaternion) ToAngleVec3() (float32, *Vec3) {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 C.float                  // in
	var _arg2 C.graphene_vec3_t        // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))

	C.graphene_quaternion_to_angle_vec3(_arg0, &_arg1, &_arg2)
	runtime.KeepAlive(q)

	var _angle float32 // out
	var _axis *Vec3    // out

	_angle = float32(_arg1)
	_axis = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _angle, _axis
}

// ToAngles converts a #graphene_quaternion_t to its corresponding rotations on
// the Euler angles (http://en.wikipedia.org/wiki/Euler_angles) on each axis.
//
// The function returns the following values:
//
//   - degX (optional): return location for the rotation angle on the X axis
//     (yaw), in degrees.
//   - degY (optional): return location for the rotation angle on the Y axis
//     (pitch), in degrees.
//   - degZ (optional): return location for the rotation angle on the Z axis
//     (roll), in degrees.
func (q *Quaternion) ToAngles() (degX float32, degY float32, degZ float32) {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 C.float                  // in
	var _arg2 C.float                  // in
	var _arg3 C.float                  // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))

	C.graphene_quaternion_to_angles(_arg0, &_arg1, &_arg2, &_arg3)
	runtime.KeepAlive(q)

	var _degX float32 // out
	var _degY float32 // out
	var _degZ float32 // out

	_degX = float32(_arg1)
	_degY = float32(_arg2)
	_degZ = float32(_arg3)

	return _degX, _degY, _degZ
}

// ToMatrix converts a quaternion into a transformation matrix expressing the
// rotation defined by the #graphene_quaternion_t.
//
// The function returns the following values:
//
//   - m: #graphene_matrix_t.
func (q *Quaternion) ToMatrix() *Matrix {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 C.graphene_matrix_t      // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))

	C.graphene_quaternion_to_matrix(_arg0, &_arg1)
	runtime.KeepAlive(q)

	var _m *Matrix // out

	_m = (*Matrix)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _m
}

// ToRadians converts a #graphene_quaternion_t to its corresponding rotations on
// the Euler angles (http://en.wikipedia.org/wiki/Euler_angles) on each axis.
//
// The function returns the following values:
//
//   - radX (optional): return location for the rotation angle on the X axis
//     (yaw), in radians.
//   - radY (optional): return location for the rotation angle on the Y axis
//     (pitch), in radians.
//   - radZ (optional): return location for the rotation angle on the Z axis
//     (roll), in radians.
func (q *Quaternion) ToRadians() (radX float32, radY float32, radZ float32) {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 C.float                  // in
	var _arg2 C.float                  // in
	var _arg3 C.float                  // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))

	C.graphene_quaternion_to_radians(_arg0, &_arg1, &_arg2, &_arg3)
	runtime.KeepAlive(q)

	var _radX float32 // out
	var _radY float32 // out
	var _radZ float32 // out

	_radX = float32(_arg1)
	_radY = float32(_arg2)
	_radZ = float32(_arg3)

	return _radX, _radY, _radZ
}

// ToVec4 copies the components of a #graphene_quaternion_t into a
// #graphene_vec4_t.
//
// The function returns the following values:
//
//   - res: return location for a #graphene_vec4_t.
func (q *Quaternion) ToVec4() *Vec4 {
	var _arg0 *C.graphene_quaternion_t // out
	var _arg1 C.graphene_vec4_t        // in

	_arg0 = (*C.graphene_quaternion_t)(gextras.StructNative(unsafe.Pointer(q)))

	C.graphene_quaternion_to_vec4(_arg0, &_arg1)
	runtime.KeepAlive(q)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Ray: ray emitted from an origin in a given direction.
//
// The contents of the graphene_ray_t structure are private, and should not be
// modified directly.
//
// An instance of this type is always passed by reference.
type Ray struct {
	*ray
}

// ray is the struct that's finalized.
type ray struct {
	native *C.graphene_ray_t
}

func marshalRay(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Ray{&ray{(*C.graphene_ray_t)(b)}}, nil
}

// NewRayAlloc constructs a struct Ray.
func NewRayAlloc() *Ray {
	var _cret *C.graphene_ray_t // in

	_cret = C.graphene_ray_alloc()

	var _ray *Ray // out

	_ray = (*Ray)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_ray)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_ray_free((*C.graphene_ray_t)(intern.C))
		},
	)

	return _ray
}

// Equal checks whether the two given #graphene_ray_t are equal.
//
// The function takes the following parameters:
//
//   - b: #graphene_ray_t.
//
// The function returns the following values:
//
//   - ok: true if the given rays are equal.
func (a *Ray) Equal(b *Ray) bool {
	var _arg0 *C.graphene_ray_t // out
	var _arg1 *C.graphene_ray_t // out
	var _cret C._Bool           // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_ray_equal(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// ClosestPointToPoint computes the point on the given #graphene_ray_t that is
// closest to the given point p.
//
// The function takes the following parameters:
//
//   - p: #graphene_point3d_t.
//
// The function returns the following values:
//
//   - res: return location for the closest point3d.
func (r *Ray) ClosestPointToPoint(p *Point3D) *Point3D {
	var _arg0 *C.graphene_ray_t     // out
	var _arg1 *C.graphene_point3d_t // out
	var _arg2 C.graphene_point3d_t  // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(p)))

	C.graphene_ray_get_closest_point_to_point(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(r)
	runtime.KeepAlive(p)

	var _res *Point3D // out

	_res = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Direction retrieves the direction of the given #graphene_ray_t.
//
// The function returns the following values:
//
//   - direction: return location for the direction.
func (r *Ray) Direction() *Vec3 {
	var _arg0 *C.graphene_ray_t // out
	var _arg1 C.graphene_vec3_t // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_ray_get_direction(_arg0, &_arg1)
	runtime.KeepAlive(r)

	var _direction *Vec3 // out

	_direction = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _direction
}

// DistanceToPlane computes the distance of the origin of the given
// #graphene_ray_t from the given plane.
//
// If the ray does not intersect the plane, this function returns INFINITY.
//
// The function takes the following parameters:
//
//   - p: #graphene_plane_t.
//
// The function returns the following values:
//
//   - gfloat: distance of the origin of the ray from the plane.
func (r *Ray) DistanceToPlane(p *Plane) float32 {
	var _arg0 *C.graphene_ray_t   // out
	var _arg1 *C.graphene_plane_t // out
	var _cret C.float             // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = (*C.graphene_plane_t)(gextras.StructNative(unsafe.Pointer(p)))

	_cret = C.graphene_ray_get_distance_to_plane(_arg0, _arg1)
	runtime.KeepAlive(r)
	runtime.KeepAlive(p)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// DistanceToPoint computes the distance of the closest approach between the
// given #graphene_ray_t r and the point p.
//
// The closest approach to a ray from a point is the distance between the point
// and the projection of the point on the ray itself.
//
// The function takes the following parameters:
//
//   - p: #graphene_point3d_t.
//
// The function returns the following values:
//
//   - gfloat: distance of the point.
func (r *Ray) DistanceToPoint(p *Point3D) float32 {
	var _arg0 *C.graphene_ray_t     // out
	var _arg1 *C.graphene_point3d_t // out
	var _cret C.float               // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(p)))

	_cret = C.graphene_ray_get_distance_to_point(_arg0, _arg1)
	runtime.KeepAlive(r)
	runtime.KeepAlive(p)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Origin retrieves the origin of the given #graphene_ray_t.
//
// The function returns the following values:
//
//   - origin: return location for the origin.
func (r *Ray) Origin() *Point3D {
	var _arg0 *C.graphene_ray_t    // out
	var _arg1 C.graphene_point3d_t // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_ray_get_origin(_arg0, &_arg1)
	runtime.KeepAlive(r)

	var _origin *Point3D // out

	_origin = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _origin
}

// PositionAt retrieves the coordinates of a point at the distance t along the
// given #graphene_ray_t.
//
// The function takes the following parameters:
//
//   - t: distance along the ray.
//
// The function returns the following values:
//
//   - position: return location for the position.
func (r *Ray) PositionAt(t float32) *Point3D {
	var _arg0 *C.graphene_ray_t    // out
	var _arg1 C.float              // out
	var _arg2 C.graphene_point3d_t // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = C.float(t)

	C.graphene_ray_get_position_at(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(r)
	runtime.KeepAlive(t)

	var _position *Point3D // out

	_position = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _position
}

// Init initializes the given #graphene_ray_t using the given origin and
// direction values.
//
// The function takes the following parameters:
//
//   - origin (optional) of the ray.
//   - direction (optional) vector.
//
// The function returns the following values:
//
//   - ray: initialized ray.
func (r *Ray) Init(origin *Point3D, direction *Vec3) *Ray {
	var _arg0 *C.graphene_ray_t     // out
	var _arg1 *C.graphene_point3d_t // out
	var _arg2 *C.graphene_vec3_t    // out
	var _cret *C.graphene_ray_t     // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))
	if origin != nil {
		_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(origin)))
	}
	if direction != nil {
		_arg2 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(direction)))
	}

	_cret = C.graphene_ray_init(_arg0, _arg1, _arg2)
	runtime.KeepAlive(r)
	runtime.KeepAlive(origin)
	runtime.KeepAlive(direction)

	var _ray *Ray // out

	_ray = (*Ray)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _ray
}

// InitFromRay initializes the given #graphene_ray_t using the origin and
// direction values of another #graphene_ray_t.
//
// The function takes the following parameters:
//
//   - src: #graphene_ray_t.
//
// The function returns the following values:
//
//   - ray: initialized ray.
func (r *Ray) InitFromRay(src *Ray) *Ray {
	var _arg0 *C.graphene_ray_t // out
	var _arg1 *C.graphene_ray_t // out
	var _cret *C.graphene_ray_t // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_ray_init_from_ray(_arg0, _arg1)
	runtime.KeepAlive(r)
	runtime.KeepAlive(src)

	var _ray *Ray // out

	_ray = (*Ray)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _ray
}

// InitFromVec3 initializes the given #graphene_ray_t using the given vectors.
//
// The function takes the following parameters:
//
//   - origin (optional): #graphene_vec3_t.
//   - direction (optional): #graphene_vec3_t.
//
// The function returns the following values:
//
//   - ray: initialized ray.
func (r *Ray) InitFromVec3(origin *Vec3, direction *Vec3) *Ray {
	var _arg0 *C.graphene_ray_t  // out
	var _arg1 *C.graphene_vec3_t // out
	var _arg2 *C.graphene_vec3_t // out
	var _cret *C.graphene_ray_t  // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))
	if origin != nil {
		_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(origin)))
	}
	if direction != nil {
		_arg2 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(direction)))
	}

	_cret = C.graphene_ray_init_from_vec3(_arg0, _arg1, _arg2)
	runtime.KeepAlive(r)
	runtime.KeepAlive(origin)
	runtime.KeepAlive(direction)

	var _ray *Ray // out

	_ray = (*Ray)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _ray
}

// IntersectBox intersects the given #graphene_ray_t r with the given
// #graphene_box_t b.
//
// The function takes the following parameters:
//
//   - b: #graphene_box_t.
//
// The function returns the following values:
//
//   - tOut: distance of the point on the ray that intersects the box.
//   - rayIntersectionKind: type of intersection.
func (r *Ray) IntersectBox(b *Box) (float32, RayIntersectionKind) {
	var _arg0 *C.graphene_ray_t                  // out
	var _arg1 *C.graphene_box_t                  // out
	var _arg2 C.float                            // in
	var _cret C.graphene_ray_intersection_kind_t // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_ray_intersect_box(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(r)
	runtime.KeepAlive(b)

	var _tOut float32                            // out
	var _rayIntersectionKind RayIntersectionKind // out

	_tOut = float32(_arg2)
	_rayIntersectionKind = RayIntersectionKind(_cret)

	return _tOut, _rayIntersectionKind
}

// IntersectSphere intersects the given #graphene_ray_t r with the given
// #graphene_sphere_t s.
//
// The function takes the following parameters:
//
//   - s: #graphene_sphere_t.
//
// The function returns the following values:
//
//   - tOut: distance of the point on the ray that intersects the sphere.
//   - rayIntersectionKind: type of intersection.
func (r *Ray) IntersectSphere(s *Sphere) (float32, RayIntersectionKind) {
	var _arg0 *C.graphene_ray_t                  // out
	var _arg1 *C.graphene_sphere_t               // out
	var _arg2 C.float                            // in
	var _cret C.graphene_ray_intersection_kind_t // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(s)))

	_cret = C.graphene_ray_intersect_sphere(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(r)
	runtime.KeepAlive(s)

	var _tOut float32                            // out
	var _rayIntersectionKind RayIntersectionKind // out

	_tOut = float32(_arg2)
	_rayIntersectionKind = RayIntersectionKind(_cret)

	return _tOut, _rayIntersectionKind
}

// IntersectTriangle intersects the given #graphene_ray_t r with the given
// #graphene_triangle_t t.
//
// The function takes the following parameters:
//
//   - t: #graphene_triangle_t.
//
// The function returns the following values:
//
//   - tOut: distance of the point on the ray that intersects the triangle.
//   - rayIntersectionKind: type of intersection.
func (r *Ray) IntersectTriangle(t *Triangle) (float32, RayIntersectionKind) {
	var _arg0 *C.graphene_ray_t                  // out
	var _arg1 *C.graphene_triangle_t             // out
	var _arg2 C.float                            // in
	var _cret C.graphene_ray_intersection_kind_t // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(t)))

	_cret = C.graphene_ray_intersect_triangle(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(r)
	runtime.KeepAlive(t)

	var _tOut float32                            // out
	var _rayIntersectionKind RayIntersectionKind // out

	_tOut = float32(_arg2)
	_rayIntersectionKind = RayIntersectionKind(_cret)

	return _tOut, _rayIntersectionKind
}

// IntersectsBox checks whether the given #graphene_ray_t r intersects the given
// #graphene_box_t b.
//
// See also: graphene_ray_intersect_box().
//
// The function takes the following parameters:
//
//   - b: #graphene_box_t.
//
// The function returns the following values:
//
//   - ok: true if the ray intersects the box.
func (r *Ray) IntersectsBox(b *Box) bool {
	var _arg0 *C.graphene_ray_t // out
	var _arg1 *C.graphene_box_t // out
	var _cret C._Bool           // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = (*C.graphene_box_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_ray_intersects_box(_arg0, _arg1)
	runtime.KeepAlive(r)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// IntersectsSphere checks if the given #graphene_ray_t r intersects the given
// #graphene_sphere_t s.
//
// See also: graphene_ray_intersect_sphere().
//
// The function takes the following parameters:
//
//   - s: #graphene_sphere_t.
//
// The function returns the following values:
//
//   - ok: true if the ray intersects the sphere.
func (r *Ray) IntersectsSphere(s *Sphere) bool {
	var _arg0 *C.graphene_ray_t    // out
	var _arg1 *C.graphene_sphere_t // out
	var _cret C._Bool              // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(s)))

	_cret = C.graphene_ray_intersects_sphere(_arg0, _arg1)
	runtime.KeepAlive(r)
	runtime.KeepAlive(s)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// IntersectsTriangle checks whether the given #graphene_ray_t r intersects the
// given #graphene_triangle_t b.
//
// See also: graphene_ray_intersect_triangle().
//
// The function takes the following parameters:
//
//   - t: #graphene_triangle_t.
//
// The function returns the following values:
//
//   - ok: true if the ray intersects the triangle.
func (r *Ray) IntersectsTriangle(t *Triangle) bool {
	var _arg0 *C.graphene_ray_t      // out
	var _arg1 *C.graphene_triangle_t // out
	var _cret C._Bool                // in

	_arg0 = (*C.graphene_ray_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(t)))

	_cret = C.graphene_ray_intersects_triangle(_arg0, _arg1)
	runtime.KeepAlive(r)
	runtime.KeepAlive(t)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Rect: location and size of a rectangle region.
//
// The width and height of a #graphene_rect_t can be negative; for instance,
// a #graphene_rect_t with an origin of [ 0, 0 ] and a size of [ 10, 10 ] is
// equivalent to a #graphene_rect_t with an origin of [ 10, 10 ] and a size of [
// -10, -10 ].
//
// Application code can normalize rectangles using graphene_rect_normalize();
// this function will ensure that the width and height of a rectangle are
// positive values. All functions taking a #graphene_rect_t as an argument
// will internally operate on a normalized copy; all functions returning a
// #graphene_rect_t will always return a normalized rectangle.
//
// An instance of this type is always passed by reference.
type Rect struct {
	*rect
}

// rect is the struct that's finalized.
type rect struct {
	native *C.graphene_rect_t
}

func marshalRect(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Rect{&rect{(*C.graphene_rect_t)(b)}}, nil
}

// Origin coordinates of the origin of the rectangle.
func (r *Rect) Origin() *Point {
	valptr := &r.native.origin
	var _v *Point // out
	_v = (*Point)(gextras.NewStructNative(unsafe.Pointer(valptr)))
	return _v
}

// Size: size of the rectangle.
func (r *Rect) Size() *Size {
	valptr := &r.native.size
	var _v *Size // out
	_v = (*Size)(gextras.NewStructNative(unsafe.Pointer(valptr)))
	return _v
}

// ContainsPoint checks whether a #graphene_rect_t contains the given
// coordinates.
//
// The function takes the following parameters:
//
//   - p: #graphene_point_t.
//
// The function returns the following values:
//
//   - ok: true if the rectangle contains the point.
func (r *Rect) ContainsPoint(p *Point) bool {
	var _arg0 *C.graphene_rect_t  // out
	var _arg1 *C.graphene_point_t // out
	var _cret C._Bool             // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(p)))

	_cret = C.graphene_rect_contains_point(_arg0, _arg1)
	runtime.KeepAlive(r)
	runtime.KeepAlive(p)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// ContainsRect checks whether a #graphene_rect_t fully contains the given
// rectangle.
//
// The function takes the following parameters:
//
//   - b: #graphene_rect_t.
//
// The function returns the following values:
//
//   - ok: true if the rectangle a fully contains b.
func (a *Rect) ContainsRect(b *Rect) bool {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 *C.graphene_rect_t // out
	var _cret C._Bool            // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_rect_contains_rect(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Equal checks whether the two given rectangle are equal.
//
// The function takes the following parameters:
//
//   - b: #graphene_rect_t.
//
// The function returns the following values:
//
//   - ok: true if the rectangles are equal.
func (a *Rect) Equal(b *Rect) bool {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 *C.graphene_rect_t // out
	var _cret C._Bool            // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_rect_equal(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Expand expands a #graphene_rect_t to contain the given #graphene_point_t.
//
// The function takes the following parameters:
//
//   - p: #graphene_point_t.
//
// The function returns the following values:
//
//   - res: return location for the expanded rectangle.
func (r *Rect) Expand(p *Point) *Rect {
	var _arg0 *C.graphene_rect_t  // out
	var _arg1 *C.graphene_point_t // out
	var _arg2 C.graphene_rect_t   // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = (*C.graphene_point_t)(gextras.StructNative(unsafe.Pointer(p)))

	C.graphene_rect_expand(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(r)
	runtime.KeepAlive(p)

	var _res *Rect // out

	_res = (*Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Area: compute the area of given normalized rectangle.
//
// The function returns the following values:
//
//   - gfloat: area of the normalized rectangle.
func (r *Rect) Area() float32 {
	var _arg0 *C.graphene_rect_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	_cret = C.graphene_rect_get_area(_arg0)
	runtime.KeepAlive(r)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// BottomLeft retrieves the coordinates of the bottom-left corner of the given
// rectangle.
//
// The function returns the following values:
//
//   - p: return location for a #graphene_point_t.
func (r *Rect) BottomLeft() *Point {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 C.graphene_point_t // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_rect_get_bottom_left(_arg0, &_arg1)
	runtime.KeepAlive(r)

	var _p *Point // out

	_p = (*Point)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _p
}

// BottomRight retrieves the coordinates of the bottom-right corner of the given
// rectangle.
//
// The function returns the following values:
//
//   - p: return location for a #graphene_point_t.
func (r *Rect) BottomRight() *Point {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 C.graphene_point_t // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_rect_get_bottom_right(_arg0, &_arg1)
	runtime.KeepAlive(r)

	var _p *Point // out

	_p = (*Point)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _p
}

// Center retrieves the coordinates of the center of the given rectangle.
//
// The function returns the following values:
//
//   - p: return location for a #graphene_point_t.
func (r *Rect) Center() *Point {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 C.graphene_point_t // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_rect_get_center(_arg0, &_arg1)
	runtime.KeepAlive(r)

	var _p *Point // out

	_p = (*Point)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _p
}

// Height retrieves the normalized height of the given rectangle.
//
// The function returns the following values:
//
//   - gfloat: normalized height of the rectangle.
func (r *Rect) Height() float32 {
	var _arg0 *C.graphene_rect_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	_cret = C.graphene_rect_get_height(_arg0)
	runtime.KeepAlive(r)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// TopLeft retrieves the coordinates of the top-left corner of the given
// rectangle.
//
// The function returns the following values:
//
//   - p: return location for a #graphene_point_t.
func (r *Rect) TopLeft() *Point {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 C.graphene_point_t // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_rect_get_top_left(_arg0, &_arg1)
	runtime.KeepAlive(r)

	var _p *Point // out

	_p = (*Point)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _p
}

// TopRight retrieves the coordinates of the top-right corner of the given
// rectangle.
//
// The function returns the following values:
//
//   - p: return location for a #graphene_point_t.
func (r *Rect) TopRight() *Point {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 C.graphene_point_t // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_rect_get_top_right(_arg0, &_arg1)
	runtime.KeepAlive(r)

	var _p *Point // out

	_p = (*Point)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _p
}

// Vertices computes the four vertices of a #graphene_rect_t.
//
// The function returns the following values:
//
//   - vertices: return location for an array of 4 #graphene_vec2_t.
func (r *Rect) Vertices() [4]Vec2 {
	var _arg0 *C.graphene_rect_t   // out
	var _arg1 [4]C.graphene_vec2_t // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_rect_get_vertices(_arg0, &_arg1[0])
	runtime.KeepAlive(r)

	var _vertices [4]Vec2 // out

	{
		src := &_arg1
		for i := 0; i < 4; i++ {
			_vertices[i] = *(*Vec2)(gextras.NewStructNative(unsafe.Pointer((&src[i]))))
		}
	}

	return _vertices
}

// Width retrieves the normalized width of the given rectangle.
//
// The function returns the following values:
//
//   - gfloat: normalized width of the rectangle.
func (r *Rect) Width() float32 {
	var _arg0 *C.graphene_rect_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	_cret = C.graphene_rect_get_width(_arg0)
	runtime.KeepAlive(r)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// X retrieves the normalized X coordinate of the origin of the given rectangle.
//
// The function returns the following values:
//
//   - gfloat: normalized X coordinate of the rectangle.
func (r *Rect) X() float32 {
	var _arg0 *C.graphene_rect_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	_cret = C.graphene_rect_get_x(_arg0)
	runtime.KeepAlive(r)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Y retrieves the normalized Y coordinate of the origin of the given rectangle.
//
// The function returns the following values:
//
//   - gfloat: normalized Y coordinate of the rectangle.
func (r *Rect) Y() float32 {
	var _arg0 *C.graphene_rect_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	_cret = C.graphene_rect_get_y(_arg0)
	runtime.KeepAlive(r)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Init initializes the given #graphene_rect_t with the given values.
//
// This function will implicitly normalize the #graphene_rect_t before
// returning.
//
// The function takes the following parameters:
//
//   - x: x coordinate of the graphene_rect_t.origin.
//   - y: y coordinate of the graphene_rect_t.origin.
//   - width of the graphene_rect_t.size.
//   - height of the graphene_rect_t.size.
//
// The function returns the following values:
//
//   - rect: initialized rectangle.
func (r *Rect) Init(x float32, y float32, width float32, height float32) *Rect {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 C.float            // out
	var _arg2 C.float            // out
	var _arg3 C.float            // out
	var _arg4 C.float            // out
	var _cret *C.graphene_rect_t // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = C.float(x)
	_arg2 = C.float(y)
	_arg3 = C.float(width)
	_arg4 = C.float(height)

	_cret = C.graphene_rect_init(_arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(r)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)

	var _rect *Rect // out

	_rect = (*Rect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _rect
}

// InitFromRect initializes r using the given src rectangle.
//
// This function will implicitly normalize the #graphene_rect_t before
// returning.
//
// The function takes the following parameters:
//
//   - src: #graphene_rect_t.
//
// The function returns the following values:
//
//   - rect: initialized rectangle.
func (r *Rect) InitFromRect(src *Rect) *Rect {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 *C.graphene_rect_t // out
	var _cret *C.graphene_rect_t // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_rect_init_from_rect(_arg0, _arg1)
	runtime.KeepAlive(r)
	runtime.KeepAlive(src)

	var _rect *Rect // out

	_rect = (*Rect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _rect
}

// Inset changes the given rectangle to be smaller, or larger depending on the
// given inset parameters.
//
// To create an inset rectangle, use positive d_x or d_y values; to create a
// larger, encompassing rectangle, use negative d_x or d_y values.
//
// The origin of the rectangle is offset by d_x and d_y, while the size
// is adjusted by (2 * d_x, 2 * d_y). If d_x and d_y are positive values,
// the size of the rectangle is decreased; if d_x and d_y are negative values,
// the size of the rectangle is increased.
//
// If the size of the resulting inset rectangle has a negative width or height
// then the size will be set to zero.
//
// The function takes the following parameters:
//
//   - dX: horizontal inset.
//   - dY: vertical inset.
//
// The function returns the following values:
//
//   - rect: inset rectangle.
func (r *Rect) Inset(dX float32, dY float32) *Rect {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 C.float            // out
	var _arg2 C.float            // out
	var _cret *C.graphene_rect_t // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = C.float(dX)
	_arg2 = C.float(dY)

	_cret = C.graphene_rect_inset(_arg0, _arg1, _arg2)
	runtime.KeepAlive(r)
	runtime.KeepAlive(dX)
	runtime.KeepAlive(dY)

	var _rect *Rect // out

	_rect = (*Rect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _rect
}

// InsetR changes the given rectangle to be smaller, or larger depending on the
// given inset parameters.
//
// To create an inset rectangle, use positive d_x or d_y values; to create a
// larger, encompassing rectangle, use negative d_x or d_y values.
//
// The origin of the rectangle is offset by d_x and d_y, while the size
// is adjusted by (2 * d_x, 2 * d_y). If d_x and d_y are positive values,
// the size of the rectangle is decreased; if d_x and d_y are negative values,
// the size of the rectangle is increased.
//
// If the size of the resulting inset rectangle has a negative width or height
// then the size will be set to zero.
//
// The function takes the following parameters:
//
//   - dX: horizontal inset.
//   - dY: vertical inset.
//
// The function returns the following values:
//
//   - res: return location for the inset rectangle.
func (r *Rect) InsetR(dX float32, dY float32) *Rect {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 C.float            // out
	var _arg2 C.float            // out
	var _arg3 C.graphene_rect_t  // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = C.float(dX)
	_arg2 = C.float(dY)

	C.graphene_rect_inset_r(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(r)
	runtime.KeepAlive(dX)
	runtime.KeepAlive(dY)

	var _res *Rect // out

	_res = (*Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _res
}

// Interpolate: linearly interpolates the origin and size of the two given
// rectangles.
//
// The function takes the following parameters:
//
//   - b: #graphene_rect_t.
//   - factor: linear interpolation factor.
//
// The function returns the following values:
//
//   - res: return location for the interpolated rectangle.
func (a *Rect) Interpolate(b *Rect, factor float64) *Rect {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 *C.graphene_rect_t // out
	var _arg2 C.double           // out
	var _arg3 C.graphene_rect_t  // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(b)))
	_arg2 = C.double(factor)

	C.graphene_rect_interpolate(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)
	runtime.KeepAlive(factor)

	var _res *Rect // out

	_res = (*Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _res
}

// Intersection computes the intersection of the two given rectangles.
//
// ! (rectangle-intersection.png)
//
// The intersection in the image above is the blue outline.
//
// If the two rectangles do not intersect, res will contain a degenerate
// rectangle with origin in (0, 0) and a size of 0.
//
// The function takes the following parameters:
//
//   - b: #graphene_rect_t.
//
// The function returns the following values:
//
//   - res (optional): return location for a #graphene_rect_t.
//   - ok: true if the two rectangles intersect.
func (a *Rect) Intersection(b *Rect) (*Rect, bool) {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 *C.graphene_rect_t // out
	var _arg2 C.graphene_rect_t  // in
	var _cret C._Bool            // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_rect_intersection(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Rect // out
	var _ok bool   // out

	_res = (*Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))
	if _cret {
		_ok = true
	}

	return _res, _ok
}

// Normalize normalizes the passed rectangle.
//
// This function ensures that the size of the rectangle is made of positive
// values, and that the origin is the top-left corner of the rectangle.
//
// The function returns the following values:
//
//   - rect: normalized rectangle.
func (r *Rect) Normalize() *Rect {
	var _arg0 *C.graphene_rect_t // out
	var _cret *C.graphene_rect_t // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	_cret = C.graphene_rect_normalize(_arg0)
	runtime.KeepAlive(r)

	var _rect *Rect // out

	_rect = (*Rect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _rect
}

// NormalizeR normalizes the passed rectangle.
//
// This function ensures that the size of the rectangle is made of positive
// values, and that the origin is in the top-left corner of the rectangle.
//
// The function returns the following values:
//
//   - res: return location for the normalized rectangle.
func (r *Rect) NormalizeR() *Rect {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 C.graphene_rect_t  // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_rect_normalize_r(_arg0, &_arg1)
	runtime.KeepAlive(r)

	var _res *Rect // out

	_res = (*Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Offset offsets the origin by d_x and d_y.
//
// The size of the rectangle is unchanged.
//
// The function takes the following parameters:
//
//   - dX: horizontal offset.
//   - dY: vertical offset.
//
// The function returns the following values:
//
//   - rect: offset rectangle.
func (r *Rect) Offset(dX float32, dY float32) *Rect {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 C.float            // out
	var _arg2 C.float            // out
	var _cret *C.graphene_rect_t // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = C.float(dX)
	_arg2 = C.float(dY)

	_cret = C.graphene_rect_offset(_arg0, _arg1, _arg2)
	runtime.KeepAlive(r)
	runtime.KeepAlive(dX)
	runtime.KeepAlive(dY)

	var _rect *Rect // out

	_rect = (*Rect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _rect
}

// OffsetR offsets the origin of the given rectangle by d_x and d_y.
//
// The size of the rectangle is left unchanged.
//
// The function takes the following parameters:
//
//   - dX: horizontal offset.
//   - dY: vertical offset.
//
// The function returns the following values:
//
//   - res: return location for the offset rectangle.
func (r *Rect) OffsetR(dX float32, dY float32) *Rect {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 C.float            // out
	var _arg2 C.float            // out
	var _arg3 C.graphene_rect_t  // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = C.float(dX)
	_arg2 = C.float(dY)

	C.graphene_rect_offset_r(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(r)
	runtime.KeepAlive(dX)
	runtime.KeepAlive(dY)

	var _res *Rect // out

	_res = (*Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _res
}

// Round rounds the origin and size of the given rectangle to their nearest
// integer values; the rounding is guaranteed to be large enough to have an area
// bigger or equal to the original rectangle, but might not fully contain its
// extents. Use graphene_rect_round_extents() in case you need to round to a
// rectangle that covers fully the original one.
//
// This function is the equivalent of calling floor on the coordinates of the
// origin, and ceil on the size.
//
// Deprecated: Use graphene_rect_round_extents() instead.
//
// The function returns the following values:
//
//   - res: return location for the rounded rectangle.
func (r *Rect) Round() *Rect {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 C.graphene_rect_t  // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_rect_round(_arg0, &_arg1)
	runtime.KeepAlive(r)

	var _res *Rect // out

	_res = (*Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// RoundExtents rounds the origin of the given rectangle to its nearest integer
// value and and recompute the size so that the rectangle is large enough to
// contain all the conrners of the original rectangle.
//
// This function is the equivalent of calling floor on the coordinates of
// the origin, and recomputing the size calling ceil on the bottom-right
// coordinates.
//
// If you want to be sure that the rounded rectangle completely covers the
// area that was covered by the original rectangle — i.e. you want to cover
// the area including all its corners — this function will make sure that the
// size is recomputed taking into account the ceiling of the coordinates of
// the bottom-right corner. If the difference between the original coordinates
// and the coordinates of the rounded rectangle is greater than the difference
// between the original size and and the rounded size, then the move of the
// origin would not be compensated by a move in the anti-origin, leaving the
// corners of the original rectangle outside the rounded one.
//
// The function returns the following values:
//
//   - res: return location for the rectangle with rounded extents.
func (r *Rect) RoundExtents() *Rect {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 C.graphene_rect_t  // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	C.graphene_rect_round_extents(_arg0, &_arg1)
	runtime.KeepAlive(r)

	var _res *Rect // out

	_res = (*Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// RoundToPixel rounds the origin and the size of the given rectangle to their
// nearest integer values; the rounding is guaranteed to be large enough to
// contain the original rectangle.
//
// Deprecated: Use graphene_rect_round() instead.
//
// The function returns the following values:
//
//   - rect: pixel-aligned rectangle.
func (r *Rect) RoundToPixel() *Rect {
	var _arg0 *C.graphene_rect_t // out
	var _cret *C.graphene_rect_t // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))

	_cret = C.graphene_rect_round_to_pixel(_arg0)
	runtime.KeepAlive(r)

	var _rect *Rect // out

	_rect = (*Rect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _rect
}

// Scale scales the size and origin of a rectangle horizontaly by s_h, and
// vertically by s_v. The result res is normalized.
//
// The function takes the following parameters:
//
//   - sH: horizontal scale factor.
//   - sV: vertical scale factor.
//
// The function returns the following values:
//
//   - res: return location for the scaled rectangle.
func (r *Rect) Scale(sH float32, sV float32) *Rect {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 C.float            // out
	var _arg2 C.float            // out
	var _arg3 C.graphene_rect_t  // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(r)))
	_arg1 = C.float(sH)
	_arg2 = C.float(sV)

	C.graphene_rect_scale(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(r)
	runtime.KeepAlive(sH)
	runtime.KeepAlive(sV)

	var _res *Rect // out

	_res = (*Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _res
}

// Union computes the union of the two given rectangles.
//
// ! (rectangle-union.png)
//
// The union in the image above is the blue outline.
//
// The function takes the following parameters:
//
//   - b: #graphene_rect_t.
//
// The function returns the following values:
//
//   - res: return location for a #graphene_rect_t.
func (a *Rect) Union(b *Rect) *Rect {
	var _arg0 *C.graphene_rect_t // out
	var _arg1 *C.graphene_rect_t // out
	var _arg2 C.graphene_rect_t  // in

	_arg0 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_rect_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_rect_union(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Rect // out

	_res = (*Rect)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// RectAlloc allocates a new #graphene_rect_t.
//
// The contents of the returned rectangle are undefined.
//
// The function returns the following values:
//
//   - rect: newly allocated rectangle.
func RectAlloc() *Rect {
	var _cret *C.graphene_rect_t // in

	_cret = C.graphene_rect_alloc()

	var _rect *Rect // out

	_rect = (*Rect)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_rect)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_rect_free((*C.graphene_rect_t)(intern.C))
		},
	)

	return _rect
}

// RectZero returns a degenerate rectangle with origin fixed at (0, 0) and a
// size of 0, 0.
//
// The function returns the following values:
//
//   - rect: fixed rectangle.
func RectZero() *Rect {
	var _cret *C.graphene_rect_t // in

	_cret = C.graphene_rect_zero()

	var _rect *Rect // out

	_rect = (*Rect)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _rect
}

// SIMD4F: instance of this type is always passed by reference.
type SIMD4F struct {
	*simD4F
}

// simD4F is the struct that's finalized.
type simD4F struct {
	native *C.graphene_simd4f_t
}

// SIMD4X4F: instance of this type is always passed by reference.
type SIMD4X4F struct {
	*simD4X4F
}

// simD4X4F is the struct that's finalized.
type simD4X4F struct {
	native *C.graphene_simd4x4f_t
}

// Size: size.
//
// An instance of this type is always passed by reference.
type Size struct {
	*size
}

// size is the struct that's finalized.
type size struct {
	native *C.graphene_size_t
}

func marshalSize(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Size{&size{(*C.graphene_size_t)(b)}}, nil
}

// NewSizeAlloc constructs a struct Size.
func NewSizeAlloc() *Size {
	var _cret *C.graphene_size_t // in

	_cret = C.graphene_size_alloc()

	var _size *Size // out

	_size = (*Size)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_size)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_size_free((*C.graphene_size_t)(intern.C))
		},
	)

	return _size
}

// Width: width.
func (s *Size) Width() float32 {
	valptr := &s.native.width
	var _v float32 // out
	_v = float32(*valptr)
	return _v
}

// Height: height.
func (s *Size) Height() float32 {
	valptr := &s.native.height
	var _v float32 // out
	_v = float32(*valptr)
	return _v
}

// Width: width.
func (s *Size) SetWidth(width float32) {
	valptr := &s.native.width
	*valptr = C.float(width)
}

// Height: height.
func (s *Size) SetHeight(height float32) {
	valptr := &s.native.height
	*valptr = C.float(height)
}

// Equal checks whether the two give #graphene_size_t are equal.
//
// The function takes the following parameters:
//
//   - b: #graphene_size_t.
//
// The function returns the following values:
//
//   - ok: true if the sizes are equal.
func (a *Size) Equal(b *Size) bool {
	var _arg0 *C.graphene_size_t // out
	var _arg1 *C.graphene_size_t // out
	var _cret C._Bool            // in

	_arg0 = (*C.graphene_size_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_size_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_size_equal(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Init initializes a #graphene_size_t using the given width and height.
//
// The function takes the following parameters:
//
//   - width: width.
//   - height: height.
//
// The function returns the following values:
//
//   - size: initialized #graphene_size_t.
func (s *Size) Init(width float32, height float32) *Size {
	var _arg0 *C.graphene_size_t // out
	var _arg1 C.float            // out
	var _arg2 C.float            // out
	var _cret *C.graphene_size_t // in

	_arg0 = (*C.graphene_size_t)(gextras.StructNative(unsafe.Pointer(s)))
	_arg1 = C.float(width)
	_arg2 = C.float(height)

	_cret = C.graphene_size_init(_arg0, _arg1, _arg2)
	runtime.KeepAlive(s)
	runtime.KeepAlive(width)
	runtime.KeepAlive(height)

	var _size *Size // out

	_size = (*Size)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _size
}

// InitFromSize initializes a #graphene_size_t using the width and height of the
// given src.
//
// The function takes the following parameters:
//
//   - src: #graphene_size_t.
//
// The function returns the following values:
//
//   - size: initialized #graphene_size_t.
func (s *Size) InitFromSize(src *Size) *Size {
	var _arg0 *C.graphene_size_t // out
	var _arg1 *C.graphene_size_t // out
	var _cret *C.graphene_size_t // in

	_arg0 = (*C.graphene_size_t)(gextras.StructNative(unsafe.Pointer(s)))
	_arg1 = (*C.graphene_size_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_size_init_from_size(_arg0, _arg1)
	runtime.KeepAlive(s)
	runtime.KeepAlive(src)

	var _size *Size // out

	_size = (*Size)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _size
}

// Interpolate: linearly interpolates the two given #graphene_size_t using the
// given interpolation factor.
//
// The function takes the following parameters:
//
//   - b: #graphene_size_t.
//   - factor: linear interpolation factor.
//
// The function returns the following values:
//
//   - res: return location for the interpolated size.
func (a *Size) Interpolate(b *Size, factor float64) *Size {
	var _arg0 *C.graphene_size_t // out
	var _arg1 *C.graphene_size_t // out
	var _arg2 C.double           // out
	var _arg3 C.graphene_size_t  // in

	_arg0 = (*C.graphene_size_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_size_t)(gextras.StructNative(unsafe.Pointer(b)))
	_arg2 = C.double(factor)

	C.graphene_size_interpolate(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)
	runtime.KeepAlive(factor)

	var _res *Size // out

	_res = (*Size)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _res
}

// Scale scales the components of a #graphene_size_t using the given factor.
//
// The function takes the following parameters:
//
//   - factor: scaling factor.
//
// The function returns the following values:
//
//   - res: return location for the scaled size.
func (s *Size) Scale(factor float32) *Size {
	var _arg0 *C.graphene_size_t // out
	var _arg1 C.float            // out
	var _arg2 C.graphene_size_t  // in

	_arg0 = (*C.graphene_size_t)(gextras.StructNative(unsafe.Pointer(s)))
	_arg1 = C.float(factor)

	C.graphene_size_scale(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(s)
	runtime.KeepAlive(factor)

	var _res *Size // out

	_res = (*Size)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// SizeZero: constant pointer to a zero #graphene_size_t, useful for equality
// checks and interpolations.
//
// The function returns the following values:
//
//   - size: constant size.
func SizeZero() *Size {
	var _cret *C.graphene_size_t // in

	_cret = C.graphene_size_zero()

	var _size *Size // out

	_size = (*Size)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _size
}

// Sphere: sphere, represented by its center and radius.
//
// An instance of this type is always passed by reference.
type Sphere struct {
	*sphere
}

// sphere is the struct that's finalized.
type sphere struct {
	native *C.graphene_sphere_t
}

func marshalSphere(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Sphere{&sphere{(*C.graphene_sphere_t)(b)}}, nil
}

// NewSphereAlloc constructs a struct Sphere.
func NewSphereAlloc() *Sphere {
	var _cret *C.graphene_sphere_t // in

	_cret = C.graphene_sphere_alloc()

	var _sphere *Sphere // out

	_sphere = (*Sphere)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_sphere)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_sphere_free((*C.graphene_sphere_t)(intern.C))
		},
	)

	return _sphere
}

// ContainsPoint checks whether the given point is contained in the volume of a
// #graphene_sphere_t.
//
// The function takes the following parameters:
//
//   - point: #graphene_point3d_t.
//
// The function returns the following values:
//
//   - ok: true if the sphere contains the point.
func (s *Sphere) ContainsPoint(point *Point3D) bool {
	var _arg0 *C.graphene_sphere_t  // out
	var _arg1 *C.graphene_point3d_t // out
	var _cret C._Bool               // in

	_arg0 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(s)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(point)))

	_cret = C.graphene_sphere_contains_point(_arg0, _arg1)
	runtime.KeepAlive(s)
	runtime.KeepAlive(point)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Distance computes the distance of the given point from the surface of a
// #graphene_sphere_t.
//
// The function takes the following parameters:
//
//   - point: #graphene_point3d_t.
//
// The function returns the following values:
//
//   - gfloat: distance of the point.
func (s *Sphere) Distance(point *Point3D) float32 {
	var _arg0 *C.graphene_sphere_t  // out
	var _arg1 *C.graphene_point3d_t // out
	var _cret C.float               // in

	_arg0 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(s)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(point)))

	_cret = C.graphene_sphere_distance(_arg0, _arg1)
	runtime.KeepAlive(s)
	runtime.KeepAlive(point)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Equal checks whether two #graphene_sphere_t are equal.
//
// The function takes the following parameters:
//
//   - b: #graphene_sphere_t.
//
// The function returns the following values:
//
//   - ok: true if the spheres are equal.
func (a *Sphere) Equal(b *Sphere) bool {
	var _arg0 *C.graphene_sphere_t // out
	var _arg1 *C.graphene_sphere_t // out
	var _cret C._Bool              // in

	_arg0 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_sphere_equal(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// BoundingBox computes the bounding box capable of containing the given
// #graphene_sphere_t.
//
// The function returns the following values:
//
//   - box: return location for the bounding box.
func (s *Sphere) BoundingBox() *Box {
	var _arg0 *C.graphene_sphere_t // out
	var _arg1 C.graphene_box_t     // in

	_arg0 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(s)))

	C.graphene_sphere_get_bounding_box(_arg0, &_arg1)
	runtime.KeepAlive(s)

	var _box *Box // out

	_box = (*Box)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _box
}

// Center retrieves the coordinates of the center of a #graphene_sphere_t.
//
// The function returns the following values:
//
//   - center: return location for the coordinates of the center.
func (s *Sphere) Center() *Point3D {
	var _arg0 *C.graphene_sphere_t // out
	var _arg1 C.graphene_point3d_t // in

	_arg0 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(s)))

	C.graphene_sphere_get_center(_arg0, &_arg1)
	runtime.KeepAlive(s)

	var _center *Point3D // out

	_center = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _center
}

// Radius retrieves the radius of a #graphene_sphere_t.
func (s *Sphere) Radius() float32 {
	var _arg0 *C.graphene_sphere_t // out
	var _cret C.float              // in

	_arg0 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(s)))

	_cret = C.graphene_sphere_get_radius(_arg0)
	runtime.KeepAlive(s)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Init initializes the given #graphene_sphere_t with the given center and
// radius.
//
// The function takes the following parameters:
//
//   - center (optional) coordinates of the center of the sphere, or NULL for a
//     center in (0, 0, 0).
//   - radius of the sphere.
//
// The function returns the following values:
//
//   - sphere: initialized #graphene_sphere_t.
func (s *Sphere) Init(center *Point3D, radius float32) *Sphere {
	var _arg0 *C.graphene_sphere_t  // out
	var _arg1 *C.graphene_point3d_t // out
	var _arg2 C.float               // out
	var _cret *C.graphene_sphere_t  // in

	_arg0 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(s)))
	if center != nil {
		_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(center)))
	}
	_arg2 = C.float(radius)

	_cret = C.graphene_sphere_init(_arg0, _arg1, _arg2)
	runtime.KeepAlive(s)
	runtime.KeepAlive(center)
	runtime.KeepAlive(radius)

	var _sphere *Sphere // out

	_sphere = (*Sphere)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _sphere
}

// InitFromPoints initializes the given #graphene_sphere_t using the given array
// of 3D coordinates so that the sphere includes them.
//
// The center of the sphere can either be specified, or will be center of the 3D
// volume that encompasses all points.
//
// The function takes the following parameters:
//
//   - points: array of #graphene_point3d_t.
//   - center (optional) of the sphere.
//
// The function returns the following values:
//
//   - sphere: initialized #graphene_sphere_t.
func (s *Sphere) InitFromPoints(points []Point3D, center *Point3D) *Sphere {
	var _arg0 *C.graphene_sphere_t  // out
	var _arg2 *C.graphene_point3d_t // out
	var _arg1 C.uint
	var _arg3 *C.graphene_point3d_t // out
	var _cret *C.graphene_sphere_t  // in

	_arg0 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(s)))
	_arg1 = (C.uint)(len(points))
	_arg2 = (*C.graphene_point3d_t)(C.calloc(C.size_t(len(points)), C.size_t(C.sizeof_graphene_point3d_t)))
	defer C.free(unsafe.Pointer(_arg2))
	{
		out := unsafe.Slice((*C.graphene_point3d_t)(_arg2), len(points))
		for i := range points {
			out[i] = *(*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer((&points[i]))))
		}
	}
	if center != nil {
		_arg3 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(center)))
	}

	_cret = C.graphene_sphere_init_from_points(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(s)
	runtime.KeepAlive(points)
	runtime.KeepAlive(center)

	var _sphere *Sphere // out

	_sphere = (*Sphere)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _sphere
}

// InitFromVectors initializes the given #graphene_sphere_t using the given
// array of 3D coordinates so that the sphere includes them.
//
// The center of the sphere can either be specified, or will be center of the 3D
// volume that encompasses all vectors.
//
// The function takes the following parameters:
//
//   - vectors: array of #graphene_vec3_t.
//   - center (optional) of the sphere.
//
// The function returns the following values:
//
//   - sphere: initialized #graphene_sphere_t.
func (s *Sphere) InitFromVectors(vectors []Vec3, center *Point3D) *Sphere {
	var _arg0 *C.graphene_sphere_t // out
	var _arg2 *C.graphene_vec3_t   // out
	var _arg1 C.uint
	var _arg3 *C.graphene_point3d_t // out
	var _cret *C.graphene_sphere_t  // in

	_arg0 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(s)))
	_arg1 = (C.uint)(len(vectors))
	_arg2 = (*C.graphene_vec3_t)(C.calloc(C.size_t(len(vectors)), C.size_t(C.sizeof_graphene_vec3_t)))
	defer C.free(unsafe.Pointer(_arg2))
	{
		out := unsafe.Slice((*C.graphene_vec3_t)(_arg2), len(vectors))
		for i := range vectors {
			out[i] = *(*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer((&vectors[i]))))
		}
	}
	if center != nil {
		_arg3 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(center)))
	}

	_cret = C.graphene_sphere_init_from_vectors(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(s)
	runtime.KeepAlive(vectors)
	runtime.KeepAlive(center)

	var _sphere *Sphere // out

	_sphere = (*Sphere)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _sphere
}

// IsEmpty checks whether the sphere has a zero radius.
//
// The function returns the following values:
//
//   - ok: true if the sphere is empty.
func (s *Sphere) IsEmpty() bool {
	var _arg0 *C.graphene_sphere_t // out
	var _cret C._Bool              // in

	_arg0 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(s)))

	_cret = C.graphene_sphere_is_empty(_arg0)
	runtime.KeepAlive(s)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Translate translates the center of the given #graphene_sphere_t using the
// point coordinates as the delta of the translation.
//
// The function takes the following parameters:
//
//   - point coordinates of the translation.
//
// The function returns the following values:
//
//   - res: return location for the translated sphere.
func (s *Sphere) Translate(point *Point3D) *Sphere {
	var _arg0 *C.graphene_sphere_t  // out
	var _arg1 *C.graphene_point3d_t // out
	var _arg2 C.graphene_sphere_t   // in

	_arg0 = (*C.graphene_sphere_t)(gextras.StructNative(unsafe.Pointer(s)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(point)))

	C.graphene_sphere_translate(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(s)
	runtime.KeepAlive(point)

	var _res *Sphere // out

	_res = (*Sphere)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Triangle: triangle.
//
// An instance of this type is always passed by reference.
type Triangle struct {
	*triangle
}

// triangle is the struct that's finalized.
type triangle struct {
	native *C.graphene_triangle_t
}

func marshalTriangle(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Triangle{&triangle{(*C.graphene_triangle_t)(b)}}, nil
}

// NewTriangleAlloc constructs a struct Triangle.
func NewTriangleAlloc() *Triangle {
	var _cret *C.graphene_triangle_t // in

	_cret = C.graphene_triangle_alloc()

	var _triangle *Triangle // out

	_triangle = (*Triangle)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_triangle)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_triangle_free((*C.graphene_triangle_t)(intern.C))
		},
	)

	return _triangle
}

// ContainsPoint checks whether the given triangle t contains the point p.
//
// The function takes the following parameters:
//
//   - p: #graphene_point3d_t.
//
// The function returns the following values:
//
//   - ok: true if the point is inside the triangle.
func (t *Triangle) ContainsPoint(p *Point3D) bool {
	var _arg0 *C.graphene_triangle_t // out
	var _arg1 *C.graphene_point3d_t  // out
	var _cret C._Bool                // in

	_arg0 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(t)))
	_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(p)))

	_cret = C.graphene_triangle_contains_point(_arg0, _arg1)
	runtime.KeepAlive(t)
	runtime.KeepAlive(p)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Equal checks whether the two given #graphene_triangle_t are equal.
//
// The function takes the following parameters:
//
//   - b: #graphene_triangle_t.
//
// The function returns the following values:
//
//   - ok: true if the triangles are equal.
func (a *Triangle) Equal(b *Triangle) bool {
	var _arg0 *C.graphene_triangle_t // out
	var _arg1 *C.graphene_triangle_t // out
	var _cret C._Bool                // in

	_arg0 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_triangle_equal(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Area computes the area of the given #graphene_triangle_t.
//
// The function returns the following values:
//
//   - gfloat: area of the triangle.
func (t *Triangle) Area() float32 {
	var _arg0 *C.graphene_triangle_t // out
	var _cret C.float                // in

	_arg0 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(t)))

	_cret = C.graphene_triangle_get_area(_arg0)
	runtime.KeepAlive(t)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Barycoords computes the barycentric coordinates
// (http://en.wikipedia.org/wiki/Barycentric_coordinate_system) of the given
// point p.
//
// The point p must lie on the same plane as the triangle t; if the point is not
// coplanar, the result of this function is undefined.
//
// If we place the origin in the coordinates of the triangle's A point,
// the barycentric coordinates are u, which is on the AC vector; and v which is
// on the AB vector:
//
// ! (triangle-barycentric.png)
//
// The returned #graphene_vec2_t contains the following values, in order:
//
//   - res.x = u
//   - res.y = v.
//
// The function takes the following parameters:
//
//   - p (optional): #graphene_point3d_t.
//
// The function returns the following values:
//
//   - res: return location for the vector with the barycentric coordinates.
//   - ok: true if the barycentric coordinates are valid.
func (t *Triangle) Barycoords(p *Point3D) (*Vec2, bool) {
	var _arg0 *C.graphene_triangle_t // out
	var _arg1 *C.graphene_point3d_t  // out
	var _arg2 C.graphene_vec2_t      // in
	var _cret C._Bool                // in

	_arg0 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(t)))
	if p != nil {
		_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(p)))
	}

	_cret = C.graphene_triangle_get_barycoords(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(t)
	runtime.KeepAlive(p)

	var _res *Vec2 // out
	var _ok bool   // out

	_res = (*Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))
	if _cret {
		_ok = true
	}

	return _res, _ok
}

// BoundingBox computes the bounding box of the given #graphene_triangle_t.
//
// The function returns the following values:
//
//   - res: return location for the box.
func (t *Triangle) BoundingBox() *Box {
	var _arg0 *C.graphene_triangle_t // out
	var _arg1 C.graphene_box_t       // in

	_arg0 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(t)))

	C.graphene_triangle_get_bounding_box(_arg0, &_arg1)
	runtime.KeepAlive(t)

	var _res *Box // out

	_res = (*Box)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Midpoint computes the coordinates of the midpoint of the given
// #graphene_triangle_t.
//
// The midpoint G is the centroid
// (https://en.wikipedia.org/wiki/Centroid#Triangle_centroid) of the triangle,
// i.e. the intersection of its medians.
//
// The function returns the following values:
//
//   - res: return location for the coordinates of the midpoint.
func (t *Triangle) Midpoint() *Point3D {
	var _arg0 *C.graphene_triangle_t // out
	var _arg1 C.graphene_point3d_t   // in

	_arg0 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(t)))

	C.graphene_triangle_get_midpoint(_arg0, &_arg1)
	runtime.KeepAlive(t)

	var _res *Point3D // out

	_res = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Normal computes the normal vector of the given #graphene_triangle_t.
//
// The function returns the following values:
//
//   - res: return location for the normal vector.
func (t *Triangle) Normal() *Vec3 {
	var _arg0 *C.graphene_triangle_t // out
	var _arg1 C.graphene_vec3_t      // in

	_arg0 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(t)))

	C.graphene_triangle_get_normal(_arg0, &_arg1)
	runtime.KeepAlive(t)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Plane computes the plane based on the vertices of the given
// #graphene_triangle_t.
//
// The function returns the following values:
//
//   - res: return location for the plane.
func (t *Triangle) Plane() *Plane {
	var _arg0 *C.graphene_triangle_t // out
	var _arg1 C.graphene_plane_t     // in

	_arg0 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(t)))

	C.graphene_triangle_get_plane(_arg0, &_arg1)
	runtime.KeepAlive(t)

	var _res *Plane // out

	_res = (*Plane)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Points retrieves the three vertices of the given #graphene_triangle_t and
// returns their coordinates as #graphene_point3d_t.
//
// The function returns the following values:
//
//   - a (optional): return location for the coordinates of the first vertex.
//   - b (optional): return location for the coordinates of the second vertex.
//   - c (optional): return location for the coordinates of the third vertex.
func (t *Triangle) Points() (a *Point3D, b *Point3D, c *Point3D) {
	var _arg0 *C.graphene_triangle_t // out
	var _arg1 C.graphene_point3d_t   // in
	var _arg2 C.graphene_point3d_t   // in
	var _arg3 C.graphene_point3d_t   // in

	_arg0 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(t)))

	C.graphene_triangle_get_points(_arg0, &_arg1, &_arg2, &_arg3)
	runtime.KeepAlive(t)

	var _a *Point3D // out
	var _b *Point3D // out
	var _c *Point3D // out

	_a = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))
	_b = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))
	_c = (*Point3D)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _a, _b, _c
}

// Uv computes the UV coordinates of the given point p.
//
// The point p must lie on the same plane as the triangle t; if the point is not
// coplanar, the result of this function is undefined. If p is NULL, the point
// will be set in (0, 0, 0).
//
// The UV coordinates will be placed in the res vector:
//
//   - res.x = u
//   - res.y = v
//
// See also: graphene_triangle_get_barycoords().
//
// The function takes the following parameters:
//
//   - p (optional): #graphene_point3d_t.
//   - uvA: UV coordinates of the first point.
//   - uvB: UV coordinates of the second point.
//   - uvC: UV coordinates of the third point.
//
// The function returns the following values:
//
//   - res: vector containing the UV coordinates of the given point p.
//   - ok: true if the coordinates are valid.
func (t *Triangle) Uv(p *Point3D, uvA *Vec2, uvB *Vec2, uvC *Vec2) (*Vec2, bool) {
	var _arg0 *C.graphene_triangle_t // out
	var _arg1 *C.graphene_point3d_t  // out
	var _arg2 *C.graphene_vec2_t     // out
	var _arg3 *C.graphene_vec2_t     // out
	var _arg4 *C.graphene_vec2_t     // out
	var _arg5 C.graphene_vec2_t      // in
	var _cret C._Bool                // in

	_arg0 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(t)))
	if p != nil {
		_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(p)))
	}
	_arg2 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(uvA)))
	_arg3 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(uvB)))
	_arg4 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(uvC)))

	_cret = C.graphene_triangle_get_uv(_arg0, _arg1, _arg2, _arg3, _arg4, &_arg5)
	runtime.KeepAlive(t)
	runtime.KeepAlive(p)
	runtime.KeepAlive(uvA)
	runtime.KeepAlive(uvB)
	runtime.KeepAlive(uvC)

	var _res *Vec2 // out
	var _ok bool   // out

	_res = (*Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg5))))
	if _cret {
		_ok = true
	}

	return _res, _ok
}

// Vertices retrieves the three vertices of the given #graphene_triangle_t.
//
// The function returns the following values:
//
//   - a (optional): return location for the first vertex.
//   - b (optional): return location for the second vertex.
//   - c (optional): return location for the third vertex.
func (t *Triangle) Vertices() (a *Vec3, b *Vec3, c *Vec3) {
	var _arg0 *C.graphene_triangle_t // out
	var _arg1 C.graphene_vec3_t      // in
	var _arg2 C.graphene_vec3_t      // in
	var _arg3 C.graphene_vec3_t      // in

	_arg0 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(t)))

	C.graphene_triangle_get_vertices(_arg0, &_arg1, &_arg2, &_arg3)
	runtime.KeepAlive(t)

	var _a *Vec3 // out
	var _b *Vec3 // out
	var _c *Vec3 // out

	_a = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))
	_b = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))
	_c = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _a, _b, _c
}

// InitFromFloat initializes a #graphene_triangle_t using the three given arrays
// of floating point values, each representing the coordinates of a point in 3D
// space.
//
// The function takes the following parameters:
//
//   - a: array of 3 floating point values.
//   - b: array of 3 floating point values.
//   - c: array of 3 floating point values.
//
// The function returns the following values:
//
//   - triangle: initialized #graphene_triangle_t.
func (t *Triangle) InitFromFloat(a [3]float32, b [3]float32, c [3]float32) *Triangle {
	var _arg0 *C.graphene_triangle_t // out
	var _arg1 *C.float               // out
	var _arg2 *C.float               // out
	var _arg3 *C.float               // out
	var _cret *C.graphene_triangle_t // in

	_arg0 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(t)))
	_arg1 = (*C.float)(unsafe.Pointer(&a))
	_arg2 = (*C.float)(unsafe.Pointer(&b))
	_arg3 = (*C.float)(unsafe.Pointer(&c))

	_cret = C.graphene_triangle_init_from_float(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(t)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)
	runtime.KeepAlive(c)

	var _triangle *Triangle // out

	_triangle = (*Triangle)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _triangle
}

// InitFromPoint3D initializes a #graphene_triangle_t using the three given 3D
// points.
//
// The function takes the following parameters:
//
//   - a (optional): #graphene_point3d_t.
//   - b (optional): #graphene_point3d_t.
//   - c (optional): #graphene_point3d_t.
//
// The function returns the following values:
//
//   - triangle: initialized #graphene_triangle_t.
func (t *Triangle) InitFromPoint3D(a *Point3D, b *Point3D, c *Point3D) *Triangle {
	var _arg0 *C.graphene_triangle_t // out
	var _arg1 *C.graphene_point3d_t  // out
	var _arg2 *C.graphene_point3d_t  // out
	var _arg3 *C.graphene_point3d_t  // out
	var _cret *C.graphene_triangle_t // in

	_arg0 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(t)))
	if a != nil {
		_arg1 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(a)))
	}
	if b != nil {
		_arg2 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(b)))
	}
	if c != nil {
		_arg3 = (*C.graphene_point3d_t)(gextras.StructNative(unsafe.Pointer(c)))
	}

	_cret = C.graphene_triangle_init_from_point3d(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(t)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)
	runtime.KeepAlive(c)

	var _triangle *Triangle // out

	_triangle = (*Triangle)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _triangle
}

// InitFromVec3 initializes a #graphene_triangle_t using the three given
// vectors.
//
// The function takes the following parameters:
//
//   - a (optional): #graphene_vec3_t.
//   - b (optional): #graphene_vec3_t.
//   - c (optional): #graphene_vec3_t.
//
// The function returns the following values:
//
//   - triangle: initialized #graphene_triangle_t.
func (t *Triangle) InitFromVec3(a *Vec3, b *Vec3, c *Vec3) *Triangle {
	var _arg0 *C.graphene_triangle_t // out
	var _arg1 *C.graphene_vec3_t     // out
	var _arg2 *C.graphene_vec3_t     // out
	var _arg3 *C.graphene_vec3_t     // out
	var _cret *C.graphene_triangle_t // in

	_arg0 = (*C.graphene_triangle_t)(gextras.StructNative(unsafe.Pointer(t)))
	if a != nil {
		_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(a)))
	}
	if b != nil {
		_arg2 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(b)))
	}
	if c != nil {
		_arg3 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(c)))
	}

	_cret = C.graphene_triangle_init_from_vec3(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(t)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)
	runtime.KeepAlive(c)

	var _triangle *Triangle // out

	_triangle = (*Triangle)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _triangle
}

// Vec2: structure capable of holding a vector with two dimensions, x and y.
//
// The contents of the #graphene_vec2_t structure are private and should never
// be accessed directly.
//
// An instance of this type is always passed by reference.
type Vec2 struct {
	*vec2
}

// vec2 is the struct that's finalized.
type vec2 struct {
	native *C.graphene_vec2_t
}

func marshalVec2(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Vec2{&vec2{(*C.graphene_vec2_t)(b)}}, nil
}

// NewVec2Alloc constructs a struct Vec2.
func NewVec2Alloc() *Vec2 {
	var _cret *C.graphene_vec2_t // in

	_cret = C.graphene_vec2_alloc()

	var _vec2 *Vec2 // out

	_vec2 = (*Vec2)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_vec2)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_vec2_free((*C.graphene_vec2_t)(intern.C))
		},
	)

	return _vec2
}

// Add adds each component of the two passed vectors and places each result into
// the components of res.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec2_t.
//
// The function returns the following values:
//
//   - res: return location for the result.
func (a *Vec2) Add(b *Vec2) *Vec2 {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 *C.graphene_vec2_t // out
	var _arg2 C.graphene_vec2_t  // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec2_add(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec2 // out

	_res = (*Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Divide divides each component of the first operand a by the corresponding
// component of the second operand b, and places the results into the vector
// res.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec2_t.
//
// The function returns the following values:
//
//   - res: return location for the result.
func (a *Vec2) Divide(b *Vec2) *Vec2 {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 *C.graphene_vec2_t // out
	var _arg2 C.graphene_vec2_t  // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec2_divide(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec2 // out

	_res = (*Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Dot computes the dot product of the two given vectors.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec2_t.
//
// The function returns the following values:
//
//   - gfloat: dot product of the vectors.
func (a *Vec2) Dot(b *Vec2) float32 {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 *C.graphene_vec2_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_vec2_dot(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Equal checks whether the two given #graphene_vec2_t are equal.
//
// The function takes the following parameters:
//
//   - v2: #graphene_vec2_t.
//
// The function returns the following values:
//
//   - ok: true if the two vectors are equal, and false otherwise.
func (v1 *Vec2) Equal(v2 *Vec2) bool {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 *C.graphene_vec2_t // out
	var _cret C._Bool            // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v1)))
	_arg1 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v2)))

	_cret = C.graphene_vec2_equal(_arg0, _arg1)
	runtime.KeepAlive(v1)
	runtime.KeepAlive(v2)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// X retrieves the X component of the #graphene_vec2_t.
//
// The function returns the following values:
//
//   - gfloat: value of the X component.
func (v *Vec2) X() float32 {
	var _arg0 *C.graphene_vec2_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v)))

	_cret = C.graphene_vec2_get_x(_arg0)
	runtime.KeepAlive(v)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Y retrieves the Y component of the #graphene_vec2_t.
//
// The function returns the following values:
//
//   - gfloat: value of the Y component.
func (v *Vec2) Y() float32 {
	var _arg0 *C.graphene_vec2_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v)))

	_cret = C.graphene_vec2_get_y(_arg0)
	runtime.KeepAlive(v)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Init initializes a #graphene_vec2_t using the given values.
//
// This function can be called multiple times.
//
// The function takes the following parameters:
//
//   - x: x field of the vector.
//   - y: y field of the vector.
//
// The function returns the following values:
//
//   - vec2: initialized vector.
func (v *Vec2) Init(x float32, y float32) *Vec2 {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 C.float            // out
	var _arg2 C.float            // out
	var _cret *C.graphene_vec2_t // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v)))
	_arg1 = C.float(x)
	_arg2 = C.float(y)

	_cret = C.graphene_vec2_init(_arg0, _arg1, _arg2)
	runtime.KeepAlive(v)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)

	var _vec2 *Vec2 // out

	_vec2 = (*Vec2)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec2
}

// InitFromFloat initializes v with the contents of the given array.
//
// The function takes the following parameters:
//
//   - src: array of floating point values with at least two elements.
//
// The function returns the following values:
//
//   - vec2: initialized vector.
func (v *Vec2) InitFromFloat(src [2]float32) *Vec2 {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 *C.float           // out
	var _cret *C.graphene_vec2_t // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v)))
	_arg1 = (*C.float)(unsafe.Pointer(&src))

	_cret = C.graphene_vec2_init_from_float(_arg0, _arg1)
	runtime.KeepAlive(v)
	runtime.KeepAlive(src)

	var _vec2 *Vec2 // out

	_vec2 = (*Vec2)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec2
}

// InitFromVec2 copies the contents of src into v.
//
// The function takes the following parameters:
//
//   - src: #graphene_vec2_t.
//
// The function returns the following values:
//
//   - vec2: initialized vector.
func (v *Vec2) InitFromVec2(src *Vec2) *Vec2 {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 *C.graphene_vec2_t // out
	var _cret *C.graphene_vec2_t // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v)))
	_arg1 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_vec2_init_from_vec2(_arg0, _arg1)
	runtime.KeepAlive(v)
	runtime.KeepAlive(src)

	var _vec2 *Vec2 // out

	_vec2 = (*Vec2)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec2
}

// Interpolate: linearly interpolates v1 and v2 using the given factor.
//
// The function takes the following parameters:
//
//   - v2: #graphene_vec2_t.
//   - factor: interpolation factor.
//
// The function returns the following values:
//
//   - res: interpolated vector.
func (v1 *Vec2) Interpolate(v2 *Vec2, factor float64) *Vec2 {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 *C.graphene_vec2_t // out
	var _arg2 C.double           // out
	var _arg3 C.graphene_vec2_t  // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v1)))
	_arg1 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v2)))
	_arg2 = C.double(factor)

	C.graphene_vec2_interpolate(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(v1)
	runtime.KeepAlive(v2)
	runtime.KeepAlive(factor)

	var _res *Vec2 // out

	_res = (*Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _res
}

// Length computes the length of the given vector.
//
// The function returns the following values:
//
//   - gfloat: length of the vector.
func (v *Vec2) Length() float32 {
	var _arg0 *C.graphene_vec2_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v)))

	_cret = C.graphene_vec2_length(_arg0)
	runtime.KeepAlive(v)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Max compares the two given vectors and places the maximum values of each
// component into res.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec2_t.
//
// The function returns the following values:
//
//   - res: resulting vector.
func (a *Vec2) Max(b *Vec2) *Vec2 {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 *C.graphene_vec2_t // out
	var _arg2 C.graphene_vec2_t  // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec2_max(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec2 // out

	_res = (*Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Min compares the two given vectors and places the minimum values of each
// component into res.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec2_t.
//
// The function returns the following values:
//
//   - res: resulting vector.
func (a *Vec2) Min(b *Vec2) *Vec2 {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 *C.graphene_vec2_t // out
	var _arg2 C.graphene_vec2_t  // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec2_min(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec2 // out

	_res = (*Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Multiply multiplies each component of the two passed vectors and places each
// result into the components of res.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec2_t.
//
// The function returns the following values:
//
//   - res: return location for the result.
func (a *Vec2) Multiply(b *Vec2) *Vec2 {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 *C.graphene_vec2_t // out
	var _arg2 C.graphene_vec2_t  // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec2_multiply(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec2 // out

	_res = (*Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Near compares the two given #graphene_vec2_t vectors and checks whether their
// values are within the given epsilon.
//
// The function takes the following parameters:
//
//   - v2: #graphene_vec2_t.
//   - epsilon: threshold between the two vectors.
//
// The function returns the following values:
//
//   - ok: true if the two vectors are near each other.
func (v1 *Vec2) Near(v2 *Vec2, epsilon float32) bool {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 *C.graphene_vec2_t // out
	var _arg2 C.float            // out
	var _cret C._Bool            // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v1)))
	_arg1 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v2)))
	_arg2 = C.float(epsilon)

	_cret = C.graphene_vec2_near(_arg0, _arg1, _arg2)
	runtime.KeepAlive(v1)
	runtime.KeepAlive(v2)
	runtime.KeepAlive(epsilon)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Negate negates the given #graphene_vec2_t.
//
// The function returns the following values:
//
//   - res: return location for the result vector.
func (v *Vec2) Negate() *Vec2 {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 C.graphene_vec2_t  // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_vec2_negate(_arg0, &_arg1)
	runtime.KeepAlive(v)

	var _res *Vec2 // out

	_res = (*Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Normalize computes the normalized vector for the given vector v.
//
// The function returns the following values:
//
//   - res: return location for the normalized vector.
func (v *Vec2) Normalize() *Vec2 {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 C.graphene_vec2_t  // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_vec2_normalize(_arg0, &_arg1)
	runtime.KeepAlive(v)

	var _res *Vec2 // out

	_res = (*Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Scale multiplies all components of the given vector with the given scalar
// factor.
//
// The function takes the following parameters:
//
//   - factor: scalar factor.
//
// The function returns the following values:
//
//   - res: return location for the result vector.
func (v *Vec2) Scale(factor float32) *Vec2 {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 C.float            // out
	var _arg2 C.graphene_vec2_t  // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v)))
	_arg1 = C.float(factor)

	C.graphene_vec2_scale(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(v)
	runtime.KeepAlive(factor)

	var _res *Vec2 // out

	_res = (*Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Subtract subtracts from each component of the first operand a the
// corresponding component of the second operand b and places each result into
// the components of res.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec2_t.
//
// The function returns the following values:
//
//   - res: return location for the result.
func (a *Vec2) Subtract(b *Vec2) *Vec2 {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 *C.graphene_vec2_t // out
	var _arg2 C.graphene_vec2_t  // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec2_subtract(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec2 // out

	_res = (*Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// ToFloat stores the components of v into an array.
//
// The function returns the following values:
//
//   - dest: return location for an array of floating point values with at least
//     2 elements.
func (v *Vec2) ToFloat() [2]float32 {
	var _arg0 *C.graphene_vec2_t // out
	var _arg1 [2]C.float         // in

	_arg0 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_vec2_to_float(_arg0, &_arg1[0])
	runtime.KeepAlive(v)

	var _dest [2]float32 // out

	_dest = *(*[2]float32)(unsafe.Pointer(&_arg1))

	return _dest
}

// Vec2One retrieves a constant vector with (1, 1) components.
//
// The function returns the following values:
//
//   - vec2: one vector.
func Vec2One() *Vec2 {
	var _cret *C.graphene_vec2_t // in

	_cret = C.graphene_vec2_one()

	var _vec2 *Vec2 // out

	_vec2 = (*Vec2)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec2
}

// Vec2XAxis retrieves a constant vector with (1, 0) components.
//
// The function returns the following values:
//
//   - vec2: x axis vector.
func Vec2XAxis() *Vec2 {
	var _cret *C.graphene_vec2_t // in

	_cret = C.graphene_vec2_x_axis()

	var _vec2 *Vec2 // out

	_vec2 = (*Vec2)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec2
}

// Vec2YAxis retrieves a constant vector with (0, 1) components.
//
// The function returns the following values:
//
//   - vec2: y axis vector.
func Vec2YAxis() *Vec2 {
	var _cret *C.graphene_vec2_t // in

	_cret = C.graphene_vec2_y_axis()

	var _vec2 *Vec2 // out

	_vec2 = (*Vec2)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec2
}

// Vec2Zero retrieves a constant vector with (0, 0) components.
//
// The function returns the following values:
//
//   - vec2: zero vector.
func Vec2Zero() *Vec2 {
	var _cret *C.graphene_vec2_t // in

	_cret = C.graphene_vec2_zero()

	var _vec2 *Vec2 // out

	_vec2 = (*Vec2)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec2
}

// Vec3: structure capable of holding a vector with three dimensions: x, y,
// and z.
//
// The contents of the #graphene_vec3_t structure are private and should never
// be accessed directly.
//
// An instance of this type is always passed by reference.
type Vec3 struct {
	*vec3
}

// vec3 is the struct that's finalized.
type vec3 struct {
	native *C.graphene_vec3_t
}

func marshalVec3(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Vec3{&vec3{(*C.graphene_vec3_t)(b)}}, nil
}

// NewVec3Alloc constructs a struct Vec3.
func NewVec3Alloc() *Vec3 {
	var _cret *C.graphene_vec3_t // in

	_cret = C.graphene_vec3_alloc()

	var _vec3 *Vec3 // out

	_vec3 = (*Vec3)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_vec3)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_vec3_free((*C.graphene_vec3_t)(intern.C))
		},
	)

	return _vec3
}

// Add adds each component of the two given vectors.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec3_t.
//
// The function returns the following values:
//
//   - res: return location for the resulting vector.
func (a *Vec3) Add(b *Vec3) *Vec3 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 *C.graphene_vec3_t // out
	var _arg2 C.graphene_vec3_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec3_add(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Cross computes the cross product of the two given vectors.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec3_t.
//
// The function returns the following values:
//
//   - res: return location for the resulting vector.
func (a *Vec3) Cross(b *Vec3) *Vec3 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 *C.graphene_vec3_t // out
	var _arg2 C.graphene_vec3_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec3_cross(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Divide divides each component of the first operand a by the corresponding
// component of the second operand b, and places the results into the vector
// res.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec3_t.
//
// The function returns the following values:
//
//   - res: return location for the resulting vector.
func (a *Vec3) Divide(b *Vec3) *Vec3 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 *C.graphene_vec3_t // out
	var _arg2 C.graphene_vec3_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec3_divide(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Dot computes the dot product of the two given vectors.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec3_t.
//
// The function returns the following values:
//
//   - gfloat: value of the dot product.
func (a *Vec3) Dot(b *Vec3) float32 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 *C.graphene_vec3_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_vec3_dot(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Equal checks whether the two given #graphene_vec3_t are equal.
//
// The function takes the following parameters:
//
//   - v2: #graphene_vec3_t.
//
// The function returns the following values:
//
//   - ok: true if the two vectors are equal, and false otherwise.
func (v1 *Vec3) Equal(v2 *Vec3) bool {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 *C.graphene_vec3_t // out
	var _cret C._Bool            // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v1)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v2)))

	_cret = C.graphene_vec3_equal(_arg0, _arg1)
	runtime.KeepAlive(v1)
	runtime.KeepAlive(v2)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// X retrieves the first component of the given vector v.
//
// The function returns the following values:
//
//   - gfloat: value of the first component of the vector.
func (v *Vec3) X() float32 {
	var _arg0 *C.graphene_vec3_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))

	_cret = C.graphene_vec3_get_x(_arg0)
	runtime.KeepAlive(v)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// XY creates a #graphene_vec2_t that contains the first and second components
// of the given #graphene_vec3_t.
//
// The function returns the following values:
//
//   - res: return location for a #graphene_vec2_t.
func (v *Vec3) XY() *Vec2 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 C.graphene_vec2_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_vec3_get_xy(_arg0, &_arg1)
	runtime.KeepAlive(v)

	var _res *Vec2 // out

	_res = (*Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// XY0 creates a #graphene_vec3_t that contains the first two components of the
// given #graphene_vec3_t, and the third component set to 0.
//
// The function returns the following values:
//
//   - res: return location for a #graphene_vec3_t.
func (v *Vec3) XY0() *Vec3 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 C.graphene_vec3_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_vec3_get_xy0(_arg0, &_arg1)
	runtime.KeepAlive(v)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// XYZ0 converts a #graphene_vec3_t in a #graphene_vec4_t using 0.0 as the value
// for the fourth component of the resulting vector.
//
// The function returns the following values:
//
//   - res: return location for the vector.
func (v *Vec3) XYZ0() *Vec4 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 C.graphene_vec4_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_vec3_get_xyz0(_arg0, &_arg1)
	runtime.KeepAlive(v)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// XYZ1 converts a #graphene_vec3_t in a #graphene_vec4_t using 1.0 as the value
// for the fourth component of the resulting vector.
//
// The function returns the following values:
//
//   - res: return location for the vector.
func (v *Vec3) XYZ1() *Vec4 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 C.graphene_vec4_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_vec3_get_xyz1(_arg0, &_arg1)
	runtime.KeepAlive(v)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Xyzw converts a #graphene_vec3_t in a #graphene_vec4_t using w as the value
// of the fourth component of the resulting vector.
//
// The function takes the following parameters:
//
//   - w: value of the W component.
//
// The function returns the following values:
//
//   - res: return location for the vector.
func (v *Vec3) Xyzw(w float32) *Vec4 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 C.float            // out
	var _arg2 C.graphene_vec4_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))
	_arg1 = C.float(w)

	C.graphene_vec3_get_xyzw(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(v)
	runtime.KeepAlive(w)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Y retrieves the second component of the given vector v.
//
// The function returns the following values:
//
//   - gfloat: value of the second component of the vector.
func (v *Vec3) Y() float32 {
	var _arg0 *C.graphene_vec3_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))

	_cret = C.graphene_vec3_get_y(_arg0)
	runtime.KeepAlive(v)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Z retrieves the third component of the given vector v.
//
// The function returns the following values:
//
//   - gfloat: value of the third component of the vector.
func (v *Vec3) Z() float32 {
	var _arg0 *C.graphene_vec3_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))

	_cret = C.graphene_vec3_get_z(_arg0)
	runtime.KeepAlive(v)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Init initializes a #graphene_vec3_t using the given values.
//
// This function can be called multiple times.
//
// The function takes the following parameters:
//
//   - x: x field of the vector.
//   - y: y field of the vector.
//   - z: z field of the vector.
//
// The function returns the following values:
//
//   - vec3: pointer to the initialized vector.
func (v *Vec3) Init(x float32, y float32, z float32) *Vec3 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 C.float            // out
	var _arg2 C.float            // out
	var _arg3 C.float            // out
	var _cret *C.graphene_vec3_t // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))
	_arg1 = C.float(x)
	_arg2 = C.float(y)
	_arg3 = C.float(z)

	_cret = C.graphene_vec3_init(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(v)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
	runtime.KeepAlive(z)

	var _vec3 *Vec3 // out

	_vec3 = (*Vec3)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec3
}

// InitFromFloat initializes a #graphene_vec3_t with the values from an array.
//
// The function takes the following parameters:
//
//   - src: array of 3 floating point values.
//
// The function returns the following values:
//
//   - vec3: initialized vector.
func (v *Vec3) InitFromFloat(src [3]float32) *Vec3 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 *C.float           // out
	var _cret *C.graphene_vec3_t // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))
	_arg1 = (*C.float)(unsafe.Pointer(&src))

	_cret = C.graphene_vec3_init_from_float(_arg0, _arg1)
	runtime.KeepAlive(v)
	runtime.KeepAlive(src)

	var _vec3 *Vec3 // out

	_vec3 = (*Vec3)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec3
}

// InitFromVec3 initializes a #graphene_vec3_t with the values of another
// #graphene_vec3_t.
//
// The function takes the following parameters:
//
//   - src: #graphene_vec3_t.
//
// The function returns the following values:
//
//   - vec3: initialized vector.
func (v *Vec3) InitFromVec3(src *Vec3) *Vec3 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 *C.graphene_vec3_t // out
	var _cret *C.graphene_vec3_t // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_vec3_init_from_vec3(_arg0, _arg1)
	runtime.KeepAlive(v)
	runtime.KeepAlive(src)

	var _vec3 *Vec3 // out

	_vec3 = (*Vec3)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec3
}

// Interpolate: linearly interpolates v1 and v2 using the given factor.
//
// The function takes the following parameters:
//
//   - v2: #graphene_vec3_t.
//   - factor: interpolation factor.
//
// The function returns the following values:
//
//   - res: interpolated vector.
func (v1 *Vec3) Interpolate(v2 *Vec3, factor float64) *Vec3 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 *C.graphene_vec3_t // out
	var _arg2 C.double           // out
	var _arg3 C.graphene_vec3_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v1)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v2)))
	_arg2 = C.double(factor)

	C.graphene_vec3_interpolate(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(v1)
	runtime.KeepAlive(v2)
	runtime.KeepAlive(factor)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _res
}

// Length retrieves the length of the given vector v.
//
// The function returns the following values:
//
//   - gfloat: value of the length of the vector.
func (v *Vec3) Length() float32 {
	var _arg0 *C.graphene_vec3_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))

	_cret = C.graphene_vec3_length(_arg0)
	runtime.KeepAlive(v)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Max compares each component of the two given vectors and creates a vector
// that contains the maximum values.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec3_t.
//
// The function returns the following values:
//
//   - res: return location for the result vector.
func (a *Vec3) Max(b *Vec3) *Vec3 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 *C.graphene_vec3_t // out
	var _arg2 C.graphene_vec3_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec3_max(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Min compares each component of the two given vectors and creates a vector
// that contains the minimum values.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec3_t.
//
// The function returns the following values:
//
//   - res: return location for the result vector.
func (a *Vec3) Min(b *Vec3) *Vec3 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 *C.graphene_vec3_t // out
	var _arg2 C.graphene_vec3_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec3_min(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Multiply multiplies each component of the two given vectors.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec3_t.
//
// The function returns the following values:
//
//   - res: return location for the resulting vector.
func (a *Vec3) Multiply(b *Vec3) *Vec3 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 *C.graphene_vec3_t // out
	var _arg2 C.graphene_vec3_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec3_multiply(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Near compares the two given #graphene_vec3_t vectors and checks whether their
// values are within the given epsilon.
//
// The function takes the following parameters:
//
//   - v2: #graphene_vec3_t.
//   - epsilon: threshold between the two vectors.
//
// The function returns the following values:
//
//   - ok: true if the two vectors are near each other.
func (v1 *Vec3) Near(v2 *Vec3, epsilon float32) bool {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 *C.graphene_vec3_t // out
	var _arg2 C.float            // out
	var _cret C._Bool            // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v1)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v2)))
	_arg2 = C.float(epsilon)

	_cret = C.graphene_vec3_near(_arg0, _arg1, _arg2)
	runtime.KeepAlive(v1)
	runtime.KeepAlive(v2)
	runtime.KeepAlive(epsilon)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Negate negates the given #graphene_vec3_t.
//
// The function returns the following values:
//
//   - res: return location for the result vector.
func (v *Vec3) Negate() *Vec3 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 C.graphene_vec3_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_vec3_negate(_arg0, &_arg1)
	runtime.KeepAlive(v)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Normalize normalizes the given #graphene_vec3_t.
//
// The function returns the following values:
//
//   - res: return location for the normalized vector.
func (v *Vec3) Normalize() *Vec3 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 C.graphene_vec3_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_vec3_normalize(_arg0, &_arg1)
	runtime.KeepAlive(v)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Scale multiplies all components of the given vector with the given scalar
// factor.
//
// The function takes the following parameters:
//
//   - factor: scalar factor.
//
// The function returns the following values:
//
//   - res: return location for the result vector.
func (v *Vec3) Scale(factor float32) *Vec3 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 C.float            // out
	var _arg2 C.graphene_vec3_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))
	_arg1 = C.float(factor)

	C.graphene_vec3_scale(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(v)
	runtime.KeepAlive(factor)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Subtract subtracts from each component of the first operand a the
// corresponding component of the second operand b and places each result into
// the components of res.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec3_t.
//
// The function returns the following values:
//
//   - res: return location for the resulting vector.
func (a *Vec3) Subtract(b *Vec3) *Vec3 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 *C.graphene_vec3_t // out
	var _arg2 C.graphene_vec3_t  // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec3_subtract(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// ToFloat copies the components of a #graphene_vec3_t into the given array.
//
// The function returns the following values:
//
//   - dest: return location for an array of floating point values.
func (v *Vec3) ToFloat() [3]float32 {
	var _arg0 *C.graphene_vec3_t // out
	var _arg1 [3]C.float         // in

	_arg0 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_vec3_to_float(_arg0, &_arg1[0])
	runtime.KeepAlive(v)

	var _dest [3]float32 // out

	_dest = *(*[3]float32)(unsafe.Pointer(&_arg1))

	return _dest
}

// Vec3One provides a constant pointer to a vector with three components,
// all sets to 1.
//
// The function returns the following values:
//
//   - vec3: constant vector.
func Vec3One() *Vec3 {
	var _cret *C.graphene_vec3_t // in

	_cret = C.graphene_vec3_one()

	var _vec3 *Vec3 // out

	_vec3 = (*Vec3)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec3
}

// Vec3XAxis provides a constant pointer to a vector with three components with
// values set to (1, 0, 0).
//
// The function returns the following values:
//
//   - vec3: constant vector.
func Vec3XAxis() *Vec3 {
	var _cret *C.graphene_vec3_t // in

	_cret = C.graphene_vec3_x_axis()

	var _vec3 *Vec3 // out

	_vec3 = (*Vec3)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec3
}

// Vec3YAxis provides a constant pointer to a vector with three components with
// values set to (0, 1, 0).
//
// The function returns the following values:
//
//   - vec3: constant vector.
func Vec3YAxis() *Vec3 {
	var _cret *C.graphene_vec3_t // in

	_cret = C.graphene_vec3_y_axis()

	var _vec3 *Vec3 // out

	_vec3 = (*Vec3)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec3
}

// Vec3ZAxis provides a constant pointer to a vector with three components with
// values set to (0, 0, 1).
//
// The function returns the following values:
//
//   - vec3: constant vector.
func Vec3ZAxis() *Vec3 {
	var _cret *C.graphene_vec3_t // in

	_cret = C.graphene_vec3_z_axis()

	var _vec3 *Vec3 // out

	_vec3 = (*Vec3)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec3
}

// Vec3Zero provides a constant pointer to a vector with three components,
// all sets to 0.
//
// The function returns the following values:
//
//   - vec3: constant vector.
func Vec3Zero() *Vec3 {
	var _cret *C.graphene_vec3_t // in

	_cret = C.graphene_vec3_zero()

	var _vec3 *Vec3 // out

	_vec3 = (*Vec3)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec3
}

// Vec4: structure capable of holding a vector with four dimensions: x, y, z,
// and w.
//
// The contents of the #graphene_vec4_t structure are private and should never
// be accessed directly.
//
// An instance of this type is always passed by reference.
type Vec4 struct {
	*vec4
}

// vec4 is the struct that's finalized.
type vec4 struct {
	native *C.graphene_vec4_t
}

func marshalVec4(p uintptr) (interface{}, error) {
	b := coreglib.ValueFromNative(unsafe.Pointer(p)).Boxed()
	return &Vec4{&vec4{(*C.graphene_vec4_t)(b)}}, nil
}

// NewVec4Alloc constructs a struct Vec4.
func NewVec4Alloc() *Vec4 {
	var _cret *C.graphene_vec4_t // in

	_cret = C.graphene_vec4_alloc()

	var _vec4 *Vec4 // out

	_vec4 = (*Vec4)(gextras.NewStructNative(unsafe.Pointer(_cret)))
	runtime.SetFinalizer(
		gextras.StructIntern(unsafe.Pointer(_vec4)),
		func(intern *struct{ C unsafe.Pointer }) {
			C.graphene_vec4_free((*C.graphene_vec4_t)(intern.C))
		},
	)

	return _vec4
}

// Add adds each component of the two given vectors.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec4_t.
//
// The function returns the following values:
//
//   - res: return location for the resulting vector.
func (a *Vec4) Add(b *Vec4) *Vec4 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 *C.graphene_vec4_t // out
	var _arg2 C.graphene_vec4_t  // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec4_add(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Divide divides each component of the first operand a by the corresponding
// component of the second operand b, and places the results into the vector
// res.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec4_t.
//
// The function returns the following values:
//
//   - res: return location for the resulting vector.
func (a *Vec4) Divide(b *Vec4) *Vec4 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 *C.graphene_vec4_t // out
	var _arg2 C.graphene_vec4_t  // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec4_divide(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Dot computes the dot product of the two given vectors.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec4_t.
//
// The function returns the following values:
//
//   - gfloat: value of the dot product.
func (a *Vec4) Dot(b *Vec4) float32 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 *C.graphene_vec4_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(b)))

	_cret = C.graphene_vec4_dot(_arg0, _arg1)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Equal checks whether the two given #graphene_vec4_t are equal.
//
// The function takes the following parameters:
//
//   - v2: #graphene_vec4_t.
//
// The function returns the following values:
//
//   - ok: true if the two vectors are equal, and false otherwise.
func (v1 *Vec4) Equal(v2 *Vec4) bool {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 *C.graphene_vec4_t // out
	var _cret C._Bool            // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v1)))
	_arg1 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v2)))

	_cret = C.graphene_vec4_equal(_arg0, _arg1)
	runtime.KeepAlive(v1)
	runtime.KeepAlive(v2)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// W retrieves the value of the fourth component of the given #graphene_vec4_t.
//
// The function returns the following values:
//
//   - gfloat: value of the fourth component.
func (v *Vec4) W() float32 {
	var _arg0 *C.graphene_vec4_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))

	_cret = C.graphene_vec4_get_w(_arg0)
	runtime.KeepAlive(v)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// X retrieves the value of the first component of the given #graphene_vec4_t.
//
// The function returns the following values:
//
//   - gfloat: value of the first component.
func (v *Vec4) X() float32 {
	var _arg0 *C.graphene_vec4_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))

	_cret = C.graphene_vec4_get_x(_arg0)
	runtime.KeepAlive(v)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// XY creates a #graphene_vec2_t that contains the first two components of the
// given #graphene_vec4_t.
//
// The function returns the following values:
//
//   - res: return location for a #graphene_vec2_t.
func (v *Vec4) XY() *Vec2 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 C.graphene_vec2_t  // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_vec4_get_xy(_arg0, &_arg1)
	runtime.KeepAlive(v)

	var _res *Vec2 // out

	_res = (*Vec2)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// XYZ creates a #graphene_vec3_t that contains the first three components of
// the given #graphene_vec4_t.
//
// The function returns the following values:
//
//   - res: return location for a graphene_vec3_t.
func (v *Vec4) XYZ() *Vec3 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 C.graphene_vec3_t  // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_vec4_get_xyz(_arg0, &_arg1)
	runtime.KeepAlive(v)

	var _res *Vec3 // out

	_res = (*Vec3)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Y retrieves the value of the second component of the given #graphene_vec4_t.
//
// The function returns the following values:
//
//   - gfloat: value of the second component.
func (v *Vec4) Y() float32 {
	var _arg0 *C.graphene_vec4_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))

	_cret = C.graphene_vec4_get_y(_arg0)
	runtime.KeepAlive(v)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Z retrieves the value of the third component of the given #graphene_vec4_t.
//
// The function returns the following values:
//
//   - gfloat: value of the third component.
func (v *Vec4) Z() float32 {
	var _arg0 *C.graphene_vec4_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))

	_cret = C.graphene_vec4_get_z(_arg0)
	runtime.KeepAlive(v)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Init initializes a #graphene_vec4_t using the given values.
//
// This function can be called multiple times.
//
// The function takes the following parameters:
//
//   - x: x field of the vector.
//   - y: y field of the vector.
//   - z: z field of the vector.
//   - w: w field of the vector.
//
// The function returns the following values:
//
//   - vec4: pointer to the initialized vector.
func (v *Vec4) Init(x float32, y float32, z float32, w float32) *Vec4 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 C.float            // out
	var _arg2 C.float            // out
	var _arg3 C.float            // out
	var _arg4 C.float            // out
	var _cret *C.graphene_vec4_t // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))
	_arg1 = C.float(x)
	_arg2 = C.float(y)
	_arg3 = C.float(z)
	_arg4 = C.float(w)

	_cret = C.graphene_vec4_init(_arg0, _arg1, _arg2, _arg3, _arg4)
	runtime.KeepAlive(v)
	runtime.KeepAlive(x)
	runtime.KeepAlive(y)
	runtime.KeepAlive(z)
	runtime.KeepAlive(w)

	var _vec4 *Vec4 // out

	_vec4 = (*Vec4)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec4
}

// InitFromFloat initializes a #graphene_vec4_t with the values inside the given
// array.
//
// The function takes the following parameters:
//
//   - src: array of four floating point values.
//
// The function returns the following values:
//
//   - vec4: initialized vector.
func (v *Vec4) InitFromFloat(src [4]float32) *Vec4 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 *C.float           // out
	var _cret *C.graphene_vec4_t // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))
	_arg1 = (*C.float)(unsafe.Pointer(&src))

	_cret = C.graphene_vec4_init_from_float(_arg0, _arg1)
	runtime.KeepAlive(v)
	runtime.KeepAlive(src)

	var _vec4 *Vec4 // out

	_vec4 = (*Vec4)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec4
}

// InitFromVec2 initializes a #graphene_vec4_t using the components of a
// #graphene_vec2_t and the values of z and w.
//
// The function takes the following parameters:
//
//   - src: #graphene_vec2_t.
//   - z: value for the third component of v.
//   - w: value for the fourth component of v.
//
// The function returns the following values:
//
//   - vec4: initialized vector.
func (v *Vec4) InitFromVec2(src *Vec2, z float32, w float32) *Vec4 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 *C.graphene_vec2_t // out
	var _arg2 C.float            // out
	var _arg3 C.float            // out
	var _cret *C.graphene_vec4_t // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))
	_arg1 = (*C.graphene_vec2_t)(gextras.StructNative(unsafe.Pointer(src)))
	_arg2 = C.float(z)
	_arg3 = C.float(w)

	_cret = C.graphene_vec4_init_from_vec2(_arg0, _arg1, _arg2, _arg3)
	runtime.KeepAlive(v)
	runtime.KeepAlive(src)
	runtime.KeepAlive(z)
	runtime.KeepAlive(w)

	var _vec4 *Vec4 // out

	_vec4 = (*Vec4)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec4
}

// InitFromVec3 initializes a #graphene_vec4_t using the components of a
// #graphene_vec3_t and the value of w.
//
// The function takes the following parameters:
//
//   - src: #graphene_vec3_t.
//   - w: value for the fourth component of v.
//
// The function returns the following values:
//
//   - vec4: initialized vector.
func (v *Vec4) InitFromVec3(src *Vec3, w float32) *Vec4 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 *C.graphene_vec3_t // out
	var _arg2 C.float            // out
	var _cret *C.graphene_vec4_t // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))
	_arg1 = (*C.graphene_vec3_t)(gextras.StructNative(unsafe.Pointer(src)))
	_arg2 = C.float(w)

	_cret = C.graphene_vec4_init_from_vec3(_arg0, _arg1, _arg2)
	runtime.KeepAlive(v)
	runtime.KeepAlive(src)
	runtime.KeepAlive(w)

	var _vec4 *Vec4 // out

	_vec4 = (*Vec4)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec4
}

// InitFromVec4 initializes a #graphene_vec4_t using the components of another
// #graphene_vec4_t.
//
// The function takes the following parameters:
//
//   - src: #graphene_vec4_t.
//
// The function returns the following values:
//
//   - vec4: initialized vector.
func (v *Vec4) InitFromVec4(src *Vec4) *Vec4 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 *C.graphene_vec4_t // out
	var _cret *C.graphene_vec4_t // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))
	_arg1 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(src)))

	_cret = C.graphene_vec4_init_from_vec4(_arg0, _arg1)
	runtime.KeepAlive(v)
	runtime.KeepAlive(src)

	var _vec4 *Vec4 // out

	_vec4 = (*Vec4)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec4
}

// Interpolate: linearly interpolates v1 and v2 using the given factor.
//
// The function takes the following parameters:
//
//   - v2: #graphene_vec4_t.
//   - factor: interpolation factor.
//
// The function returns the following values:
//
//   - res: interpolated vector.
func (v1 *Vec4) Interpolate(v2 *Vec4, factor float64) *Vec4 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 *C.graphene_vec4_t // out
	var _arg2 C.double           // out
	var _arg3 C.graphene_vec4_t  // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v1)))
	_arg1 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v2)))
	_arg2 = C.double(factor)

	C.graphene_vec4_interpolate(_arg0, _arg1, _arg2, &_arg3)
	runtime.KeepAlive(v1)
	runtime.KeepAlive(v2)
	runtime.KeepAlive(factor)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg3))))

	return _res
}

// Length computes the length of the given #graphene_vec4_t.
//
// The function returns the following values:
//
//   - gfloat: length of the vector.
func (v *Vec4) Length() float32 {
	var _arg0 *C.graphene_vec4_t // out
	var _cret C.float            // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))

	_cret = C.graphene_vec4_length(_arg0)
	runtime.KeepAlive(v)

	var _gfloat float32 // out

	_gfloat = float32(_cret)

	return _gfloat
}

// Max compares each component of the two given vectors and creates a vector
// that contains the maximum values.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec4_t.
//
// The function returns the following values:
//
//   - res: return location for the result vector.
func (a *Vec4) Max(b *Vec4) *Vec4 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 *C.graphene_vec4_t // out
	var _arg2 C.graphene_vec4_t  // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec4_max(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Min compares each component of the two given vectors and creates a vector
// that contains the minimum values.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec4_t.
//
// The function returns the following values:
//
//   - res: return location for the result vector.
func (a *Vec4) Min(b *Vec4) *Vec4 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 *C.graphene_vec4_t // out
	var _arg2 C.graphene_vec4_t  // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec4_min(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Multiply multiplies each component of the two given vectors.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec4_t.
//
// The function returns the following values:
//
//   - res: return location for the resulting vector.
func (a *Vec4) Multiply(b *Vec4) *Vec4 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 *C.graphene_vec4_t // out
	var _arg2 C.graphene_vec4_t  // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec4_multiply(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Near compares the two given #graphene_vec4_t vectors and checks whether their
// values are within the given epsilon.
//
// The function takes the following parameters:
//
//   - v2: #graphene_vec4_t.
//   - epsilon: threshold between the two vectors.
//
// The function returns the following values:
//
//   - ok: true if the two vectors are near each other.
func (v1 *Vec4) Near(v2 *Vec4, epsilon float32) bool {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 *C.graphene_vec4_t // out
	var _arg2 C.float            // out
	var _cret C._Bool            // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v1)))
	_arg1 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v2)))
	_arg2 = C.float(epsilon)

	_cret = C.graphene_vec4_near(_arg0, _arg1, _arg2)
	runtime.KeepAlive(v1)
	runtime.KeepAlive(v2)
	runtime.KeepAlive(epsilon)

	var _ok bool // out

	if _cret {
		_ok = true
	}

	return _ok
}

// Negate negates the given #graphene_vec4_t.
//
// The function returns the following values:
//
//   - res: return location for the result vector.
func (v *Vec4) Negate() *Vec4 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 C.graphene_vec4_t  // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_vec4_negate(_arg0, &_arg1)
	runtime.KeepAlive(v)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Normalize normalizes the given #graphene_vec4_t.
//
// The function returns the following values:
//
//   - res: return location for the normalized vector.
func (v *Vec4) Normalize() *Vec4 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 C.graphene_vec4_t  // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_vec4_normalize(_arg0, &_arg1)
	runtime.KeepAlive(v)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg1))))

	return _res
}

// Scale multiplies all components of the given vector with the given scalar
// factor.
//
// The function takes the following parameters:
//
//   - factor: scalar factor.
//
// The function returns the following values:
//
//   - res: return location for the result vector.
func (v *Vec4) Scale(factor float32) *Vec4 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 C.float            // out
	var _arg2 C.graphene_vec4_t  // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))
	_arg1 = C.float(factor)

	C.graphene_vec4_scale(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(v)
	runtime.KeepAlive(factor)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// Subtract subtracts from each component of the first operand a the
// corresponding component of the second operand b and places each result into
// the components of res.
//
// The function takes the following parameters:
//
//   - b: #graphene_vec4_t.
//
// The function returns the following values:
//
//   - res: return location for the resulting vector.
func (a *Vec4) Subtract(b *Vec4) *Vec4 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 *C.graphene_vec4_t // out
	var _arg2 C.graphene_vec4_t  // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(a)))
	_arg1 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(b)))

	C.graphene_vec4_subtract(_arg0, _arg1, &_arg2)
	runtime.KeepAlive(a)
	runtime.KeepAlive(b)

	var _res *Vec4 // out

	_res = (*Vec4)(gextras.NewStructNative(unsafe.Pointer((&_arg2))))

	return _res
}

// ToFloat stores the components of the given #graphene_vec4_t into an array of
// floating point values.
//
// The function returns the following values:
//
//   - dest: return location for an array of floating point values.
func (v *Vec4) ToFloat() [4]float32 {
	var _arg0 *C.graphene_vec4_t // out
	var _arg1 [4]C.float         // in

	_arg0 = (*C.graphene_vec4_t)(gextras.StructNative(unsafe.Pointer(v)))

	C.graphene_vec4_to_float(_arg0, &_arg1[0])
	runtime.KeepAlive(v)

	var _dest [4]float32 // out

	_dest = *(*[4]float32)(unsafe.Pointer(&_arg1))

	return _dest
}

// Vec4One retrieves a pointer to a #graphene_vec4_t with all its components set
// to 1.
//
// The function returns the following values:
//
//   - vec4: constant vector.
func Vec4One() *Vec4 {
	var _cret *C.graphene_vec4_t // in

	_cret = C.graphene_vec4_one()

	var _vec4 *Vec4 // out

	_vec4 = (*Vec4)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec4
}

// Vec4WAxis retrieves a pointer to a #graphene_vec4_t with its components set
// to (0, 0, 0, 1).
//
// The function returns the following values:
//
//   - vec4: constant vector.
func Vec4WAxis() *Vec4 {
	var _cret *C.graphene_vec4_t // in

	_cret = C.graphene_vec4_w_axis()

	var _vec4 *Vec4 // out

	_vec4 = (*Vec4)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec4
}

// Vec4XAxis retrieves a pointer to a #graphene_vec4_t with its components set
// to (1, 0, 0, 0).
//
// The function returns the following values:
//
//   - vec4: constant vector.
func Vec4XAxis() *Vec4 {
	var _cret *C.graphene_vec4_t // in

	_cret = C.graphene_vec4_x_axis()

	var _vec4 *Vec4 // out

	_vec4 = (*Vec4)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec4
}

// Vec4YAxis retrieves a pointer to a #graphene_vec4_t with its components set
// to (0, 1, 0, 0).
//
// The function returns the following values:
//
//   - vec4: constant vector.
func Vec4YAxis() *Vec4 {
	var _cret *C.graphene_vec4_t // in

	_cret = C.graphene_vec4_y_axis()

	var _vec4 *Vec4 // out

	_vec4 = (*Vec4)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec4
}

// Vec4ZAxis retrieves a pointer to a #graphene_vec4_t with its components set
// to (0, 0, 1, 0).
//
// The function returns the following values:
//
//   - vec4: constant vector.
func Vec4ZAxis() *Vec4 {
	var _cret *C.graphene_vec4_t // in

	_cret = C.graphene_vec4_z_axis()

	var _vec4 *Vec4 // out

	_vec4 = (*Vec4)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec4
}

// Vec4Zero retrieves a pointer to a #graphene_vec4_t with all its components
// set to 0.
//
// The function returns the following values:
//
//   - vec4: constant vector.
func Vec4Zero() *Vec4 {
	var _cret *C.graphene_vec4_t // in

	_cret = C.graphene_vec4_zero()

	var _vec4 *Vec4 // out

	_vec4 = (*Vec4)(gextras.NewStructNative(unsafe.Pointer(_cret)))

	return _vec4
}
