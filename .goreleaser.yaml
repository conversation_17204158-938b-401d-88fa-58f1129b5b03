version: 2
project_name: clyp
before:
  hooks:
    - go mod tidy
builds:
  - id: clyp
    env:
      - CGO_ENABLED=1
    goos:
      - linux
    goarch:
      - amd64
archives:
  - formats: [tar.gz]
    name_template: >-
      {{ .ProjectName }}-
      {{- .Version }}-
      {{- title .Os }}-
      {{- if eq .Arch "amd64" }}x86_64
      {{- else }}{{ .Arch }}{{ end }}
release:
  include_meta: false
  extra_files:
    - glob: ./dist/*.deb
    - glob: ./dist/*.pkg.tar.zst
    - glob: ./dist/*.rpm
